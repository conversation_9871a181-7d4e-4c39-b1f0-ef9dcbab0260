#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：万科000002分钟数据历史范围
"""

import pandas as pd
import tushare as ts
import time
from datetime import datetime, timedelta

# 设置Tushare token
TUSHARE_TOKEN = "77f6a05e9b214c373a88a6503c574f13b33ebfd428dddad8fb7086c2"
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def test_historical_range(ts_code, freq):
    """测试历史数据范围"""
    print(f"\n🔍 测试 {ts_code} {freq} 历史数据范围:")
    print("-" * 50)
    
    # 测试不同年份
    test_years = [2024, 2023, 2022, 2021, 2020, 2019, 2018, 2015, 2010]
    
    earliest_success = None
    
    for year in test_years:
        start_date = f"{year}0101"
        end_date = f"{year}1231"
        
        print(f"📅 测试 {year} 年...")
        
        try:
            df = ts.pro_bar(
                ts_code=ts_code,
                asset='E',
                adj='qfq',
                freq=freq,
                start_date=start_date,
                end_date=end_date
            )
            
            if df is not None and not df.empty:
                df['trade_time'] = pd.to_datetime(df['trade_time'])
                earliest = df['trade_time'].min()
                latest = df['trade_time'].max()
                count = len(df)
                
                print(f"  ✅ {year} 年: {count} 条数据")
                print(f"  📊 范围: {earliest.strftime('%Y-%m-%d')} 到 {latest.strftime('%Y-%m-%d')}")
                
                earliest_success = earliest
            else:
                print(f"  ❌ {year} 年: 无数据")
                break
                
            time.sleep(0.5)  # 避免API限制
            
        except Exception as e:
            print(f"  ❌ {year} 年: 失败 - {e}")
            break
    
    return earliest_success

def estimate_data_volume(ts_code, freq, start_year, end_year):
    """估算数据量"""
    print(f"\n📊 估算 {ts_code} {freq} 数据量 ({start_year}-{end_year}):")
    print("-" * 50)
    
    # 测试一年的数据量
    test_year = 2023
    start_date = f"{test_year}0101"
    end_date = f"{test_year}1231"
    
    try:
        df = ts.pro_bar(
            ts_code=ts_code,
            asset='E',
            adj='qfq',
            freq=freq,
            start_date=start_date,
            end_date=end_date
        )
        
        if df is not None and not df.empty:
            yearly_count = len(df)
            total_years = end_year - start_year + 1
            estimated_total = yearly_count * total_years
            
            print(f"📈 {test_year} 年数据量: {yearly_count:,} 条")
            print(f"📊 预计总年数: {total_years} 年")
            print(f"📊 预计总数据量: {estimated_total:,} 条")
            
            # 估算存储空间 (每条记录约100字节)
            estimated_size_mb = estimated_total * 100 / 1024 / 1024
            print(f"💾 预计存储空间: {estimated_size_mb:.1f} MB")
            
            return estimated_total, estimated_size_mb
        else:
            print(f"❌ 无法获取 {test_year} 年数据进行估算")
            return 0, 0
            
    except Exception as e:
        print(f"❌ 估算失败: {e}")
        return 0, 0

def main():
    """主函数"""
    print("🚀 万科A (000002.SZ) 分钟数据历史范围最终测试")
    print("=" * 60)
    
    ts_code = "000002.SZ"
    frequencies = ["5min", "15min", "60min"]
    
    results = {}
    
    for freq in frequencies:
        print(f"\n{'='*60}")
        print(f"🔍 测试频率: {freq}")
        print(f"{'='*60}")
        
        # 测试历史范围
        earliest = test_historical_range(ts_code, freq)
        
        if earliest:
            earliest_year = earliest.year
            current_year = datetime.now().year
            
            print(f"\n🎯 {freq} 数据最早可追溯到: {earliest.strftime('%Y-%m-%d')}")
            
            # 估算数据量
            total_count, size_mb = estimate_data_volume(ts_code, freq, earliest_year, current_year)
            
            results[freq] = {
                'earliest_date': earliest,
                'earliest_year': earliest_year,
                'estimated_count': total_count,
                'estimated_size_mb': size_mb
            }
        else:
            print(f"\n❌ {freq} 数据无法获取历史数据")
            results[freq] = None
    
    # 生成最终报告
    print(f"\n{'='*60}")
    print("📊 万科A分钟数据历史范围测试结果")
    print(f"{'='*60}")
    
    total_size = 0
    
    for freq in frequencies:
        result = results[freq]
        if result:
            print(f"\n📈 {freq} 数据:")
            print(f"  ✅ 可获取历史数据")
            print(f"  📅 最早日期: {result['earliest_date'].strftime('%Y-%m-%d')}")
            print(f"  📊 覆盖年限: {datetime.now().year - result['earliest_year'] + 1} 年")
            print(f"  📊 预计数据量: {result['estimated_count']:,} 条")
            print(f"  💾 预计大小: {result['estimated_size_mb']:.1f} MB")
            
            total_size += result['estimated_size_mb']
        else:
            print(f"\n📈 {freq} 数据:")
            print(f"  ❌ 无法获取历史数据")
    
    print(f"\n💾 所有分钟数据预计总大小: {total_size:.1f} MB")
    
    # 给出最终建议
    print(f"\n💡 分钟数据下载建议:")
    print("-" * 40)
    
    available_freqs = [freq for freq in frequencies if results[freq]]
    
    if available_freqs:
        earliest_year = min(results[freq]['earliest_year'] for freq in available_freqs if results[freq])
        
        print(f"✅ 可下载的频率: {', '.join(available_freqs)}")
        print(f"📅 建议起始时间: {earliest_year}0101")
        print(f"💾 预计总存储需求: {total_size:.1f} MB")
        
        print(f"\n📋 下载策略建议:")
        print(f"1. 按年分批下载，避免单次请求过大")
        print(f"2. 从 {earliest_year} 年开始，逐年下载到当前年份")
        print(f"3. 每个API调用间隔0.3-0.5秒，避免触发限制")
        print(f"4. 优先下载60分钟数据（数据量最小）")
        print(f"5. 然后下载15分钟数据")
        print(f"6. 最后下载5分钟数据（数据量最大）")
        
        print(f"\n⚠️  注意事项:")
        print(f"1. 分钟数据使用 'trade_time' 列而不是 'trade_date'")
        print(f"2. 需要将 'vol' 列重命名为 'volume'")
        print(f"3. 时间格式需要转换为标准datetime格式")
        print(f"4. 建议先用少量股票测试程序正确性")
    else:
        print(f"❌ 无法获取任何分钟数据，可能需要更高的Tushare权限")
    
    print(f"\n{'='*60}")
    print("🎉 测试完成!")
    print("="*60)

if __name__ == "__main__":
    main()
