#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中股票日线和周线数据的完整性
"""

import sys
import os
import pandas as pd
import pymysql
from datetime import datetime, timedelta

# 添加项目源码路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from chanlun.exchange.exchange_db import ExchangeDB

def get_database_connection():
    """获取数据库连接"""
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'chanlun',
        'charset': 'utf8mb4'
    }
    return pymysql.connect(**config)

def check_database_tables():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构...")
    
    connection = get_database_connection()
    
    with connection.cursor() as cursor:
        # 查看所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"📋 数据库表总数: {len(tables)}")
        
        # 分类统计表
        kline_tables = []
        other_tables = []
        
        for table in tables:
            table_name = table[0]
            if 'klines' in table_name:
                kline_tables.append(table_name)
            else:
                other_tables.append(table_name)
        
        print(f"📈 K线数据表: {len(kline_tables)} 个")
        print(f"📊 其他表: {len(other_tables)} 个")
        
        # 显示K线表详情
        total_records = 0
        for table in kline_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"   {table}: {count:,} 条记录")
        
        print(f"📊 K线数据总记录数: {total_records:,} 条")
    
    connection.close()
    return kline_tables

def check_stock_data_completeness():
    """检查股票数据完整性"""
    print(f"\n🔍 检查股票数据完整性...")
    
    try:
        db_ex = ExchangeDB("a")
        
        # 测试几只代表性股票
        test_stocks = [
            ("SZSE.000001", "平安银行"),
            ("SZSE.000002", "万科A"),
            ("SHSE.600519", "贵州茅台"),
            ("SHSE.600000", "浦发银行"),
            ("SHSE.000001", "上证指数"),
            ("SZSE.399001", "深证成指"),
        ]
        
        print(f"📊 检查 {len(test_stocks)} 只代表性股票/指数:")
        
        for code, name in test_stocks:
            print(f"\n--- {name} ({code}) ---")
            
            # 检查日线数据
            try:
                daily_data = db_ex.klines(code, "d", args={"limit": 10000})
                if len(daily_data) > 0:
                    print(f"  ✅ 日线数据: {len(daily_data):,} 条")
                    print(f"  📅 日期范围: {daily_data['date'].min().strftime('%Y-%m-%d')} 到 {daily_data['date'].max().strftime('%Y-%m-%d')}")
                    print(f"  💰 最新收盘价: {daily_data.iloc[0]['close']:.2f}")
                    
                    # 计算数据年限
                    date_range = daily_data['date'].max() - daily_data['date'].min()
                    years = date_range.days / 365.25
                    print(f"  📈 数据年限: {years:.1f} 年")
                else:
                    print(f"  ❌ 日线数据: 无数据")
            except Exception as e:
                print(f"  ❌ 日线数据查询失败: {e}")
            
            # 检查周线数据
            try:
                weekly_data = db_ex.klines(code, "w", args={"limit": 10000})
                if len(weekly_data) > 0:
                    print(f"  ✅ 周线数据: {len(weekly_data):,} 条")
                    print(f"  📅 周线范围: {weekly_data['date'].min().strftime('%Y-%m-%d')} 到 {weekly_data['date'].max().strftime('%Y-%m-%d')}")
                else:
                    print(f"  ❌ 周线数据: 无数据")
            except Exception as e:
                print(f"  ❌ 周线数据查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性检查失败: {e}")
        return False

def check_data_quality():
    """检查数据质量"""
    print(f"\n🔍 检查数据质量...")
    
    connection = get_database_connection()
    
    try:
        with connection.cursor() as cursor:
            # 检查数据质量问题
            quality_issues = []
            
            # 查找有数据的表
            cursor.execute("SHOW TABLES LIKE '%klines%'")
            kline_tables = [table[0] for table in cursor.fetchall()]
            
            for table in kline_tables[:3]:  # 检查前3个表
                print(f"\n📊 检查表: {table}")
                
                # 检查记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                total_count = cursor.fetchone()[0]
                print(f"  📋 总记录数: {total_count:,}")
                
                if total_count > 0:
                    # 检查股票数量
                    cursor.execute(f"SELECT COUNT(DISTINCT code) FROM {table}")
                    stock_count = cursor.fetchone()[0]
                    print(f"  📈 股票数量: {stock_count}")
                    
                    # 检查日期范围
                    cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table}")
                    min_date, max_date = cursor.fetchone()
                    print(f"  📅 日期范围: {min_date} 到 {max_date}")
                    
                    # 检查异常数据
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE close <= 0 OR open <= 0")
                    zero_price_count = cursor.fetchone()[0]
                    
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE volume < 0")
                    negative_volume_count = cursor.fetchone()[0]
                    
                    if zero_price_count > 0:
                        quality_issues.append(f"{table}: {zero_price_count} 条零价格记录")
                        print(f"  ⚠️  零价格记录: {zero_price_count}")
                    else:
                        print(f"  ✅ 价格数据正常")
                    
                    if negative_volume_count > 0:
                        quality_issues.append(f"{table}: {negative_volume_count} 条负成交量记录")
                        print(f"  ⚠️  负成交量记录: {negative_volume_count}")
                    else:
                        print(f"  ✅ 成交量数据正常")
                    
                    # 检查数据密度（前10只股票）
                    cursor.execute(f"""
                        SELECT code, COUNT(*) as cnt 
                        FROM {table} 
                        GROUP BY code 
                        ORDER BY cnt DESC 
                        LIMIT 10
                    """)
                    top_stocks = cursor.fetchall()
                    
                    print(f"  📊 数据最多的10只股票:")
                    for code, cnt in top_stocks:
                        print(f"    {code}: {cnt:,} 条")
            
            if quality_issues:
                print(f"\n⚠️  发现 {len(quality_issues)} 个数据质量问题:")
                for issue in quality_issues:
                    print(f"    - {issue}")
            else:
                print(f"\n✅ 数据质量检查通过，未发现问题")
        
        return len(quality_issues) == 0
        
    except Exception as e:
        print(f"❌ 数据质量检查失败: {e}")
        return False
    finally:
        connection.close()

def generate_summary_report():
    """生成汇总报告"""
    print(f"\n📊 生成数据汇总报告...")
    
    connection = get_database_connection()
    
    try:
        with connection.cursor() as cursor:
            # 统计总体数据
            cursor.execute("SHOW TABLES LIKE '%klines%'")
            kline_tables = [table[0] for table in cursor.fetchall()]
            
            total_records = 0
            total_stocks = set()
            
            for table in kline_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
                
                cursor.execute(f"SELECT DISTINCT code FROM {table}")
                stocks = [row[0] for row in cursor.fetchall()]
                total_stocks.update(stocks)
            
            # 按交易所分类
            szse_stocks = [s for s in total_stocks if s.startswith('SZSE.')]
            shse_stocks = [s for s in total_stocks if s.startswith('SHSE.')]
            
            print(f"📈 数据汇总:")
            print(f"  📊 总K线记录数: {total_records:,} 条")
            print(f"  📊 总股票/指数数: {len(total_stocks)} 只")
            print(f"  📊 深交所股票: {len(szse_stocks)} 只")
            print(f"  📊 上交所股票: {len(shse_stocks)} 只")
            print(f"  📊 平均每只股票: {total_records/len(total_stocks):.0f} 条记录")
            
            # 估算数据大小
            estimated_size_mb = total_records * 100 / 1024 / 1024  # 假设每条记录约100字节
            print(f"  💾 估算数据大小: {estimated_size_mb:.1f} MB")
            
        return {
            'total_records': total_records,
            'total_stocks': len(total_stocks),
            'szse_stocks': len(szse_stocks),
            'shse_stocks': len(shse_stocks)
        }
        
    except Exception as e:
        print(f"❌ 汇总报告生成失败: {e}")
        return None
    finally:
        connection.close()

def main():
    """主函数"""
    print("🚀 Tushare股票数据完整性检查")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 1. 检查数据库表结构
    print("1️⃣ 检查数据库表结构")
    try:
        kline_tables = check_database_tables()
        if kline_tables:
            success_count += 1
            print("✅ 数据库表结构检查通过")
        else:
            print("❌ 数据库表结构检查失败")
    except Exception as e:
        print(f"❌ 数据库表结构检查失败: {e}")
    
    # 2. 检查股票数据完整性
    print(f"\n2️⃣ 检查股票数据完整性")
    if check_stock_data_completeness():
        success_count += 1
        print("✅ 股票数据完整性检查通过")
    else:
        print("❌ 股票数据完整性检查失败")
    
    # 3. 检查数据质量
    print(f"\n3️⃣ 检查数据质量")
    if check_data_quality():
        success_count += 1
        print("✅ 数据质量检查通过")
    else:
        print("❌ 数据质量检查失败")
    
    # 4. 生成汇总报告
    print(f"\n4️⃣ 生成汇总报告")
    summary = generate_summary_report()
    if summary:
        success_count += 1
        print("✅ 汇总报告生成成功")
    else:
        print("❌ 汇总报告生成失败")
    
    # 最终结果
    print(f"\n{'='*60}")
    print(f"🎉 检查完成!")
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests-success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎊 所有检查通过！数据库数据完整且质量良好！")
        print("💡 现在可以:")
        print("   1. 启动Web界面查看股票数据")
        print("   2. 运行缠论分析程序")
        print("   3. 进行技术分析和策略回测")
    else:
        print("⚠️  部分检查失败，请检查数据导入过程")
    
    print("="*60)

if __name__ == "__main__":
    main()
