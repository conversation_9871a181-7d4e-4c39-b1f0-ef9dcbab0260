#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确回测引擎
严格模拟实盘交易过程的回测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
import time
warnings.filterwarnings('ignore')

# 导入自定义模块
import sys
import os
# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录
sys.path.append(project_root)

from limit_up_reversal_strategy import LimitUpReversalStrategy, TradeRecord, Position, StockCandidate
from chanlun_analyzer import ChanLunAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class PreciseBacktestEngine:
    """精确回测引擎 - 严格模拟实盘交易过程"""

    def __init__(self, strategy: LimitUpReversalStrategy):
        self.strategy = strategy
        self.chanlun_analyzer = ChanLunAnalyzer()

        # 日志设置
        self.start_time = time.time()
        self.log_enabled = True
        
        # 回测参数
        self.position_amount = 100000  # 每次开仓10万
        self.max_positions = 10        # 最大持仓10只
        self.monitor_days = 15         # 监控天数
        
        # 回测状态
        self.current_date = None
        self.daily_nav = []            # 每日净值
        self.trade_log = []            # 交易日志
        self.ml_features = []          # 机器学习特征数据
        
        # 股票池状态
        self.primary_pool = {}         # 一级监控池：等待向下笔
        self.secondary_pool = {}       # 二级监控池：等待背驰信号
        self.positions = {}            # 持仓

        # 交易规则状态
        self.today_bought = set()      # 当天买入的股票（T+1规则）
        self.pending_sells = {}        # 待卖出的股票（当天买入当天出止损信号）
        self.daily_limit_prices = {}   # 每日涨跌停价格缓存 {date: {code: {'limit_up': price, 'limit_down': price}}}

        # 资金管理
        self.initial_capital = strategy.initial_capital
        self.available_cash = strategy.initial_capital
        self.total_asset = strategy.initial_capital

    def _log(self, message: str, level: str = "INFO"):
        """输出带时间戳的日志"""
        if not self.log_enabled:
            return

        current_time = datetime.now().strftime("%H:%M:%S")
        elapsed = time.time() - self.start_time
        elapsed_str = f"{elapsed:.1f}s"

        # 根据级别设置前缀
        if level == "INFO":
            prefix = "📊"
        elif level == "SUCCESS":
            prefix = "✅"
        elif level == "WARNING":
            prefix = "⚠️"
        elif level == "ERROR":
            prefix = "❌"
        elif level == "TRADE":
            prefix = "💰"
        elif level == "SIGNAL":
            prefix = "📈"
        else:
            prefix = "ℹ️"

        print(f"[{current_time}|{elapsed_str}] {prefix} {message}")

    def _log_trade(self, action: str, code: str, price: float, details: str = ""):
        """记录交易日志"""
        date_str = self.current_date.strftime("%Y-%m-%d")
        message = f"{date_str} {action} {code} 价格:{price:.2f}"
        if details:
            message += f" {details}"
        self._log(message, "TRADE")

    def _log_signal(self, code: str, signal_type: str, details: str = ""):
        """记录信号日志"""
        date_str = self.current_date.strftime("%Y-%m-%d")
        message = f"{date_str} {code} {signal_type}"
        if details:
            message += f" {details}"
        self._log(message, "SIGNAL")
        
    def run_backtest(self, start_date: str, end_date: str) -> Dict:
        """运行精确回测"""
        self._log("🚀 开始精确回测...")
        self._log(f"📅 回测期间: {start_date} 到 {end_date}")
        self._log(f"💰 初始资金: {self.initial_capital:,.2f}")
        self._log(f"📊 每次开仓: {self.position_amount:,.2f}")
        self._log(f"🏠 最大持仓: {self.max_positions}")

        # 生成交易日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        current_date = start_dt
        day_count = 0
        total_trading_days = 0

        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue

            self.current_date = current_date
            day_count += 1
            total_trading_days += 1

            try:
                # 每日交易流程
                self._daily_trading_process()

                # 记录每日净值
                self._record_daily_nav()

                # 每5个交易日打印简要状态
                if day_count % 5 == 0:
                    self._print_daily_status()

                # 每月打印详细进度
                if day_count % 20 == 0:
                    self._print_monthly_progress(total_trading_days, start_dt, end_dt)

                current_date += timedelta(days=1)

            except Exception as e:
                self._log(f"处理日期 {current_date.date()} 时出错: {e}", "ERROR")
                current_date += timedelta(days=1)
                continue

        self._log(f"📊 回测完成，共处理 {total_trading_days} 个交易日", "SUCCESS")

        # 生成回测报告
        return self._generate_final_report()
    
    def _daily_trading_process(self):
        """每日交易流程"""
        date_str = self.current_date.strftime("%Y-%m-%d")

        # 0. 重置当日状态
        self.today_bought.clear()

        # 1. 检查待卖出股票（昨天买入今天出止损信号的）
        self._process_pending_sells()

        # 2. 扫描新的涨停股票，加入一级监控池
        self._scan_and_add_to_primary_pool()

        # 3. 检查一级监控池：寻找向下笔
        self._check_primary_pool_for_down_bi()

        # 4. 检查二级监控池：寻找背驰信号并立即买入（如果5分钟没涨停）
        self._check_secondary_pool_for_divergence()

        # 5. 检查持仓：止盈止损（T+1规则）
        self._check_positions_for_exit()

        # 6. 清理超时股票
        self._cleanup_expired_stocks()

        # 7. 每日状态日志
        self._log(f"{date_str} 一级监控:{len(self.primary_pool)} 二级监控:{len(self.secondary_pool)} 持仓:{len(self.positions)}")
    
    def _scan_and_add_to_primary_pool(self):
        """扫描涨停股票并加入一级监控池"""
        # 获取股票列表
        stock_list = self.strategy.get_stock_list()
        new_candidates = 0
        scanned_count = 0

        # 每天扫描200只股票（轮换扫描）
        day_offset = (self.current_date - datetime(2023, 1, 1)).days
        start_idx = (day_offset * 200) % len(stock_list)
        end_idx = min(start_idx + 200, len(stock_list))

        scan_list = stock_list[start_idx:end_idx]
        if len(scan_list) < 200 and start_idx > 0:
            # 如果不够200只，从头开始补充
            remaining = 200 - len(scan_list)
            scan_list.extend(stock_list[:remaining])

        for code in scan_list:
            scanned_count += 1
            try:
                daily_data, min5_data = self.strategy.data_adapter.load_stock_data(code)
                if daily_data is None:
                    continue

                # 检测连续涨停
                limit_up_dates = self.strategy.limit_up_detector.detect_consecutive_limit_up(
                    daily_data, self.current_date
                )

                if len(limit_up_dates) >= 2:  # 至少2个涨停
                    # 检查是否已在监控池中
                    if (code not in self.primary_pool and
                        code not in self.secondary_pool and
                        code not in self.positions):

                        # 添加到一级监控池
                        candidate = StockCandidate(
                            code=code,
                            enter_date=self.current_date,
                            limit_up_dates=limit_up_dates,
                            status="primary_monitoring",
                            daily_data=daily_data,
                            min5_data=min5_data
                        )

                        self.primary_pool[code] = candidate
                        new_candidates += 1

                        self.trade_log.append({
                            'date': self.current_date,
                            'action': 'add_to_primary_pool',
                            'code': code,
                            'limit_up_dates': [d.strftime('%Y-%m-%d') for d in limit_up_dates],
                            'message': f'发现连续{len(limit_up_dates)}个涨停，加入一级监控池'
                        })

                        self._log_signal(code, f"发现连续{len(limit_up_dates)}个涨停，加入一级监控池")

            except Exception as e:
                continue

        if scanned_count > 0:
            self._log(f"扫描了 {scanned_count} 只股票，新增 {new_candidates} 只到一级监控池")

    def _get_stock_market_type(self, code: str) -> int:
        """获取股票市场类型"""
        if code.startswith('00'):
            return 0  # 深圳主板
        elif code.startswith('60'):
            return 1  # 上海主板
        elif code.startswith('30'):
            return 2  # 创业板（20%涨跌幅）
        elif code.startswith('68'):
            return 3  # 科创板（20%涨跌幅）
        else:
            return 0  # 默认深圳主板

    def _calculate_limit_prices(self, code: str, yesterday_close: float) -> dict:
        """计算涨跌停价格"""
        market_type = self._get_stock_market_type(code)

        if market_type in [2, 3]:  # 创业板、科创板
            limit_ratio = 0.2  # 20%
        else:  # 主板
            limit_ratio = 0.1  # 10%

        limit_up_price = yesterday_close * (1 + limit_ratio)
        limit_down_price = yesterday_close * (1 - limit_ratio)

        # 价格精度处理（保留2位小数）
        limit_up_price = round(limit_up_price, 2)
        limit_down_price = round(limit_down_price, 2)

        return {
            'limit_up': limit_up_price,
            'limit_down': limit_down_price,
            'yesterday_close': yesterday_close
        }

    def _get_daily_limit_prices(self, code: str) -> dict:
        """获取股票当日的涨跌停价格"""
        date_key = self.current_date.date()

        # 检查缓存
        if date_key in self.daily_limit_prices and code in self.daily_limit_prices[date_key]:
            return self.daily_limit_prices[date_key][code]

        try:
            # 获取昨日收盘价
            daily_data, _ = self.strategy.data_adapter.load_stock_data(code)
            if daily_data is None:
                return None

            yesterday_data = daily_data[daily_data.index.date < self.current_date.date()]
            if len(yesterday_data) == 0:
                return None

            yesterday_close = yesterday_data['close'].iloc[-1]

            # 计算涨跌停价格
            limit_prices = self._calculate_limit_prices(code, yesterday_close)

            # 缓存结果
            if date_key not in self.daily_limit_prices:
                self.daily_limit_prices[date_key] = {}
            self.daily_limit_prices[date_key][code] = limit_prices

            return limit_prices

        except Exception as e:
            return None



    def _process_pending_sells(self):
        """处理待卖出股票（昨天买入今天出止损信号的）"""
        if not self.pending_sells:
            return

        processed_count = 0
        for code, sell_info in list(self.pending_sells.items()):
            if code in self.positions:
                # 执行卖出
                self._close_position(code, sell_info['price'], sell_info['reason'])
                processed_count += 1

            # 从待卖出列表移除
            del self.pending_sells[code]

        if processed_count > 0:
            print(f"   📉 处理待卖出，卖出 {processed_count} 只股票")

    def _is_5min_limit_up(self, code: str) -> bool:
        """检查股票当前5分钟收盘价是否达到涨停价"""
        try:
            # 获取当日涨停价
            limit_prices = self._get_daily_limit_prices(code)
            if limit_prices is None:
                return False

            limit_up_price = limit_prices['limit_up']

            # 获取当前5分钟数据
            _, min5_data = self.strategy.data_adapter.load_stock_data(code)
            if min5_data is None:
                return False

            current_5min_data = min5_data[min5_data.index.date == self.current_date.date()]
            if len(current_5min_data) == 0:
                return False

            # 获取最新5分钟收盘价
            current_5min_close = current_5min_data['close'].iloc[-1]

            # 检查5分钟收盘价是否达到涨停价（允许0.01的误差）
            return current_5min_close >= (limit_up_price - 0.01)

        except Exception as e:
            return False

    def _get_current_5min_price(self, code: str) -> float:
        """获取当前5分钟收盘价"""
        try:
            _, min5_data = self.strategy.data_adapter.load_stock_data(code)
            if min5_data is None:
                return None

            current_5min_data = min5_data[min5_data.index.date == self.current_date.date()]
            if len(current_5min_data) == 0:
                return None

            return current_5min_data['close'].iloc[-1]

        except:
            return None

    def _calculate_comprehensive_features(self, daily_data: pd.DataFrame, min5_data: pd.DataFrame, code: str, candidate: StockCandidate = None) -> dict:
        """计算开仓点的完整特征快照"""
        try:
            features = {}

            # ==================== 基础信息 ====================
            features['code'] = code
            features['open_date'] = self.current_date.strftime('%Y-%m-%d')
            features['open_time'] = self.current_date.strftime('%Y-%m-%d %H:%M:%S')

            # ==================== 前一日日线技术指标 ====================
            if daily_data is not None and len(daily_data) >= 30:
                # 获取前一日数据（开仓当日不包含完整信息）
                prev_daily_data = daily_data[daily_data.index.date < self.current_date.date()]
                if len(prev_daily_data) >= 20:
                    recent_daily = prev_daily_data.tail(20)
                    prev_daily = recent_daily.iloc[-1]  # 前一日

                    # 前一日基础价格指标
                    features['prev_daily_open'] = prev_daily['open']
                    features['prev_daily_high'] = prev_daily['high']
                    features['prev_daily_low'] = prev_daily['low']
                    features['prev_daily_close'] = prev_daily['close']
                    features['prev_daily_volume'] = prev_daily['volume']
                    features['prev_daily_amount'] = prev_daily.get('amount', 0)

                    # 前一日K线形态
                    features['prev_daily_body'] = abs(prev_daily['close'] - prev_daily['open']) / prev_daily['open']
                    features['prev_daily_upper_shadow'] = (prev_daily['high'] - max(prev_daily['open'], prev_daily['close'])) / prev_daily['open']
                    features['prev_daily_lower_shadow'] = (min(prev_daily['open'], prev_daily['close']) - prev_daily['low']) / prev_daily['open']
                    features['prev_daily_is_red'] = 1 if prev_daily['close'] > prev_daily['open'] else 0

                    # 前一日移动平均线
                    features['prev_ma5'] = recent_daily['close'].tail(5).mean()
                    features['prev_ma10'] = recent_daily['close'].tail(10).mean()
                    features['prev_ma20'] = recent_daily['close'].tail(20).mean()
                    features['prev_ma30'] = prev_daily_data['close'].tail(30).mean() if len(prev_daily_data) >= 30 else features['prev_ma20']

                    # 前一日价格相对位置
                    features['prev_close_vs_ma5'] = (prev_daily['close'] - features['prev_ma5']) / features['prev_ma5']
                    features['prev_close_vs_ma10'] = (prev_daily['close'] - features['prev_ma10']) / features['prev_ma10']
                    features['prev_close_vs_ma20'] = (prev_daily['close'] - features['prev_ma20']) / features['prev_ma20']
                    features['prev_close_vs_ma30'] = (prev_daily['close'] - features['prev_ma30']) / features['prev_ma30']

                    # 前一日成交量指标
                    features['prev_volume_ma5'] = recent_daily['volume'].tail(5).mean()
                    features['prev_volume_ma10'] = recent_daily['volume'].tail(10).mean()
                    features['prev_volume_ratio'] = prev_daily['volume'] / features['prev_volume_ma5']
                    features['prev_volume_ratio_10'] = prev_daily['volume'] / features['prev_volume_ma10']

                    # 前一日波动率指标
                    features['prev_volatility_5d'] = recent_daily['close'].pct_change().tail(5).std()
                    features['prev_volatility_10d'] = recent_daily['close'].pct_change().tail(10).std()
                    features['prev_volatility_20d'] = recent_daily['close'].pct_change().tail(20).std()

                    # 前一日价格区间指标
                    features['prev_high_5d'] = recent_daily['high'].tail(5).max()
                    features['prev_low_5d'] = recent_daily['low'].tail(5).min()
                    features['prev_high_10d'] = recent_daily['high'].tail(10).max()
                    features['prev_low_10d'] = recent_daily['low'].tail(10).min()
                    features['prev_high_20d'] = recent_daily['high'].tail(20).max()
                    features['prev_low_20d'] = recent_daily['low'].tail(20).min()

                    # 前一日价格位置百分比
                    features['prev_price_position_5d'] = (prev_daily['close'] - features['prev_low_5d']) / (features['prev_high_5d'] - features['prev_low_5d']) if features['prev_high_5d'] != features['prev_low_5d'] else 0.5
                    features['prev_price_position_10d'] = (prev_daily['close'] - features['prev_low_10d']) / (features['prev_high_10d'] - features['prev_low_10d']) if features['prev_high_10d'] != features['prev_low_10d'] else 0.5
                    features['prev_price_position_20d'] = (prev_daily['close'] - features['prev_low_20d']) / (features['prev_high_20d'] - features['prev_low_20d']) if features['prev_high_20d'] != features['prev_low_20d'] else 0.5

                    # 前一日涨跌幅
                    if len(recent_daily) >= 2:
                        prev_prev_close = recent_daily.iloc[-2]['close']
                        features['prev_daily_return'] = (prev_daily['close'] - prev_prev_close) / prev_prev_close

                    # 前一日RSI指标
                    features['prev_rsi_14'] = self._calculate_rsi(recent_daily['close'], 14)

                    # 前一日MACD指标
                    macd_data = self._calculate_macd(recent_daily['close'])
                    features['prev_macd'] = macd_data['macd']
                    features['prev_macd_signal'] = macd_data['signal']
                    features['prev_macd_hist'] = macd_data['histogram']

            # ==================== 开仓前5分钟技术指标 ====================
            if min5_data is not None and len(min5_data) >= 100:
                current_5min_data = min5_data[min5_data.index <= self.current_date]
                if len(current_5min_data) >= 50:
                    recent_5min = current_5min_data.tail(50)
                    current_5min = recent_5min.iloc[-1]  # 开仓前最后一个5分钟K线

                    # 开仓前5分钟基础指标
                    features['current_5min_open'] = current_5min['open']
                    features['current_5min_high'] = current_5min['high']
                    features['current_5min_low'] = current_5min['low']
                    features['current_5min_close'] = current_5min['close']
                    features['current_5min_volume'] = current_5min['volume']

                    # 5分钟K线形态
                    features['current_5min_body'] = abs(current_5min['close'] - current_5min['open']) / current_5min['open']
                    features['current_5min_upper_shadow'] = (current_5min['high'] - max(current_5min['open'], current_5min['close'])) / current_5min['open']
                    features['current_5min_lower_shadow'] = (min(current_5min['open'], current_5min['close']) - current_5min['low']) / current_5min['open']
                    features['current_5min_is_red'] = 1 if current_5min['close'] > current_5min['open'] else 0

                    # 5分钟移动平均线
                    features['current_5min_ma5'] = recent_5min['close'].tail(5).mean()
                    features['current_5min_ma10'] = recent_5min['close'].tail(10).mean()
                    features['current_5min_ma20'] = recent_5min['close'].tail(20).mean()
                    features['current_5min_ma30'] = recent_5min['close'].tail(30).mean()

                    # 5分钟价格相对位置
                    features['current_5min_close_vs_ma5'] = (current_5min['close'] - features['current_5min_ma5']) / features['current_5min_ma5']
                    features['current_5min_close_vs_ma10'] = (current_5min['close'] - features['current_5min_ma10']) / features['current_5min_ma10']
                    features['current_5min_close_vs_ma20'] = (current_5min['close'] - features['current_5min_ma20']) / features['current_5min_ma20']
                    features['current_5min_close_vs_ma30'] = (current_5min['close'] - features['current_5min_ma30']) / features['current_5min_ma30']

                    # 5分钟成交量指标
                    features['current_5min_volume_ma5'] = recent_5min['volume'].tail(5).mean()
                    features['current_5min_volume_ma10'] = recent_5min['volume'].tail(10).mean()
                    features['current_5min_volume_ratio'] = current_5min['volume'] / features['current_5min_volume_ma5']
                    features['current_5min_volume_ratio_10'] = current_5min['volume'] / features['current_5min_volume_ma10']

                    # 5分钟波动率
                    features['current_5min_volatility_10'] = recent_5min['close'].pct_change().tail(10).std()
                    features['current_5min_volatility_20'] = recent_5min['close'].pct_change().tail(20).std()

                    # 5分钟价格区间
                    features['current_5min_high_10'] = recent_5min['high'].tail(10).max()
                    features['current_5min_low_10'] = recent_5min['low'].tail(10).min()
                    features['current_5min_high_20'] = recent_5min['high'].tail(20).max()
                    features['current_5min_low_20'] = recent_5min['low'].tail(20).min()
                    features['current_5min_high_30'] = recent_5min['high'].tail(30).max()
                    features['current_5min_low_30'] = recent_5min['low'].tail(30).min()

                    # 5分钟价格位置百分比
                    features['current_5min_position_10'] = (current_5min['close'] - features['current_5min_low_10']) / (features['current_5min_high_10'] - features['current_5min_low_10']) if features['current_5min_high_10'] != features['current_5min_low_10'] else 0.5
                    features['current_5min_position_20'] = (current_5min['close'] - features['current_5min_low_20']) / (features['current_5min_high_20'] - features['current_5min_low_20']) if features['current_5min_high_20'] != features['current_5min_low_20'] else 0.5
                    features['current_5min_position_30'] = (current_5min['close'] - features['current_5min_low_30']) / (features['current_5min_high_30'] - features['current_5min_low_30']) if features['current_5min_high_30'] != features['current_5min_low_30'] else 0.5

                    # 5分钟RSI指标
                    features['current_5min_rsi_14'] = self._calculate_rsi(recent_5min['close'], 14)

                    # 5分钟MACD指标
                    macd_5min = self._calculate_macd(recent_5min['close'])
                    features['current_5min_macd'] = macd_5min['macd']
                    features['current_5min_macd_signal'] = macd_5min['signal']
                    features['current_5min_macd_hist'] = macd_5min['histogram']

            return features

        except Exception as e:
            self._log(f"计算综合特征失败 {code}: {e}", "ERROR")
            return {}

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except:
            return 50.0

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> dict:
        """计算MACD指标"""
        try:
            if len(prices) < slow + signal:
                return {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0}

            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line

            return {
                'macd': macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else 0.0,
                'signal': signal_line.iloc[-1] if not pd.isna(signal_line.iloc[-1]) else 0.0,
                'histogram': histogram.iloc[-1] if not pd.isna(histogram.iloc[-1]) else 0.0
            }
        except:
            return {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0}

    def _calculate_chanlun_features(self, candidate: StockCandidate, features: dict, daily_data: pd.DataFrame, min5_data: pd.DataFrame, code: str) -> dict:
        """计算缠论相关特征"""
        try:
            # ==================== 涨停板相关特征 ====================
            features['limit_up_count'] = len(candidate.limit_up_dates) if candidate.limit_up_dates else 0
            features['days_since_limit_up'] = (self.current_date - candidate.enter_date).days
            features['monitoring_days'] = (self.current_date - candidate.enter_date).days
            features['candidate_status'] = candidate.status

            # ==================== 向下笔特征 ====================
            if candidate.decline_bi_low > 0:
                features['has_down_bi'] = 1
                features['bi_low'] = candidate.decline_bi_low
                features['bi_start_time'] = candidate.decline_bi_start.strftime('%Y-%m-%d %H:%M:%S') if candidate.decline_bi_start else ''
                features['bi_end_time'] = candidate.decline_bi_end.strftime('%Y-%m-%d %H:%M:%S') if candidate.decline_bi_end else ''

                # 当前价格相对笔低点的位置
                current_price = features.get('current_5min_close', features.get('prev_daily_close', 0))
                if current_price > 0:
                    features['price_vs_bi_low'] = (current_price - candidate.decline_bi_low) / candidate.decline_bi_low
                else:
                    features['price_vs_bi_low'] = 0
            else:
                features['has_down_bi'] = 0
                features['bi_low'] = 0
                features['bi_start_time'] = ''
                features['bi_end_time'] = ''
                features['price_vs_bi_low'] = 0

            # ==================== 尝试计算更详细的缠论特征 ====================
            try:
                # 使用缠论分析器计算笔和线段
                if min5_data is not None and len(min5_data) >= 100:
                    current_5min_data = min5_data[min5_data.index <= self.current_date]
                    if len(current_5min_data) >= 50:
                        # 简化的笔特征计算
                        bi_features = self._calculate_simple_bi_features(current_5min_data.tail(100))
                        features.update(bi_features)

                        # 简化的线段特征计算
                        duan_features = self._calculate_simple_duan_features(current_5min_data.tail(200))
                        features.update(duan_features)

                        # 简化的中枢特征计算
                        zhongshu_features = self._calculate_simple_zhongshu_features(current_5min_data.tail(300))
                        features.update(zhongshu_features)
            except Exception as e:
                self._log(f"计算详细缠论特征失败 {code}: {e}", "WARNING")
                # 设置默认值
                for i in range(5):
                    features[f'bi_{i+1}_high'] = 0
                    features[f'bi_{i+1}_low'] = 0
                    features[f'bi_{i+1}_direction'] = 0
                    features[f'bi_{i+1}_strength'] = 0

            return features

        except Exception as e:
            self._log(f"计算缠论特征失败 {code}: {e}", "ERROR")
            return features

    def _calculate_simple_bi_features(self, data: pd.DataFrame) -> dict:
        """计算简化的笔特征"""
        try:
            features = {}

            # 寻找最近的高低点作为笔的端点
            highs = data['high'].rolling(window=3, center=True).max() == data['high']
            lows = data['low'].rolling(window=3, center=True).min() == data['low']

            # 获取最近的5个笔端点
            high_points = data[highs].tail(3)
            low_points = data[lows].tail(3)

            # 合并并按时间排序
            all_points = pd.concat([high_points, low_points]).sort_index()
            recent_points = all_points.tail(5)

            # 计算前5笔的特征
            for i, (idx, point) in enumerate(recent_points.iterrows()):
                if i < 5:
                    features[f'bi_{i+1}_high'] = point['high']
                    features[f'bi_{i+1}_low'] = point['low']
                    features[f'bi_{i+1}_close'] = point['close']
                    features[f'bi_{i+1}_volume'] = point['volume']

                    # 判断笔的方向（简化）
                    if i > 0:
                        prev_point = list(recent_points.iterrows())[i-1][1]
                        if point['close'] > prev_point['close']:
                            features[f'bi_{i+1}_direction'] = 1  # 向上笔
                        else:
                            features[f'bi_{i+1}_direction'] = -1  # 向下笔
                    else:
                        features[f'bi_{i+1}_direction'] = 0

                    # 笔的强度（价格变化幅度）
                    features[f'bi_{i+1}_strength'] = (point['high'] - point['low']) / point['low'] if point['low'] > 0 else 0

            # 填充不足5个笔的默认值
            for i in range(len(recent_points), 5):
                features[f'bi_{i+1}_high'] = 0
                features[f'bi_{i+1}_low'] = 0
                features[f'bi_{i+1}_close'] = 0
                features[f'bi_{i+1}_volume'] = 0
                features[f'bi_{i+1}_direction'] = 0
                features[f'bi_{i+1}_strength'] = 0

            return features

        except Exception as e:
            # 返回默认值
            features = {}
            for i in range(5):
                features[f'bi_{i+1}_high'] = 0
                features[f'bi_{i+1}_low'] = 0
                features[f'bi_{i+1}_close'] = 0
                features[f'bi_{i+1}_volume'] = 0
                features[f'bi_{i+1}_direction'] = 0
                features[f'bi_{i+1}_strength'] = 0
            return features

    def _calculate_simple_duan_features(self, data: pd.DataFrame) -> dict:
        """计算简化的线段特征"""
        try:
            features = {}

            # 简化的线段识别：使用更大的窗口寻找转折点
            highs = data['high'].rolling(window=10, center=True).max() == data['high']
            lows = data['low'].rolling(window=10, center=True).min() == data['low']

            high_points = data[highs].tail(2)
            low_points = data[lows].tail(2)

            all_points = pd.concat([high_points, low_points]).sort_index()
            recent_duans = all_points.tail(3)

            # 计算前3个线段的特征
            for i, (idx, point) in enumerate(recent_duans.iterrows()):
                if i < 3:
                    features[f'duan_{i+1}_high'] = point['high']
                    features[f'duan_{i+1}_low'] = point['low']
                    features[f'duan_{i+1}_close'] = point['close']
                    features[f'duan_{i+1}_volume'] = point['volume']

                    # 线段方向
                    if i > 0:
                        prev_point = list(recent_duans.iterrows())[i-1][1]
                        if point['close'] > prev_point['close']:
                            features[f'duan_{i+1}_direction'] = 1
                        else:
                            features[f'duan_{i+1}_direction'] = -1
                    else:
                        features[f'duan_{i+1}_direction'] = 0

                    # 线段强度
                    features[f'duan_{i+1}_strength'] = (point['high'] - point['low']) / point['low'] if point['low'] > 0 else 0

            # 填充默认值
            for i in range(len(recent_duans), 3):
                features[f'duan_{i+1}_high'] = 0
                features[f'duan_{i+1}_low'] = 0
                features[f'duan_{i+1}_close'] = 0
                features[f'duan_{i+1}_volume'] = 0
                features[f'duan_{i+1}_direction'] = 0
                features[f'duan_{i+1}_strength'] = 0

            return features

        except Exception as e:
            # 返回默认值
            features = {}
            for i in range(3):
                features[f'duan_{i+1}_high'] = 0
                features[f'duan_{i+1}_low'] = 0
                features[f'duan_{i+1}_close'] = 0
                features[f'duan_{i+1}_volume'] = 0
                features[f'duan_{i+1}_direction'] = 0
                features[f'duan_{i+1}_strength'] = 0
            return features

    def _calculate_simple_zhongshu_features(self, data: pd.DataFrame) -> dict:
        """计算简化的中枢特征"""
        try:
            features = {}

            # 简化的中枢识别：寻找价格震荡区间
            if len(data) >= 50:
                recent_data = data.tail(50)

                # 计算价格区间
                high_max = recent_data['high'].max()
                low_min = recent_data['low'].min()
                price_range = high_max - low_min

                # 寻找中枢区间（价格密集区域）
                price_bins = pd.cut(recent_data['close'], bins=10)
                price_counts = price_bins.value_counts()
                most_frequent_bin = price_counts.idxmax()

                # 中枢特征
                features['zhongshu_high'] = most_frequent_bin.right
                features['zhongshu_low'] = most_frequent_bin.left
                features['zhongshu_center'] = (most_frequent_bin.right + most_frequent_bin.left) / 2
                features['zhongshu_width'] = most_frequent_bin.right - most_frequent_bin.left
                features['zhongshu_strength'] = price_counts.max() / len(recent_data)  # 中枢强度

                # 当前价格相对中枢的位置
                current_price = recent_data['close'].iloc[-1]
                if features['zhongshu_width'] > 0:
                    features['price_vs_zhongshu'] = (current_price - features['zhongshu_center']) / features['zhongshu_width']
                else:
                    features['price_vs_zhongshu'] = 0
            else:
                # 默认值
                features['zhongshu_high'] = 0
                features['zhongshu_low'] = 0
                features['zhongshu_center'] = 0
                features['zhongshu_width'] = 0
                features['zhongshu_strength'] = 0
                features['price_vs_zhongshu'] = 0

            return features

        except Exception as e:
            # 返回默认值
            return {
                'zhongshu_high': 0,
                'zhongshu_low': 0,
                'zhongshu_center': 0,
                'zhongshu_width': 0,
                'zhongshu_strength': 0,
                'price_vs_zhongshu': 0
            }

    def _check_primary_pool_for_down_bi(self):
        """检查一级监控池中的股票是否出现向下笔"""
        for code in list(self.primary_pool.keys()):
            candidate = self.primary_pool[code]
            
            try:
                # 获取5分钟数据
                if candidate.min5_data is None:
                    continue
                
                # 获取截至当前日期的5分钟数据
                current_5min_data = candidate.min5_data[
                    candidate.min5_data.index.date <= self.current_date.date()
                ]
                
                if len(current_5min_data) < 50:
                    continue
                
                # 使用简化的向下笔检测
                has_down_bi, bi_info = self._detect_down_bi_simple(current_5min_data)
                
                if has_down_bi:
                    # 移动到二级监控池
                    candidate.status = "secondary_monitoring"
                    candidate.has_down_bi = True
                    candidate.decline_bi_low = bi_info['low']
                    candidate.decline_bi_start = bi_info['start_time']
                    candidate.decline_bi_end = bi_info['end_time']
                    
                    self.secondary_pool[code] = candidate
                    del self.primary_pool[code]
                    
                    self.trade_log.append({
                        'date': self.current_date,
                        'action': 'move_to_secondary_pool',
                        'code': code,
                        'bi_low': bi_info['low'],
                        'message': f'检测到向下笔，最低点{bi_info["low"]:.2f}，进入二级监控'
                    })
                    
                    self._log_signal(code, f"检测到向下笔，最低点{bi_info['low']:.2f}，进入二级监控池")
            
            except Exception as e:
                continue
    
    def _detect_down_bi_simple(self, df_5min: pd.DataFrame) -> Tuple[bool, Dict]:
        """简化的向下笔检测"""
        if len(df_5min) < 20:
            return False, {}
        
        # 使用最近20根K线进行简单的向下笔检测
        recent_data = df_5min.tail(20)
        
        # 寻找最高点和最低点
        high_idx = recent_data['high'].idxmax()
        low_idx = recent_data['low'].idxmin()
        
        # 检查是否形成向下笔：最高点在前，最低点在后，且有足够的跌幅
        if (high_idx < low_idx and 
            recent_data.loc[high_idx, 'high'] > recent_data.loc[low_idx, 'low'] * 1.03):  # 至少3%跌幅
            
            return True, {
                'high': recent_data.loc[high_idx, 'high'],
                'low': recent_data.loc[low_idx, 'low'],
                'start_time': high_idx,
                'end_time': low_idx
            }
        
        return False, {}
    
    def _check_secondary_pool_for_divergence(self):
        """检查二级监控池中的股票是否出现背驰信号并立即买入（如果5分钟没涨停）"""
        signals_found = 0
        bought_count = 0

        for code in list(self.secondary_pool.keys()):
            candidate = self.secondary_pool[code]

            # 检查是否满仓
            if len(self.positions) >= self.max_positions:
                break

            try:
                # 获取当前5分钟数据
                if candidate.min5_data is None:
                    continue

                current_5min_data = candidate.min5_data[
                    candidate.min5_data.index.date <= self.current_date.date()
                ]

                if len(current_5min_data) < 50:
                    continue

                # 简化的背驰信号检测
                has_divergence, signal_info = self._detect_divergence_simple(
                    current_5min_data, candidate.decline_bi_low
                )

                if has_divergence:
                    signals_found += 1
                    self._log_signal(code, "出现买入信号")

                    # 检查5分钟收盘价是否涨停
                    if self._is_5min_limit_up(code):
                        self._log(f"{code} 5分钟收盘价涨停，无法买入", "WARNING")
                    else:
                        # 可以买入，获取当前5分钟价格
                        current_5min_price = self._get_current_5min_price(code)
                        if current_5min_price is not None:
                            # 更新信号价格为5分钟收盘价
                            signal_info['signal_price'] = current_5min_price

                            # 立即开仓
                            self._open_position(code, candidate, signal_info)
                            bought_count += 1

                    # 从二级监控池移除（无论是否买入成功）
                    del self.secondary_pool[code]

            except Exception as e:
                continue

        if signals_found > 0:
            print(f"   📊 发现 {signals_found} 个买入信号，成功买入 {bought_count} 只")
    
    def _detect_divergence_simple(self, df_5min: pd.DataFrame, bi_low: float) -> Tuple[bool, Dict]:
        """简化的背驰信号检测"""
        if len(df_5min) < 10:
            return False, {}
        
        recent_data = df_5min.tail(10)
        current_price = recent_data['close'].iloc[-1]
        
        # 简化的背驰条件：
        # 1. 当前价格接近向下笔低点（在低点上下2%范围内）
        # 2. 最近出现反弹（最近3根K线有上涨）
        price_near_low = abs(current_price - bi_low) / bi_low <= 0.02
        
        # 检查最近是否有反弹迹象
        recent_closes = recent_data['close'].tail(3)
        has_rebound = (recent_closes.iloc[-1] > recent_closes.iloc[-3] * 1.005)  # 最近有0.5%以上反弹
        
        if price_near_low and has_rebound:
            return True, {
                'signal_price': current_price,
                'signal_time': recent_data.index[-1],
                'reason': '价格接近笔低点且出现反弹'
            }
        
        return False, {}
    
    def _open_position(self, code: str, candidate: StockCandidate, signal_info: Dict):
        """开仓"""
        signal_price = signal_info['signal_price']

        # 计算止损止盈 - 修改为8%止损，16%止盈
        stop_loss = signal_price * (1 - 0.08)   # 8%止损
        take_profit = signal_price * (1 + 0.16)  # 16%止盈

        # 计算股数
        shares = int(self.position_amount / signal_price / 100) * 100  # 整手
        actual_cost = signal_price * shares

        if actual_cost > self.available_cash:
            return  # 资金不足

        # 计算完整的特征快照
        daily_data, min5_data = self.strategy.data_adapter.load_stock_data(code)
        features = self._calculate_comprehensive_features(daily_data, min5_data, code, candidate)
        features = self._calculate_chanlun_features(candidate, features, daily_data, min5_data, code)

        # 添加开仓相关信息
        features['action'] = 'open'
        features['signal_price'] = signal_price
        features['stop_loss'] = stop_loss
        features['take_profit'] = take_profit
        features['shares'] = shares
        features['actual_cost'] = actual_cost
        features['signal_reason'] = signal_info['reason']
        features['position_amount'] = self.position_amount
        features['available_cash_before'] = self.available_cash
        features['total_asset'] = self.total_asset
        features['nav'] = self.total_asset / self.initial_capital

        # 记录开仓特征数据（这是一条完整的训练样本的开始）
        self.ml_features.append(features.copy())

        # 创建持仓
        position = Position(
            code=code,
            enter_price=signal_price,
            enter_date=self.current_date,
            stop_loss=stop_loss,
            take_profit=take_profit,
            shares=shares,
            enter_reason=signal_info['reason']
        )

        self.positions[code] = position
        self.available_cash -= actual_cost
        
        # 记录交易
        self.trade_log.append({
            'date': self.current_date,
            'action': 'open_position',
            'code': code,
            'price': signal_price,
            'shares': shares,
            'cost': actual_cost,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'reason': signal_info['reason']
        })
        
        self._log_trade("开仓", code, signal_price, f"股数{shares} 止损{stop_loss:.2f} 止盈{take_profit:.2f}")

        # 记录到当天买入列表（T+1规则）
        self.today_bought.add(code)
    
    def _check_positions_for_exit(self):
        """检查持仓的止盈止损（T+1规则）"""
        for code in list(self.positions.keys()):
            position = self.positions[code]

            # T+1规则：当天买入的股票当天不能卖出
            if code in self.today_bought:
                continue

            try:
                # 获取当前价格
                current_price = self._get_current_price(code)
                if current_price is None:
                    continue

                # 检查是否是当天买入的股票
                is_bought_today = position.enter_date.date() == self.current_date.date()

                # 检查止损
                if current_price <= position.stop_loss:
                    if is_bought_today:
                        # 当天买入当天出止损信号，记录待明日卖出
                        self.pending_sells[code] = {
                            'price': current_price,
                            'reason': '止损（T+1延迟）'
                        }
                        print(f"   ⏰ {code} 当天买入出现止损信号，记录待明日卖出")
                    else:
                        # 非当天买入，可以立即卖出
                        self._close_position(code, current_price, "止损")

                # 检查止盈
                elif current_price >= position.take_profit:
                    if is_bought_today:
                        # 当天买入当天出止盈信号，记录待明日卖出
                        self.pending_sells[code] = {
                            'price': current_price,
                            'reason': '止盈（T+1延迟）'
                        }
                        print(f"   ⏰ {code} 当天买入出现止盈信号，记录待明日卖出")
                    else:
                        # 非当天买入，可以立即卖出
                        self._close_position(code, current_price, "止盈")

            except Exception as e:
                continue
    
    def _close_position(self, code: str, exit_price: float, reason: str):
        """平仓"""
        position = self.positions[code]

        # 计算盈亏
        proceeds = exit_price * position.shares
        cost = position.enter_price * position.shares
        pnl = proceeds - cost
        pnl_pct = pnl / cost

        # 计算平仓时的技术指标（简化版，主要记录交易结果）
        daily_data, min5_data = self.strategy.data_adapter.load_stock_data(code)
        close_features = {
            'code': code,
            'close_date': self.current_date.strftime('%Y-%m-%d'),
            'close_time': self.current_date.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 添加平仓相关信息
        close_features['action'] = 'close'
        close_features['enter_price'] = position.enter_price
        close_features['exit_price'] = exit_price
        close_features['shares'] = position.shares
        close_features['cost'] = cost
        close_features['proceeds'] = proceeds
        close_features['pnl'] = pnl
        close_features['pnl_pct'] = pnl_pct
        close_features['hold_days'] = (self.current_date - position.enter_date).days
        close_features['exit_reason'] = reason
        close_features['enter_reason'] = position.enter_reason
        close_features['stop_loss'] = position.stop_loss
        close_features['take_profit'] = position.take_profit
        close_features['available_cash_before'] = self.available_cash
        close_features['total_asset'] = self.total_asset
        close_features['nav'] = self.total_asset / self.initial_capital

        # 判断交易结果 - 这是机器学习的标签
        if pnl > 0:
            close_features['trade_result'] = 'win'
            close_features['label'] = 1  # 盈利标签
        elif pnl < 0:
            close_features['trade_result'] = 'loss'
            close_features['label'] = -1  # 亏损标签
        else:
            close_features['trade_result'] = 'break_even'
            close_features['label'] = 0  # 平手标签

        # 更新对应的开仓记录，添加交易结果
        # 寻找对应的开仓记录并更新
        for i, record in enumerate(self.ml_features):
            if (record.get('action') == 'open' and
                record.get('code') == code and
                record.get('signal_price') == position.enter_price and
                'label' not in record):  # 确保还没有标签

                # 将交易结果添加到开仓记录中
                self.ml_features[i]['close_date'] = close_features['close_date']
                self.ml_features[i]['close_time'] = close_features['close_time']
                self.ml_features[i]['exit_price'] = exit_price
                self.ml_features[i]['pnl'] = pnl
                self.ml_features[i]['pnl_pct'] = pnl_pct
                self.ml_features[i]['hold_days'] = close_features['hold_days']
                self.ml_features[i]['exit_reason'] = reason
                self.ml_features[i]['trade_result'] = close_features['trade_result']
                self.ml_features[i]['label'] = close_features['label']
                break

        # 更新资金
        self.available_cash += proceeds
        
        # 记录交易
        self.trade_log.append({
            'date': self.current_date,
            'action': 'close_position',
            'code': code,
            'enter_price': position.enter_price,
            'exit_price': exit_price,
            'shares': position.shares,
            'pnl': pnl,
            'reason': reason,
            'hold_days': (self.current_date - position.enter_date).days
        })
        
        # 移除持仓
        del self.positions[code]
        
        pnl_pct = pnl / cost * 100
        self._log_trade("平仓", code, exit_price, f"盈亏{pnl:.2f}({pnl_pct:+.1f}%) {reason}")
    
    def _get_current_price(self, code: str) -> Optional[float]:
        """获取当前价格"""
        try:
            daily_data, min5_data = self.strategy.data_adapter.load_stock_data(code)
            
            if min5_data is not None:
                # 使用5分钟数据的最新价格
                current_data = min5_data[min5_data.index.date <= self.current_date.date()]
                if len(current_data) > 0:
                    return current_data['close'].iloc[-1]
            
            if daily_data is not None:
                # 使用日线数据
                current_data = daily_data[daily_data.index.date <= self.current_date.date()]
                if len(current_data) > 0:
                    return current_data['close'].iloc[-1]
            
            return None
        except:
            return None
    
    def _cleanup_expired_stocks(self):
        """清理超时股票"""
        # 清理一级监控池
        expired_primary = []
        for code, candidate in self.primary_pool.items():
            days_monitored = (self.current_date - candidate.enter_date).days
            if days_monitored > self.monitor_days:
                expired_primary.append(code)
        
        for code in expired_primary:
            del self.primary_pool[code]
            self.trade_log.append({
                'date': self.current_date,
                'action': 'remove_expired',
                'code': code,
                'pool': 'primary',
                'message': f'监控{self.monitor_days}天超时，移除'
            })
        
        # 清理二级监控池
        expired_secondary = []
        for code, candidate in self.secondary_pool.items():
            days_monitored = (self.current_date - candidate.enter_date).days
            if days_monitored > self.monitor_days:
                expired_secondary.append(code)
        
        for code in expired_secondary:
            del self.secondary_pool[code]
            self.trade_log.append({
                'date': self.current_date,
                'action': 'remove_expired',
                'code': code,
                'pool': 'secondary',
                'message': f'监控{self.monitor_days}天超时，移除'
            })
        
        if expired_primary or expired_secondary:
            self._log(f"清理超时股票: 一级{len(expired_primary)}只, 二级{len(expired_secondary)}只")

    def _print_monthly_progress(self, current_days: int, start_dt: datetime, end_dt: datetime):
        """打印月度进度报告"""
        total_days = (end_dt - start_dt).days
        progress = current_days / total_days * 100 if total_days > 0 else 0

        nav = self.total_asset / self.initial_capital

        self._log("="*50, "INFO")
        self._log(f"📊 月度进度报告 - 已完成 {progress:.1f}%", "INFO")
        self._log(f"📅 当前日期: {self.current_date.strftime('%Y-%m-%d')}", "INFO")
        self._log(f"💰 当前净值: {nav:.4f}", "INFO")
        self._log(f"💵 总资产: {self.total_asset:,.2f}", "INFO")
        self._log(f"🏦 可用资金: {self.available_cash:,.2f}", "INFO")
        self._log(f"📈 一级监控池: {len(self.primary_pool)} 只", "INFO")
        self._log(f"📊 二级监控池: {len(self.secondary_pool)} 只", "INFO")
        self._log(f"🏠 当前持仓: {len(self.positions)} 只", "INFO")

        if self.positions:
            self._log("当前持仓详情:", "INFO")
            for code, position in list(self.positions.items())[:5]:  # 显示前5只
                current_price = self._get_current_price(code)
                if current_price:
                    pnl_pct = (current_price - position.enter_price) / position.enter_price * 100
                    self._log(f"  {code}: {current_price:.2f} ({pnl_pct:+.1f}%)", "INFO")

        self._log("="*50, "INFO")
    
    def _record_daily_nav(self):
        """记录每日净值"""
        # 计算持仓市值
        position_value = 0
        for code, position in self.positions.items():
            current_price = self._get_current_price(code)
            if current_price:
                position_value += current_price * position.shares
        
        # 计算总资产
        self.total_asset = self.available_cash + position_value
        
        # 计算净值
        nav = self.total_asset / self.initial_capital
        
        self.daily_nav.append({
            'date': self.current_date,
            'available_cash': self.available_cash,
            'position_value': position_value,
            'total_asset': self.total_asset,
            'nav': nav,
            'primary_pool_count': len(self.primary_pool),
            'secondary_pool_count': len(self.secondary_pool),
            'position_count': len(self.positions)
        })
    
    def _print_daily_status(self):
        """打印每日状态"""
        nav = self.total_asset / self.initial_capital
        date_str = self.current_date.strftime('%Y-%m-%d')

        self._log(f"{date_str} 净值:{nav:.4f} 资产:{self.total_asset:,.0f} 持仓:{len(self.positions)}")
    
    def _generate_final_report(self) -> Dict:
        """生成最终回测报告"""
        self._log("="*60, "SUCCESS")
        self._log("📊 精确回测报告", "SUCCESS")
        self._log("="*60, "SUCCESS")
        
        if not self.daily_nav:
            self._log("无回测数据", "ERROR")
            return {}
        
        # 计算基础统计
        final_nav = self.daily_nav[-1]['nav']
        total_return = (final_nav - 1) * 100
        
        # 统计交易
        open_trades = [log for log in self.trade_log if log['action'] == 'open_position']
        close_trades = [log for log in self.trade_log if log['action'] == 'close_position']
        
        total_trades = len(open_trades)
        completed_trades = len(close_trades)
        
        if completed_trades > 0:
            total_pnl = sum(trade['pnl'] for trade in close_trades)
            win_trades = len([trade for trade in close_trades if trade['pnl'] > 0])
            win_rate = win_trades / completed_trades
            avg_pnl = total_pnl / completed_trades
            max_win = max(trade['pnl'] for trade in close_trades)
            max_loss = min(trade['pnl'] for trade in close_trades)
            avg_hold_days = sum(trade['hold_days'] for trade in close_trades) / completed_trades
        else:
            total_pnl = win_rate = avg_pnl = max_win = max_loss = avg_hold_days = 0
        
        # 计算最大回撤
        navs = [record['nav'] for record in self.daily_nav]
        max_drawdown = self._calculate_max_drawdown(navs)
        
        # 打印报告
        self._log(f"初始资金: {self.initial_capital:,.2f}", "SUCCESS")
        self._log(f"最终资产: {self.total_asset:,.2f}", "SUCCESS")
        self._log(f"最终净值: {final_nav:.4f}", "SUCCESS")
        self._log(f"总收益率: {total_return:.2f}%", "SUCCESS")
        self._log(f"总交易次数: {total_trades}", "SUCCESS")
        self._log(f"完成交易次数: {completed_trades}", "SUCCESS")
        self._log(f"胜率: {win_rate:.2%}", "SUCCESS")
        self._log(f"平均每笔盈亏: {avg_pnl:,.2f}", "SUCCESS")
        self._log(f"最大单笔盈利: {max_win:,.2f}", "SUCCESS")
        self._log(f"最大单笔亏损: {max_loss:,.2f}", "SUCCESS")
        self._log(f"平均持仓天数: {avg_hold_days:.1f}", "SUCCESS")
        self._log(f"最大回撤: {max_drawdown:.2%}", "SUCCESS")
        
        # 生成图表
        self._plot_results()

        # 保存机器学习特征数据
        self._save_ml_features()

        return {
            'initial_capital': self.initial_capital,
            'final_asset': self.total_asset,
            'final_nav': final_nav,
            'total_return': total_return,
            'total_trades': total_trades,
            'completed_trades': completed_trades,
            'win_rate': win_rate,
            'avg_pnl': avg_pnl,
            'max_win': max_win,
            'max_loss': max_loss,
            'avg_hold_days': avg_hold_days,
            'max_drawdown': max_drawdown,
            'daily_nav': self.daily_nav,
            'trade_log': self.trade_log
        }
    
    def _calculate_max_drawdown(self, navs: List[float]) -> float:
        """计算最大回撤"""
        if not navs:
            return 0.0
        
        peak = navs[0]
        max_dd = 0.0
        
        for nav in navs:
            if nav > peak:
                peak = nav
            drawdown = (peak - nav) / peak
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _plot_results(self):
        """绘制回测结果"""
        if not self.daily_nav:
            return
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('精确回测结果', fontsize=16)
        
        dates = [record['date'] for record in self.daily_nav]
        navs = [record['nav'] for record in self.daily_nav]
        
        # 1. 净值曲线
        ax1.plot(dates, navs, 'b-', linewidth=2, label='净值')
        ax1.axhline(y=1.0, color='r', linestyle='--', alpha=0.7, label='基准')
        ax1.set_title('净值曲线')
        ax1.set_ylabel('净值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 资产曲线
        assets = [record['total_asset'] for record in self.daily_nav]
        ax2.plot(dates, assets, 'g-', linewidth=2)
        ax2.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7)
        ax2.set_title('总资产')
        ax2.set_ylabel('资产(元)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 股票池数量
        primary_counts = [record['primary_pool_count'] for record in self.daily_nav]
        secondary_counts = [record['secondary_pool_count'] for record in self.daily_nav]
        position_counts = [record['position_count'] for record in self.daily_nav]
        
        ax3.plot(dates, primary_counts, 'orange', label='一级监控池')
        ax3.plot(dates, secondary_counts, 'purple', label='二级监控池')
        ax3.plot(dates, position_counts, 'red', label='持仓')
        ax3.set_title('股票池数量')
        ax3.set_ylabel('数量')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易盈亏分布
        close_trades = [log for log in self.trade_log if log['action'] == 'close_position']
        if close_trades:
            pnls = [trade['pnl'] for trade in close_trades]
            ax4.hist(pnls, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax4.axvline(x=0, color='r', linestyle='--', alpha=0.7)
            ax4.set_title('交易盈亏分布')
            ax4.set_xlabel('盈亏(元)')
            ax4.set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('precise_backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        self._log("图表已保存为 precise_backtest_results.png", "SUCCESS")

    def _save_ml_features(self):
        """保存机器学习特征数据，按年份分别保存"""
        if not self.ml_features:
            self._log("没有机器学习特征数据", "WARNING")
            return

        try:
            # 转换为DataFrame
            ml_df = pd.DataFrame(self.ml_features)

            # 只保留有标签的完整交易记录
            complete_trades = ml_df[ml_df['label'].notna()].copy()

            if len(complete_trades) == 0:
                self._log("没有完整的交易记录", "WARNING")
                return

            # 添加年份列
            complete_trades['year'] = pd.to_datetime(complete_trades['open_date']).dt.year

            # 按年份分别保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            all_files = []

            for year in sorted(complete_trades['year'].unique()):
                year_data = complete_trades[complete_trades['year'] == year].copy()

                # 保存CSV文件
                csv_filename = f'ml_features_{year}_{timestamp}.csv'
                year_data.to_csv(csv_filename, index=False, encoding='utf-8-sig')

                # 保存Parquet文件
                parquet_filename = f'ml_features_{year}_{timestamp}.parquet'
                year_data.to_parquet(parquet_filename, index=False)

                all_files.extend([csv_filename, parquet_filename])

                # 统计该年数据
                win_records = year_data[year_data['label'] == 1]
                loss_records = year_data[year_data['label'] == -1]
                win_rate = len(win_records) / len(year_data) if len(year_data) > 0 else 0

                self._log(f"{year}年数据: {len(year_data)} 条交易", "SUCCESS")
                self._log(f"  盈利: {len(win_records)} 条, 亏损: {len(loss_records)} 条", "SUCCESS")
                self._log(f"  胜率: {win_rate:.2%}", "SUCCESS")
                self._log(f"  文件: {csv_filename}, {parquet_filename}", "SUCCESS")

            # 保存完整数据集
            complete_csv = f'ml_features_complete_{timestamp}.csv'
            complete_parquet = f'ml_features_complete_{timestamp}.parquet'
            complete_trades.to_csv(complete_csv, index=False, encoding='utf-8-sig')
            complete_trades.to_parquet(complete_parquet, index=False)

            self._log(f"完整数据集已保存:", "SUCCESS")
            self._log(f"  总交易记录: {len(complete_trades)} 条", "SUCCESS")
            self._log(f"  时间跨度: {complete_trades['year'].min()}-{complete_trades['year'].max()}", "SUCCESS")
            self._log(f"  特征维度: {len([col for col in complete_trades.columns if col not in ['code', 'open_date', 'year']])} 个", "SUCCESS")
            self._log(f"  完整文件: {complete_csv}, {complete_parquet}", "SUCCESS")

            # 总体统计
            total_wins = len(complete_trades[complete_trades['label'] == 1])
            total_losses = len(complete_trades[complete_trades['label'] == -1])
            overall_win_rate = total_wins / len(complete_trades) if len(complete_trades) > 0 else 0

            self._log(f"总体统计:", "SUCCESS")
            self._log(f"  总盈利交易: {total_wins} 条", "SUCCESS")
            self._log(f"  总亏损交易: {total_losses} 条", "SUCCESS")
            self._log(f"  总体胜率: {overall_win_rate:.2%}", "SUCCESS")

        except Exception as e:
            self._log(f"保存机器学习特征数据失败: {e}", "ERROR")

def main():
    """主函数"""
    # 初始化策略
    strategy = LimitUpReversalStrategy(
        data_path=r"D:\git\czsc-new\stock_data",
        initial_capital=1000000,
        max_positions=10
    )

    # 初始化精确回测引擎
    backtest = PreciseBacktestEngine(strategy)

    # 运行全市场回测（2021-2025年完整数据）
    results = backtest.run_backtest(
        start_date="2021-01-01",
        end_date="2025-07-17"  # 全市场完整回测
    )

    backtest._log("✅ 精确回测完成！", "SUCCESS")

if __name__ == "__main__":
    main()
