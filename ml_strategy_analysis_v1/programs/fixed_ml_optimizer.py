#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版机器学习策略优化器
解决数据泄露问题，严格使用只有开仓前可获得的特征
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report
import lightgbm as lgb

class FixedMLOptimizer:
    """修复版机器学习优化器"""
    
    def __init__(self):
        self.models = {}
        self.feature_importance = {}
        
    def load_data(self, file_path):
        """加载数据"""
        try:
            if file_path.endswith('.parquet'):
                data = pd.read_parquet(file_path)
            else:
                data = pd.read_csv(file_path, encoding='utf-8-sig')
            
            print(f"✅ 成功加载数据: {len(data)} 条记录")
            
            # 添加年份列
            if 'year' not in data.columns:
                data['year'] = pd.to_datetime(data['open_date']).dt.year
            
            print(f"📅 时间跨度: {data['year'].min()}-{data['year'].max()}")
            print(f"📊 特征维度: {len(data.columns)} 个")
            
            return data
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def prepare_features_fixed(self, data):
        """修复版特征准备 - 严格排除未来信息"""
        print("🔧 使用修复版特征工程（排除数据泄露）")
        
        # 严格排除所有可能包含未来信息的列
        exclude_cols = [
            # 基础信息列
            'code', 'open_date', 'open_time', 'close_date', 'close_time', 'year',
            # 标签和结果列
            'label', 'trade_result', 'action',
            # 交易执行信息（这些在开仓前不可知）
            'stop_loss', 'take_profit', 'shares', 'actual_cost',
            # 交易结果信息（未来信息）
            'exit_price', 'pnl', 'pnl_pct', 'hold_days',
            # 原因和状态列
            'signal_reason', 'exit_reason', 'enter_reason', 'candidate_status',
            # 时间相关
            'bi_start_time', 'bi_end_time'
        ]
        
        # 只保留开仓前可获得的特征
        valid_feature_prefixes = [
            'prev_',           # 前一日的技术指标
            'current_5min_',   # 当前5分钟技术指标
            'has_down_bi',     # 缠论特征（开仓前可知）
            'price_vs_bi',     # 价格相对位置（开仓前可知）
            'bi_1_', 'bi_2_', 'bi_3_', 'bi_4_', 'bi_5_',  # 历史笔特征
            'duan_1_', 'duan_2_', 'duan_3_',              # 历史段特征
            'zhongshu_',       # 中枢特征
            'limit_up_',       # 涨停相关特征
            'signal_price'     # 信号价格（开仓前已知）
        ]
        
        # 筛选有效特征
        feature_cols = []
        for col in data.columns:
            if col not in exclude_cols:
                # 检查是否是有效的特征前缀
                is_valid = any(col.startswith(prefix) for prefix in valid_feature_prefixes)
                if is_valid:
                    feature_cols.append(col)
                else:
                    print(f"⚠️  排除可疑特征: {col}")
        
        print(f"📊 修复后特征统计:")
        print(f"  原始特征数: {len(data.columns)}")
        print(f"  排除特征数: {len(data.columns) - len(feature_cols)}")
        print(f"  有效特征数: {len(feature_cols)}")
        
        # 提取特征和标签
        X = data[feature_cols].copy()
        y = data['label'].copy()
        
        # 处理数据类型
        for col in X.columns:
            if X[col].dtype == 'object':
                try:
                    X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    print(f"⚠️ 删除非数值列: {col}")
                    X = X.drop(columns=[col])
                    feature_cols.remove(col)
        
        # 处理缺失值和异常值
        X = X.fillna(0)
        X = X.replace([np.inf, -np.inf], 0)
        
        print(f"📊 最终特征准备:")
        print(f"  特征数量: {len(feature_cols)}")
        print(f"  样本数量: {len(X)}")
        print(f"  正样本: {sum(y == 1)} 条 ({sum(y == 1)/len(y):.2%})")
        print(f"  负样本: {sum(y == -1)} 条 ({sum(y == -1)/len(y):.2%})")
        
        # 检查样本量与特征数比例
        ratio = len(X) / len(feature_cols)
        print(f"  样本/特征比例: {ratio:.2f}")
        
        if ratio < 5:
            print("❌ 样本量严重不足，建议增加数据或减少特征")
        elif ratio < 10:
            print("⚠️  样本量偏少，可能存在过拟合风险")
        else:
            print("✅ 样本量充足")
        
        return X, y, feature_cols
    
    def train_models_fixed(self, X_train, y_train, feature_cols):
        """训练修复版模型"""
        print("\n🤖 开始训练修复版机器学习模型...")
        
        # 定义模型（使用更保守的参数防止过拟合）
        models_config = {
            'RandomForest': RandomForestClassifier(
                n_estimators=50,      # 减少树的数量
                max_depth=5,          # 限制树的深度
                min_samples_split=20, # 增加分割所需的最小样本数
                min_samples_leaf=10,  # 增加叶节点最小样本数
                random_state=42
            ),
            'GradientBoosting': GradientBoostingClassifier(
                n_estimators=50,
                max_depth=3,
                learning_rate=0.1,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42
            ),
            'LightGBM': lgb.LGBMClassifier(
                n_estimators=50,
                max_depth=3,
                learning_rate=0.1,
                min_child_samples=20,
                random_state=42,
                verbose=-1
            ),
            'LogisticRegression': LogisticRegression(
                C=1.0,                # 增加正则化
                max_iter=1000,
                random_state=42
            )
        }
        
        # 使用更严格的交叉验证
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        for name, model in models_config.items():
            try:
                print(f"  训练 {name}...")
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='accuracy')
                
                # 训练模型
                model.fit(X_train, y_train)
                self.models[name] = model
                
                print(f"    ✅ {name} 训练完成")
                print(f"    📊 交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
                
                # 保存特征重要性
                if hasattr(model, 'feature_importances_'):
                    importance_df = pd.DataFrame({
                        'feature': feature_cols,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    self.feature_importance[name] = importance_df
                
            except Exception as e:
                print(f"    ❌ {name} 训练失败: {e}")
        
        print(f"✅ 修复版模型训练完成，成功训练 {len(self.models)} 个模型")
    
    def evaluate_models_fixed(self, X_test, y_test, test_data, year):
        """评估修复版模型"""
        print(f"\n📊 评估 {year} 年修复版模型性能...")
        
        for name, model in self.models.items():
            try:
                # 预测
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
                
                # 计算准确率
                accuracy = accuracy_score(y_test, y_pred)
                
                # 统计预测结果
                pred_positive = sum(y_pred == 1)
                actual_positive = sum(y_test == 1)
                
                print(f"  {name}:")
                print(f"    准确率: {accuracy:.4f}")
                print(f"    预测正样本: {pred_positive} 个")
                print(f"    实际正样本: {actual_positive} 个")
                
                if pred_positive > 0:
                    # 计算预测为正样本的实际胜率
                    predicted_positive_mask = y_pred == 1
                    actual_win_rate = sum(y_test[predicted_positive_mask] == 1) / pred_positive
                    print(f"    预测正样本实际胜率: {actual_win_rate:.4f}")
                
            except Exception as e:
                print(f"    ❌ {name} 评估失败: {e}")

def main():
    """主函数"""
    optimizer = FixedMLOptimizer()
    
    # 查找数据文件
    import glob
    import os
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(os.path.dirname(current_dir), 'data')
    
    data_files = glob.glob(os.path.join(data_dir, 'ml_features_complete_*.parquet'))
    if not data_files:
        data_files = glob.glob(os.path.join(data_dir, 'ml_features_complete_*.csv'))
    
    if not data_files:
        print("❌ 未找到数据文件")
        return
    
    latest_file = max(data_files, key=os.path.getctime)
    print(f"📁 使用数据文件: {latest_file}")
    
    # 加载数据
    data = optimizer.load_data(latest_file)
    if data is None:
        return
    
    # 使用修复版特征准备
    X, y, feature_cols = optimizer.prepare_features_fixed(data)
    
    # 按年份分割数据
    train_years = [2021, 2022, 2023]
    test_years = [2024, 2025]
    
    # 准备训练数据
    train_mask = data['year'].isin(train_years)
    X_train = X[train_mask]
    y_train = y[train_mask]
    
    print(f"\n📚 训练数据 ({'-'.join(map(str, train_years))}):")
    print(f"  样本数量: {len(X_train)}")
    print(f"  正样本: {sum(y_train == 1)} 条 ({sum(y_train == 1)/len(y_train):.2%})")
    print(f"  负样本: {sum(y_train == -1)} 条 ({sum(y_train == -1)/len(y_train):.2%})")
    
    # 训练修复版模型
    optimizer.train_models_fixed(X_train, y_train, feature_cols)
    
    # 测试每一年
    for test_year in test_years:
        if test_year in data['year'].values:
            test_mask = data['year'] == test_year
            X_test = X[test_mask]
            y_test = y[test_mask]
            test_data = data[test_mask]
            
            print(f"\n📊 测试数据 ({test_year} 年):")
            print(f"  样本数量: {len(X_test)}")
            print(f"  正样本: {sum(y_test == 1)} 条 ({sum(y_test == 1)/len(y_test):.2%})")
            print(f"  负样本: {sum(y_test == -1)} 条 ({sum(y_test == -1)/len(y_test):.2%})")
            
            # 评估修复版模型
            optimizer.evaluate_models_fixed(X_test, y_test, test_data, test_year)
    
    print("\n🎉 修复版机器学习分析完成！")
    print("📊 现在的结果应该更加真实可靠")

if __name__ == "__main__":
    main()
