#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成大规模机器学习数据集
目标：生成5000+条高质量的交易记录用于机器学习训练

不依赖数据库，使用模拟数据但保持真实的统计特性
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class LargeMLDatasetGenerator:
    """大规模机器学习数据集生成器"""
    
    def __init__(self):
        self.target_samples = 5000  # 目标样本数
        self.features_count = 150   # 特征数量
        
        # 设置随机种子以确保可重复性
        np.random.seed(42)
        
    def generate_large_dataset(self):
        """生成大规模数据集"""
        print("🚀 开始生成大规模机器学习数据集")
        print(f"🎯 目标样本数: {self.target_samples} 条")
        print(f"📊 特征维度: {self.features_count} 个")
        print("=" * 60)
        
        all_data = []
        
        # 按年份生成数据
        years = [2020, 2021, 2022, 2023, 2024, 2025]
        samples_per_year = self.target_samples // len(years)
        
        for year in years:
            print(f"📅 生成 {year} 年数据...")
            year_data = self._generate_year_data(year, samples_per_year)
            all_data.extend(year_data)
            print(f"✅ {year} 年完成: {len(year_data)} 条记录")
        
        # 转换为DataFrame
        df = pd.DataFrame(all_data)
        
        print("\n" + "=" * 60)
        print(f"✅ 数据生成完成!")
        print(f"📊 总记录数: {len(df)} 条")
        print(f"📊 特征维度: {len(df.columns)} 个")
        
        # 保存数据
        self._save_dataset(df)
        
        # 数据质量报告
        self._generate_data_report(df)
        
        return df
    
    def _generate_year_data(self, year: int, target_samples: int) -> list:
        """生成指定年份的数据"""
        year_data = []
        
        # 根据年份调整市场环境
        market_conditions = self._get_market_conditions(year)
        
        for i in range(target_samples):
            # 生成随机日期
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            random_days = np.random.randint(0, (end_date - start_date).days)
            trade_date = start_date + timedelta(days=random_days)
            
            # 跳过周末
            if trade_date.weekday() >= 5:
                trade_date -= timedelta(days=trade_date.weekday() - 4)
            
            # 生成交易记录
            record = self._generate_trade_record(trade_date, market_conditions)
            year_data.append(record)
        
        return year_data
    
    def _get_market_conditions(self, year: int) -> dict:
        """获取年份对应的市场环境"""
        conditions = {
            2020: {'volatility': 0.8, 'trend': 0.3, 'win_rate_base': 0.45},  # 疫情年，高波动
            2021: {'volatility': 0.6, 'trend': 0.7, 'win_rate_base': 0.40},  # 牛市
            2022: {'volatility': 0.9, 'trend': -0.3, 'win_rate_base': 0.25}, # 熊市
            2023: {'volatility': 0.5, 'trend': 0.2, 'win_rate_base': 0.35},  # 震荡市
            2024: {'volatility': 0.6, 'trend': 0.1, 'win_rate_base': 0.30},  # 平稳市
            2025: {'volatility': 0.7, 'trend': 0.0, 'win_rate_base': 0.32},  # 当前市场
        }
        return conditions.get(year, {'volatility': 0.6, 'trend': 0.0, 'win_rate_base': 0.35})
    
    def _generate_trade_record(self, trade_date: datetime, market_conditions: dict) -> dict:
        """生成单条交易记录"""
        record = {}
        
        # 基础信息
        record['code'] = f"{np.random.randint(0, 999999):06d}"
        record['open_date'] = trade_date.strftime('%Y-%m-%d')
        record['open_time'] = trade_date.strftime('%Y-%m-%d %H:%M:%S')
        record['year'] = trade_date.year
        
        # 生成各类特征
        record.update(self._generate_daily_technical_features(market_conditions))
        record.update(self._generate_min5_technical_features(market_conditions))
        record.update(self._generate_chanlun_features(market_conditions))
        record.update(self._generate_market_features(market_conditions))
        record.update(self._generate_trade_execution_features())
        
        # 生成交易结果（基于特征的复杂模型）
        trade_result = self._generate_trade_result(record, market_conditions)
        record.update(trade_result)
        
        return record
    
    def _generate_daily_technical_features(self, market_conditions: dict) -> dict:
        """生成日线技术指标特征"""
        volatility = market_conditions['volatility']
        
        features = {}
        
        # 基础价格指标
        base_price = np.random.uniform(5, 100)
        features['prev_daily_close'] = base_price
        features['prev_daily_open'] = base_price * np.random.uniform(0.98, 1.02)
        features['prev_daily_high'] = base_price * np.random.uniform(1.0, 1.05)
        features['prev_daily_low'] = base_price * np.random.uniform(0.95, 1.0)
        features['prev_daily_volume'] = np.random.lognormal(15, 1)  # 对数正态分布
        
        # K线形态
        features['prev_daily_body'] = abs(features['prev_daily_close'] - features['prev_daily_open']) / features['prev_daily_open']
        features['prev_daily_upper_shadow'] = (features['prev_daily_high'] - max(features['prev_daily_open'], features['prev_daily_close'])) / features['prev_daily_open']
        features['prev_daily_lower_shadow'] = (min(features['prev_daily_open'], features['prev_daily_close']) - features['prev_daily_low']) / features['prev_daily_open']
        features['prev_daily_is_red'] = 1 if features['prev_daily_close'] > features['prev_daily_open'] else 0
        
        # 移动平均线
        ma_base = base_price * np.random.uniform(0.95, 1.05)
        features['prev_ma5'] = ma_base * np.random.uniform(0.98, 1.02)
        features['prev_ma10'] = ma_base * np.random.uniform(0.96, 1.04)
        features['prev_ma20'] = ma_base * np.random.uniform(0.94, 1.06)
        features['prev_ma30'] = ma_base * np.random.uniform(0.92, 1.08)
        
        # 价格相对位置
        features['prev_close_vs_ma5'] = (features['prev_daily_close'] - features['prev_ma5']) / features['prev_ma5']
        features['prev_close_vs_ma10'] = (features['prev_daily_close'] - features['prev_ma10']) / features['prev_ma10']
        features['prev_close_vs_ma20'] = (features['prev_daily_close'] - features['prev_ma20']) / features['prev_ma20']
        features['prev_close_vs_ma30'] = (features['prev_daily_close'] - features['prev_ma30']) / features['prev_ma30']
        
        # 成交量指标
        features['prev_volume_ma5'] = features['prev_daily_volume'] * np.random.uniform(0.8, 1.2)
        features['prev_volume_ma10'] = features['prev_daily_volume'] * np.random.uniform(0.7, 1.3)
        features['prev_volume_ratio'] = features['prev_daily_volume'] / features['prev_volume_ma5']
        features['prev_volume_ratio_10'] = features['prev_daily_volume'] / features['prev_volume_ma10']
        
        # 波动率（受市场环境影响）
        base_volatility = 0.02 * volatility
        features['prev_volatility_5d'] = base_volatility * np.random.uniform(0.5, 1.5)
        features['prev_volatility_10d'] = base_volatility * np.random.uniform(0.6, 1.4)
        features['prev_volatility_20d'] = base_volatility * np.random.uniform(0.7, 1.3)
        
        # 价格区间
        features['prev_high_5d'] = base_price * np.random.uniform(1.0, 1.08)
        features['prev_low_5d'] = base_price * np.random.uniform(0.92, 1.0)
        features['prev_high_10d'] = base_price * np.random.uniform(1.0, 1.12)
        features['prev_low_10d'] = base_price * np.random.uniform(0.88, 1.0)
        features['prev_high_20d'] = base_price * np.random.uniform(1.0, 1.20)
        features['prev_low_20d'] = base_price * np.random.uniform(0.80, 1.0)
        
        # 价格位置百分比
        features['prev_price_position_5d'] = (features['prev_daily_close'] - features['prev_low_5d']) / (features['prev_high_5d'] - features['prev_low_5d']) if features['prev_high_5d'] != features['prev_low_5d'] else 0.5
        features['prev_price_position_10d'] = (features['prev_daily_close'] - features['prev_low_10d']) / (features['prev_high_10d'] - features['prev_low_10d']) if features['prev_high_10d'] != features['prev_low_10d'] else 0.5
        features['prev_price_position_20d'] = (features['prev_daily_close'] - features['prev_low_20d']) / (features['prev_high_20d'] - features['prev_low_20d']) if features['prev_high_20d'] != features['prev_low_20d'] else 0.5
        
        # 技术指标
        features['prev_rsi_14'] = np.random.uniform(20, 80)
        features['prev_macd'] = np.random.uniform(-2, 2)
        features['prev_macd_signal'] = features['prev_macd'] * np.random.uniform(0.8, 1.2)
        features['prev_macd_hist'] = features['prev_macd'] - features['prev_macd_signal']
        
        # 涨跌幅
        features['prev_daily_return'] = np.random.normal(0, base_volatility)
        
        return features
    
    def _generate_min5_technical_features(self, market_conditions: dict) -> dict:
        """生成5分钟技术指标特征"""
        volatility = market_conditions['volatility']
        
        features = {}
        
        # 基础价格指标
        base_price = np.random.uniform(5, 100)
        features['current_5min_close'] = base_price
        features['current_5min_open'] = base_price * np.random.uniform(0.995, 1.005)
        features['current_5min_high'] = base_price * np.random.uniform(1.0, 1.02)
        features['current_5min_low'] = base_price * np.random.uniform(0.98, 1.0)
        features['current_5min_volume'] = np.random.lognormal(12, 1)
        
        # K线形态
        features['current_5min_body'] = abs(features['current_5min_close'] - features['current_5min_open']) / features['current_5min_open']
        features['current_5min_upper_shadow'] = (features['current_5min_high'] - max(features['current_5min_open'], features['current_5min_close'])) / features['current_5min_open']
        features['current_5min_lower_shadow'] = (min(features['current_5min_open'], features['current_5min_close']) - features['current_5min_low']) / features['current_5min_open']
        features['current_5min_is_red'] = 1 if features['current_5min_close'] > features['current_5min_open'] else 0
        
        # 移动平均线
        ma_base = base_price * np.random.uniform(0.98, 1.02)
        features['current_5min_ma5'] = ma_base * np.random.uniform(0.99, 1.01)
        features['current_5min_ma10'] = ma_base * np.random.uniform(0.98, 1.02)
        features['current_5min_ma20'] = ma_base * np.random.uniform(0.97, 1.03)
        features['current_5min_ma30'] = ma_base * np.random.uniform(0.96, 1.04)
        
        # 价格相对位置
        features['current_5min_close_vs_ma5'] = (features['current_5min_close'] - features['current_5min_ma5']) / features['current_5min_ma5']
        features['current_5min_close_vs_ma10'] = (features['current_5min_close'] - features['current_5min_ma10']) / features['current_5min_ma10']
        features['current_5min_close_vs_ma20'] = (features['current_5min_close'] - features['current_5min_ma20']) / features['current_5min_ma20']
        features['current_5min_close_vs_ma30'] = (features['current_5min_close'] - features['current_5min_ma30']) / features['current_5min_ma30']
        
        # 成交量指标
        features['current_5min_volume_ma5'] = features['current_5min_volume'] * np.random.uniform(0.8, 1.2)
        features['current_5min_volume_ma10'] = features['current_5min_volume'] * np.random.uniform(0.7, 1.3)
        features['current_5min_volume_ratio'] = features['current_5min_volume'] / features['current_5min_volume_ma5']
        features['current_5min_volume_ratio_10'] = features['current_5min_volume'] / features['current_5min_volume_ma10']
        
        # 波动率
        base_volatility_5min = 0.01 * volatility
        features['current_5min_volatility_10'] = base_volatility_5min * np.random.uniform(0.5, 1.5)
        features['current_5min_volatility_20'] = base_volatility_5min * np.random.uniform(0.6, 1.4)
        
        # 价格区间
        features['current_5min_high_10'] = base_price * np.random.uniform(1.0, 1.03)
        features['current_5min_low_10'] = base_price * np.random.uniform(0.97, 1.0)
        features['current_5min_high_20'] = base_price * np.random.uniform(1.0, 1.05)
        features['current_5min_low_20'] = base_price * np.random.uniform(0.95, 1.0)
        features['current_5min_high_30'] = base_price * np.random.uniform(1.0, 1.07)
        features['current_5min_low_30'] = base_price * np.random.uniform(0.93, 1.0)
        
        # 价格位置百分比
        features['current_5min_position_10'] = (features['current_5min_close'] - features['current_5min_low_10']) / (features['current_5min_high_10'] - features['current_5min_low_10']) if features['current_5min_high_10'] != features['current_5min_low_10'] else 0.5
        features['current_5min_position_20'] = (features['current_5min_close'] - features['current_5min_low_20']) / (features['current_5min_high_20'] - features['current_5min_low_20']) if features['current_5min_high_20'] != features['current_5min_low_20'] else 0.5
        features['current_5min_position_30'] = (features['current_5min_close'] - features['current_5min_low_30']) / (features['current_5min_high_30'] - features['current_5min_low_30']) if features['current_5min_high_30'] != features['current_5min_low_30'] else 0.5
        
        # 技术指标
        features['current_5min_rsi_14'] = np.random.uniform(20, 80)
        features['current_5min_macd'] = np.random.uniform(-1, 1)
        features['current_5min_macd_signal'] = features['current_5min_macd'] * np.random.uniform(0.8, 1.2)
        features['current_5min_macd_hist'] = features['current_5min_macd'] - features['current_5min_macd_signal']
        
        return features
