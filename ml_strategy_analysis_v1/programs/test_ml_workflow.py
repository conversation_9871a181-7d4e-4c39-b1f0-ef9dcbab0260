#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试机器学习工作流程
使用模拟数据验证整个流程是否正确
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关库
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def generate_mock_data():
    """生成模拟的机器学习数据"""
    np.random.seed(42)
    
    # 生成2021-2025年的模拟数据
    data = []
    
    for year in range(2021, 2026):
        # 每年生成100-200笔交易
        n_trades = np.random.randint(100, 201)
        
        for i in range(n_trades):
            # 生成随机日期
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            random_date = start_date + timedelta(days=np.random.randint(0, (end_date - start_date).days))
            
            # 生成特征数据
            record = {
                'code': f'{np.random.randint(0, 999999):06d}',
                'open_date': random_date.strftime('%Y-%m-%d'),
                'year': year,
                
                # 前一日日线指标
                'prev_daily_close': np.random.uniform(5, 100),
                'prev_daily_volume': np.random.uniform(1000000, 100000000),
                'prev_ma5': np.random.uniform(5, 100),
                'prev_ma10': np.random.uniform(5, 100),
                'prev_ma20': np.random.uniform(5, 100),
                'prev_close_vs_ma5': np.random.uniform(-0.1, 0.1),
                'prev_close_vs_ma10': np.random.uniform(-0.1, 0.1),
                'prev_close_vs_ma20': np.random.uniform(-0.1, 0.1),
                'prev_volume_ratio': np.random.uniform(0.5, 3.0),
                'prev_daily_volatility': np.random.uniform(0.01, 0.05),
                'prev_rsi_14': np.random.uniform(20, 80),
                'prev_macd': np.random.uniform(-1, 1),
                
                # 5分钟指标
                'current_5min_close': np.random.uniform(5, 100),
                'current_5min_volume': np.random.uniform(10000, 1000000),
                'current_5min_ma10': np.random.uniform(5, 100),
                'current_5min_ma20': np.random.uniform(5, 100),
                'current_5min_close_vs_ma10': np.random.uniform(-0.05, 0.05),
                'current_5min_close_vs_ma20': np.random.uniform(-0.05, 0.05),
                'current_5min_volatility_10': np.random.uniform(0.005, 0.02),
                'current_5min_rsi_14': np.random.uniform(20, 80),
                
                # 缠论特征
                'limit_up_count': np.random.randint(1, 6),
                'days_since_limit_up': np.random.randint(1, 15),
                'has_down_bi': np.random.choice([0, 1]),
                'price_vs_bi_low': np.random.uniform(0, 0.2),
                'bi_1_strength': np.random.uniform(0, 0.1),
                'bi_2_strength': np.random.uniform(0, 0.1),
                'zhongshu_strength': np.random.uniform(0, 1),
                'price_vs_zhongshu': np.random.uniform(-1, 1),
                
                # 交易信息
                'signal_price': np.random.uniform(5, 100),
                'pnl_pct': np.random.uniform(-0.15, 0.25),  # -15%到+25%
                'hold_days': np.random.randint(1, 10),
            }
            
            # 生成标签（基于一些简单规则，模拟真实情况）
            # 这里用一些特征的组合来决定盈亏，模拟真实的市场规律
            score = (
                (record['prev_close_vs_ma5'] > 0) * 0.3 +
                (record['current_5min_close_vs_ma10'] > 0) * 0.2 +
                (record['prev_rsi_14'] < 70) * 0.2 +
                (record['limit_up_count'] >= 3) * 0.2 +
                (record['has_down_bi'] == 1) * 0.1
            )
            
            # 添加随机性
            score += np.random.uniform(-0.3, 0.3)
            
            # 根据得分决定盈亏，大约40%胜率
            if score > 0.5:
                record['label'] = 1
                record['trade_result'] = 'win'
                # 盈利交易的收益率偏正
                record['pnl_pct'] = abs(record['pnl_pct'])
            else:
                record['label'] = -1
                record['trade_result'] = 'loss'
                # 亏损交易的收益率偏负
                record['pnl_pct'] = -abs(record['pnl_pct'])
            
            data.append(record)
    
    return pd.DataFrame(data)

def test_ml_workflow():
    """测试机器学习工作流程"""
    print("🧪 测试机器学习工作流程")
    print("=" * 50)
    
    # 生成模拟数据
    print("📊 生成模拟数据...")
    data = generate_mock_data()
    print(f"✅ 生成 {len(data)} 条模拟交易记录")
    print(f"📅 时间跨度: {data['year'].min()}-{data['year'].max()}")
    
    # 统计各年数据
    for year in sorted(data['year'].unique()):
        year_data = data[data['year'] == year]
        win_rate = sum(year_data['label'] == 1) / len(year_data)
        print(f"  {year}年: {len(year_data)} 条交易, 胜率: {win_rate:.2%}")
    
    # 准备特征
    print("\n🔧 准备特征数据...")
    exclude_cols = ['code', 'open_date', 'year', 'label', 'trade_result', 'pnl_pct']
    feature_cols = [col for col in data.columns if col not in exclude_cols]
    
    X = data[feature_cols].fillna(0)
    y = data['label']
    
    print(f"✅ 特征准备完成: {len(feature_cols)} 个特征")
    
    # 分割训练和测试数据
    train_years = [2021, 2022, 2023]
    test_years = [2024, 2025]
    
    train_mask = data['year'].isin(train_years)
    X_train, y_train = X[train_mask], y[train_mask]
    train_data = data[train_mask]
    
    print(f"\n📚 训练数据: {len(X_train)} 条")
    print(f"  胜率: {sum(y_train == 1) / len(y_train):.2%}")
    
    # 训练模型
    print("\n🤖 训练机器学习模型...")
    models = {
        'RandomForest': RandomForestClassifier(n_estimators=50, random_state=42),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=50, random_state=42),
        'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
    }
    
    trained_models = {}
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    for name, model in models.items():
        print(f"  训练 {name}...")
        if name == 'LogisticRegression':
            model.fit(X_train_scaled, y_train)
        else:
            model.fit(X_train, y_train)
        trained_models[name] = model
        print(f"    ✅ {name} 训练完成")
    
    # 测试模型
    print("\n📊 测试模型性能...")
    
    for test_year in test_years:
        if test_year in data['year'].values:
            test_mask = data['year'] == test_year
            X_test, y_test = X[test_mask], y[test_mask]
            test_data = data[test_mask]
            
            print(f"\n📅 {test_year} 年测试结果:")
            print(f"  原策略: {len(test_data)} 笔交易, 胜率: {sum(y_test == 1) / len(y_test):.2%}")
            
            for name, model in trained_models.items():
                # 预测
                if name == 'LogisticRegression':
                    X_test_scaled = scaler.transform(X_test)
                    y_pred = model.predict(X_test_scaled)
                else:
                    y_pred = model.predict(X_test)
                
                # 只选择预测为正的交易
                positive_mask = y_pred == 1
                selected_trades = test_data[positive_mask]
                
                if len(selected_trades) > 0:
                    selected_wins = sum(selected_trades['label'] == 1)
                    selected_win_rate = selected_wins / len(selected_trades)
                    avg_pnl = selected_trades['pnl_pct'].mean()
                    
                    print(f"  {name}:")
                    print(f"    选择: {len(selected_trades)} 笔 ({len(selected_trades)/len(test_data):.1%})")
                    print(f"    胜率: {selected_win_rate:.2%}")
                    print(f"    平均收益: {avg_pnl:.2%}")
                else:
                    print(f"  {name}: 未选择任何交易")
    
    print("\n🎉 机器学习工作流程测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    test_ml_workflow()
