#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
V1版本实盘验证回测引擎
使用训练好的机器学习模型对2024-2025年数据进行实盘验证
"""

import sys
import os
import pandas as pd
import numpy as np
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录

# 不导入chanlun模块，避免数据库连接问题
# from chanlun.backtesting.base import *
# from chanlun.cl_interface import *

class LiveValidationBacktest:
    """实盘验证回测引擎"""
    
    def __init__(self):
        self.initial_capital = 1000000  # 100万初始资金
        self.max_positions = 10         # 最大持仓10个股票
        self.position_size = 100000     # 每个仓位10万（均仓）
        
        # 模型预测阈值
        self.prediction_threshold = 0.7  # 预测概率超过70%才执行
        
        # 回测状态
        self.current_capital = self.initial_capital
        self.positions = {}  # 当前持仓 {code: position_info}
        self.trade_history = []  # 交易历史
        self.daily_capital = []  # 每日资金记录
        self.max_positions_held = 0  # 最大持仓股票数
        
        # 加载训练好的模型
        self.models = {}
        self.feature_columns = []
        self.load_trained_models()
        
        # 加载历史数据用于特征计算
        self.load_historical_data()
    
    def load_trained_models(self):
        """加载训练好的机器学习模型"""
        print("📦 加载训练好的机器学习模型...")
        
        try:
            # 从V1版本的机器学习优化器中加载模型
            # 这里需要重新训练或加载已保存的模型
            sys.path.append(current_dir)
            from ml_strategy_optimizer import MLStrategyOptimizer
            
            optimizer = MLStrategyOptimizer()
            
            # 加载数据
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            data_files = [f for f in os.listdir(data_dir) if f.startswith('ml_features_complete_') and f.endswith('.parquet')]
            
            if not data_files:
                raise Exception("没有找到完整的特征数据文件")
            
            latest_file = max(data_files, key=lambda x: os.path.getctime(os.path.join(data_dir, x)))
            data_path = os.path.join(data_dir, latest_file)
            
            print(f"📁 使用数据文件: {data_path}")
            
            # 加载数据并训练模型
            data = optimizer.load_data(data_path)
            X, y, feature_cols = optimizer.prepare_features(data)
            
            # 只使用2021-2023年数据训练
            train_mask = data['year'].isin([2021, 2022, 2023])
            X_train = X[train_mask]
            y_train = y[train_mask]
            
            # 训练模型
            optimizer.train_models(X_train, y_train, feature_cols)
            
            # 保存模型和特征列
            self.models = optimizer.models
            self.feature_columns = feature_cols
            
            print(f"✅ 成功加载 {len(self.models)} 个模型")
            print(f"📊 特征维度: {len(self.feature_columns)} 个")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def load_historical_data(self):
        """加载历史数据用于特征计算"""
        print("📊 加载历史数据...")
        
        # 这里应该加载股票的历史日线和5分钟数据
        # 由于数据源问题，我们先使用模拟数据
        self.historical_data = {}
        print("✅ 历史数据加载完成")
    
    def get_trading_signals(self, date):
        """获取指定日期的交易信号"""
        signals = []
        
        try:
            # 这里应该扫描所有股票，寻找符合条件的交易信号
            # 由于数据源限制，我们使用已有的特征数据进行模拟
            
            # 加载特征数据
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            data_files = [f for f in os.listdir(data_dir) if f.startswith('ml_features_complete_') and f.endswith('.parquet')]
            latest_file = max(data_files, key=lambda x: os.path.getctime(os.path.join(data_dir, x)))
            data_path = os.path.join(data_dir, latest_file)
            
            data = pd.read_parquet(data_path)
            
            # 筛选指定日期的数据
            date_str = date.strftime('%Y-%m-%d')
            daily_data = data[data['open_date'] == date_str]
            
            if len(daily_data) == 0:
                return signals
            
            print(f"📅 {date_str}: 找到 {len(daily_data)} 个潜在信号")
            
            # 使用模型进行预测
            for _, row in daily_data.iterrows():
                try:
                    # 准备特征数据
                    features = []
                    for col in self.feature_columns:
                        if col in row:
                            features.append(row[col])
                        else:
                            features.append(0)  # 缺失特征用0填充
                    
                    features = np.array(features).reshape(1, -1)
                    
                    # 使用最佳模型进行预测（这里使用RandomForest）
                    if 'RandomForest' in self.models:
                        prediction_proba = self.models['RandomForest'].predict_proba(features)[0]
                        prediction_score = prediction_proba[1] if len(prediction_proba) > 1 else 0
                        
                        # 如果预测概率超过阈值，生成交易信号
                        if prediction_score >= self.prediction_threshold:
                            signal = {
                                'code': row['code'],
                                'date': date,
                                'signal_price': row.get('signal_price', row.get('prev_daily_close', 10)),
                                'prediction_score': prediction_score,
                                'stop_loss': row.get('stop_loss', 0),
                                'take_profit': row.get('take_profit', 0),
                                'reason': f'ML预测概率: {prediction_score:.3f}'
                            }
                            signals.append(signal)
                
                except Exception as e:
                    continue
            
            # 按预测概率排序，选择最好的信号
            signals.sort(key=lambda x: x['prediction_score'], reverse=True)
            
            print(f"🎯 筛选出 {len(signals)} 个高质量信号")
            
        except Exception as e:
            print(f"❌ 获取交易信号失败: {e}")
        
        return signals
    
    def execute_trades(self, date, signals):
        """执行交易"""
        executed_trades = []
        
        # 检查可用资金和仓位
        available_positions = self.max_positions - len(self.positions)
        available_capital = self.current_capital
        
        if available_positions <= 0:
            print(f"📊 {date.strftime('%Y-%m-%d')}: 已满仓，无法开新仓")
            return executed_trades
        
        # 执行开仓
        for signal in signals[:available_positions]:
            if available_capital < self.position_size:
                print(f"💰 资金不足，无法开仓 {signal['code']}")
                break
            
            # 计算实际购买股数
            signal_price = signal['signal_price']
            shares = int(self.position_size / signal_price / 100) * 100  # 整手购买
            actual_cost = shares * signal_price
            
            if actual_cost > available_capital:
                continue
            
            # 开仓
            position = {
                'code': signal['code'],
                'open_date': date,
                'open_price': signal_price,
                'shares': shares,
                'cost': actual_cost,
                'stop_loss': signal['stop_loss'],
                'take_profit': signal['take_profit'],
                'prediction_score': signal['prediction_score'],
                'reason': signal['reason']
            }
            
            self.positions[signal['code']] = position
            self.current_capital -= actual_cost
            available_capital -= actual_cost
            
            # 记录交易
            trade = {
                'date': date,
                'action': 'BUY',
                'code': signal['code'],
                'price': signal_price,
                'shares': shares,
                'amount': actual_cost,
                'reason': signal['reason'],
                'prediction_score': signal['prediction_score']
            }
            
            executed_trades.append(trade)
            self.trade_history.append(trade)
            
            print(f"📈 开仓: {signal['code']} {shares}股 @{signal_price:.2f} 预测:{signal['prediction_score']:.3f}")
        
        # 更新最大持仓数记录
        current_positions = len(self.positions)
        if current_positions > self.max_positions_held:
            self.max_positions_held = current_positions
        
        return executed_trades
    
    def check_exit_conditions(self, date):
        """检查平仓条件"""
        exits = []
        
        for code, position in list(self.positions.items()):
            # 这里应该获取当前价格，由于数据限制，我们模拟价格变化
            # 实际应用中需要获取实时或历史价格数据
            
            # 模拟价格（这里需要替换为真实价格获取逻辑）
            current_price = self.simulate_current_price(position, date)
            
            # 检查止盈止损条件
            should_exit = False
            exit_reason = ""
            
            # 止损检查
            if position['stop_loss'] > 0 and current_price <= position['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            
            # 止盈检查
            elif position['take_profit'] > 0 and current_price >= position['take_profit']:
                should_exit = True
                exit_reason = "止盈"
            
            # 持仓时间检查（超过10个交易日强制平仓）
            elif (date - position['open_date']).days >= 10:
                should_exit = True
                exit_reason = "超时平仓"
            
            if should_exit:
                # 执行平仓
                shares = position['shares']
                sell_amount = shares * current_price
                pnl = sell_amount - position['cost']
                pnl_pct = pnl / position['cost']
                
                # 更新资金
                self.current_capital += sell_amount
                
                # 记录交易
                trade = {
                    'date': date,
                    'action': 'SELL',
                    'code': code,
                    'price': current_price,
                    'shares': shares,
                    'amount': sell_amount,
                    'pnl': pnl,
                    'pnl_pct': pnl_pct,
                    'reason': exit_reason,
                    'hold_days': (date - position['open_date']).days
                }
                
                exits.append(trade)
                self.trade_history.append(trade)
                
                # 移除持仓
                del self.positions[code]
                
                print(f"📉 平仓: {code} {shares}股 @{current_price:.2f} 盈亏:{pnl:.2f}({pnl_pct:.2%}) {exit_reason}")
        
        return exits
    
    def simulate_current_price(self, position, current_date):
        """模拟当前价格（实际应用中需要获取真实价格）"""
        # 这里使用简单的随机游走模拟价格变化
        # 实际应用中应该从数据源获取真实价格
        
        days_held = (current_date - position['open_date']).days
        open_price = position['open_price']
        
        # 模拟价格变化（基于正态分布）
        daily_return = np.random.normal(0.001, 0.03)  # 日均收益率0.1%，波动率3%
        price_change = open_price * daily_return * days_held
        
        current_price = max(open_price + price_change, open_price * 0.8)  # 最大跌幅20%
        
        return current_price
    
    def update_daily_capital(self, date):
        """更新每日资金记录"""
        # 计算持仓市值
        position_value = 0
        for code, position in self.positions.items():
            current_price = self.simulate_current_price(position, date)
            position_value += position['shares'] * current_price
        
        total_capital = self.current_capital + position_value
        
        self.daily_capital.append({
            'date': date,
            'cash': self.current_capital,
            'position_value': position_value,
            'total_capital': total_capital,
            'positions_count': len(self.positions)
        })
    
    def run_validation_backtest(self, start_date='2024-01-01', end_date='2025-07-18'):
        """运行验证回测"""
        print("🚀 开始V1版本实盘验证回测")
        print("=" * 60)
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"💰 初始资金: {self.initial_capital:,} 元")
        print(f"📊 最大持仓: {self.max_positions} 个股票")
        print(f"🎯 预测阈值: {self.prediction_threshold}")
        print("=" * 60)
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        trading_days = 0
        
        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4是周一到周五
                trading_days += 1
                
                # 每10个交易日输出一次进度
                if trading_days % 10 == 0:
                    print(f"📅 {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"💰 当前资金: {self.current_capital:,.0f} 元")
                    print(f"📊 当前持仓: {len(self.positions)} 个股票")
                
                # 检查平仓条件
                exits = self.check_exit_conditions(current_date)
                
                # 获取交易信号
                signals = self.get_trading_signals(current_date)
                
                # 执行交易
                trades = self.execute_trades(current_date, signals)
                
                # 更新每日资金记录
                self.update_daily_capital(current_date)
            
            current_date += timedelta(days=1)
        
        # 强制平仓所有剩余持仓
        print(f"\n📊 回测结束，强制平仓剩余 {len(self.positions)} 个持仓...")
        final_exits = self.check_exit_conditions(end_dt)
        
        # 生成回测报告
        self.generate_backtest_report()
    
    def generate_backtest_report(self):
        """生成回测报告"""
        print("\n" + "=" * 60)
        print("📊 V1版本实盘验证回测报告")
        print("=" * 60)
        
        # 基本统计
        total_trades = len([t for t in self.trade_history if t['action'] == 'BUY'])
        completed_trades = len([t for t in self.trade_history if t['action'] == 'SELL'])
        
        print(f"📈 交易统计:")
        print(f"  总开仓次数: {total_trades} 次")
        print(f"  完成交易: {completed_trades} 次")
        print(f"  最大持仓数: {self.max_positions_held} 个股票")
        
        # 盈亏统计
        if completed_trades > 0:
            sell_trades = [t for t in self.trade_history if t['action'] == 'SELL']
            total_pnl = sum([t['pnl'] for t in sell_trades])
            win_trades = [t for t in sell_trades if t['pnl'] > 0]
            loss_trades = [t for t in sell_trades if t['pnl'] < 0]
            
            win_rate = len(win_trades) / completed_trades
            avg_win = np.mean([t['pnl'] for t in win_trades]) if win_trades else 0
            avg_loss = np.mean([t['pnl'] for t in loss_trades]) if loss_trades else 0
            
            print(f"\n💰 盈亏统计:")
            print(f"  总盈亏: {total_pnl:,.2f} 元")
            print(f"  胜率: {win_rate:.2%}")
            print(f"  盈利交易: {len(win_trades)} 次")
            print(f"  亏损交易: {len(loss_trades)} 次")
            print(f"  平均盈利: {avg_win:.2f} 元")
            print(f"  平均亏损: {avg_loss:.2f} 元")
        
        # 资金曲线
        if self.daily_capital:
            final_capital = self.daily_capital[-1]['total_capital']
            total_return = (final_capital - self.initial_capital) / self.initial_capital
            
            print(f"\n📈 资金曲线:")
            print(f"  初始资金: {self.initial_capital:,.2f} 元")
            print(f"  最终资金: {final_capital:,.2f} 元")
            print(f"  总收益率: {total_return:.2%}")
        
        # 保存详细结果
        self.save_results()
    
    def save_results(self):
        """保存回测结果"""
        try:
            results_dir = os.path.join(os.path.dirname(current_dir), 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存交易记录
            if self.trade_history:
                trades_df = pd.DataFrame(self.trade_history)
                trades_file = os.path.join(results_dir, f'live_validation_trades_{timestamp}.csv')
                trades_df.to_csv(trades_file, index=False, encoding='utf-8-sig')
                print(f"✅ 交易记录已保存: {trades_file}")
            
            # 保存资金曲线
            if self.daily_capital:
                capital_df = pd.DataFrame(self.daily_capital)
                capital_file = os.path.join(results_dir, f'live_validation_capital_{timestamp}.csv')
                capital_df.to_csv(capital_file, index=False, encoding='utf-8-sig')
                print(f"✅ 资金曲线已保存: {capital_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    backtest = LiveValidationBacktest()
    backtest.run_validation_backtest(
        start_date='2024-01-01',
        end_date='2025-07-18'
    )

if __name__ == "__main__":
    main()
