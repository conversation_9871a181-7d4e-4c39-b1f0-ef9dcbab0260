#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
扩展数据收集策略
收集更多类型的交易信号，增加机器学习训练数据量

包含的信号类型：
1. 涨停后向下笔反弹（原策略）
2. 日线级别买卖点
3. 5分钟级别买卖点
4. 背驰信号
5. 突破信号
"""

import sys
import os
# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录
chanlun_src_path = os.path.join(project_root, 'chanlun-pro', 'src')
sys.path.append(chanlun_src_path)

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from chanlun.cl_interface import *
from chanlun.backtesting.base import *
from chanlun.config import get_data_path

class ExtendedDataCollectionStrategy(Strategy):
    """扩展数据收集策略"""
    
    def __init__(self):
        super().__init__()
        self.name = "扩展数据收集策略"
        
        # 信号类型
        self.signal_types = {
            'limit_up_reversal': '涨停后反弹',
            'daily_buy_point': '日线买点',
            'daily_sell_point': '日线卖点',
            'min5_buy_point': '5分钟买点',
            'min5_sell_point': '5分钟卖点',
            'beichi_signal': '背驰信号',
            'breakthrough_signal': '突破信号'
        }
        
        # 缓存数据
        self.cache_data = {}
        
    def clear(self):
        """清理缓存"""
        self.cache_data = {}
        return super().clear()
    
    def check_signals(self, code: str, market_data: MarketDatas) -> List[Dict]:
        """检查所有类型的交易信号"""
        signals = []
        
        try:
            # 获取日线和5分钟数据
            daily_klines = market_data.get_cl_data(code, '1d')
            min5_klines = market_data.get_cl_data(code, '5m')
            
            if daily_klines is None or min5_klines is None:
                return signals
            
            if len(daily_klines.klines) < 50 or len(min5_klines.klines) < 100:
                return signals
            
            # 1. 检查涨停后反弹信号
            limit_up_signals = self._check_limit_up_reversal(code, daily_klines, min5_klines)
            signals.extend(limit_up_signals)
            
            # 2. 检查日线买卖点
            daily_signals = self._check_daily_buy_sell_points(code, daily_klines)
            signals.extend(daily_signals)
            
            # 3. 检查5分钟买卖点
            min5_signals = self._check_min5_buy_sell_points(code, min5_klines)
            signals.extend(min5_signals)
            
            # 4. 检查背驰信号
            beichi_signals = self._check_beichi_signals(code, daily_klines, min5_klines)
            signals.extend(beichi_signals)
            
            # 5. 检查突破信号
            breakthrough_signals = self._check_breakthrough_signals(code, daily_klines, min5_klines)
            signals.extend(breakthrough_signals)
            
        except Exception as e:
            print(f"检查信号失败 {code}: {e}")
        
        return signals
    
    def _check_limit_up_reversal(self, code: str, daily_klines, min5_klines) -> List[Dict]:
        """检查涨停后反弹信号（原策略）"""
        signals = []
        
        try:
            # 寻找最近的涨停
            recent_klines = daily_klines.klines[-30:]  # 最近30天
            
            for i, kline in enumerate(recent_klines):
                # 检查是否涨停
                if kline.c >= kline.pre_c * 1.095:  # 9.5%以上涨幅
                    # 检查后续是否有向下笔
                    if i < len(recent_klines) - 5:  # 确保有足够的后续数据
                        # 简化的向下笔检测
                        has_decline = False
                        decline_low = float('inf')
                        
                        for j in range(i+1, min(i+10, len(recent_klines))):
                            if recent_klines[j].l < kline.c * 0.95:  # 跌幅超过5%
                                has_decline = True
                                decline_low = min(decline_low, recent_klines[j].l)
                        
                        if has_decline:
                            # 检查是否有反弹信号
                            current_price = recent_klines[-1].c
                            if current_price > decline_low * 1.02:  # 从低点反弹2%以上
                                signals.append({
                                    'type': 'limit_up_reversal',
                                    'code': code,
                                    'signal_price': current_price,
                                    'reason': '涨停后向下笔反弹',
                                    'strength': (current_price - decline_low) / decline_low
                                })
        except Exception as e:
            pass
        
        return signals
    
    def _check_daily_buy_sell_points(self, code: str, daily_klines) -> List[Dict]:
        """检查日线级别买卖点"""
        signals = []
        
        try:
            # 获取买卖点
            if hasattr(daily_klines, 'bi') and daily_klines.bi:
                for mmd in daily_klines.bi.mmds:
                    if mmd.name in ['1buy', '2buy', '3buy']:
                        signals.append({
                            'type': 'daily_buy_point',
                            'code': code,
                            'signal_price': mmd.val,
                            'reason': f'日线{mmd.name}',
                            'strength': 1.0
                        })
                    elif mmd.name in ['1sell', '2sell', '3sell']:
                        signals.append({
                            'type': 'daily_sell_point',
                            'code': code,
                            'signal_price': mmd.val,
                            'reason': f'日线{mmd.name}',
                            'strength': 1.0
                        })
        except Exception as e:
            pass
        
        return signals
    
    def _check_min5_buy_sell_points(self, code: str, min5_klines) -> List[Dict]:
        """检查5分钟级别买卖点"""
        signals = []
        
        try:
            # 获取5分钟买卖点
            if hasattr(min5_klines, 'bi') and min5_klines.bi:
                for mmd in min5_klines.bi.mmds[-10:]:  # 最近10个信号
                    if mmd.name in ['1buy', '2buy']:
                        signals.append({
                            'type': 'min5_buy_point',
                            'code': code,
                            'signal_price': mmd.val,
                            'reason': f'5分钟{mmd.name}',
                            'strength': 0.8
                        })
                    elif mmd.name in ['1sell', '2sell']:
                        signals.append({
                            'type': 'min5_sell_point',
                            'code': code,
                            'signal_price': mmd.val,
                            'reason': f'5分钟{mmd.name}',
                            'strength': 0.8
                        })
        except Exception as e:
            pass
        
        return signals
    
    def _check_beichi_signals(self, code: str, daily_klines, min5_klines) -> List[Dict]:
        """检查背驰信号"""
        signals = []
        
        try:
            # 简化的背驰检测
            recent_klines = daily_klines.klines[-20:]
            
            if len(recent_klines) >= 10:
                # 检查价格新高但成交量萎缩（顶背驰）
                recent_highs = [k.h for k in recent_klines[-5:]]
                recent_volumes = [k.a for k in recent_klines[-5:]]
                
                if max(recent_highs) == recent_highs[-1]:  # 价格创新高
                    if recent_volumes[-1] < np.mean(recent_volumes[:-1]) * 0.8:  # 成交量萎缩
                        signals.append({
                            'type': 'beichi_signal',
                            'code': code,
                            'signal_price': recent_klines[-1].c,
                            'reason': '顶背驰信号',
                            'strength': 0.7
                        })
                
                # 检查价格新低但成交量放大（底背驰）
                recent_lows = [k.l for k in recent_klines[-5:]]
                
                if min(recent_lows) == recent_lows[-1]:  # 价格创新低
                    if recent_volumes[-1] > np.mean(recent_volumes[:-1]) * 1.2:  # 成交量放大
                        signals.append({
                            'type': 'beichi_signal',
                            'code': code,
                            'signal_price': recent_klines[-1].c,
                            'reason': '底背驰信号',
                            'strength': 0.7
                        })
        except Exception as e:
            pass
        
        return signals
    
    def _check_breakthrough_signals(self, code: str, daily_klines, min5_klines) -> List[Dict]:
        """检查突破信号"""
        signals = []
        
        try:
            recent_klines = daily_klines.klines[-30:]
            
            if len(recent_klines) >= 20:
                # 计算20日最高价
                high_20 = max([k.h for k in recent_klines[-20:-1]])
                current_price = recent_klines[-1].c
                
                # 向上突破
                if current_price > high_20 * 1.02:  # 突破20日高点2%以上
                    signals.append({
                        'type': 'breakthrough_signal',
                        'code': code,
                        'signal_price': current_price,
                        'reason': '向上突破20日高点',
                        'strength': (current_price - high_20) / high_20
                    })
                
                # 计算20日最低价
                low_20 = min([k.l for k in recent_klines[-20:-1]])
                
                # 向下突破（做空信号）
                if current_price < low_20 * 0.98:  # 跌破20日低点2%以上
                    signals.append({
                        'type': 'breakthrough_signal',
                        'code': code,
                        'signal_price': current_price,
                        'reason': '向下突破20日低点',
                        'strength': (low_20 - current_price) / low_20
                    })
        except Exception as e:
            pass
        
        return signals
    
    def open(self, code: str, opt: Operation) -> List[Operation]:
        """开仓逻辑"""
        return [opt]
    
    def close(self, code: str, mmd: str, pos: POSITION) -> List[Operation]:
        """平仓逻辑"""
        return []
