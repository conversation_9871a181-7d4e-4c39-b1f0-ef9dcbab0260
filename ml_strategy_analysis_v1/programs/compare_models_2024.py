#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对比不同机器学习模型在2024年的预测效果
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import lightgbm as lgb
import os

def compare_models_2024():
    """对比不同模型在2024年的表现"""
    print("🤖 2024年各模型预测效果对比分析")
    print("=" * 60)
    
    # 加载数据
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(os.path.dirname(current_dir), 'data')
    data_file = os.path.join(data_dir, 'ml_features_complete_20250718_204428.parquet')
    
    df = pd.read_parquet(data_file)
    
    # 准备特征（修复版）
    exclude_cols = [
        'code', 'open_date', 'open_time', 'close_date', 'close_time', 'year',
        'label', 'trade_result', 'action',
        'stop_loss', 'take_profit', 'shares', 'actual_cost',
        'exit_price', 'pnl', 'pnl_pct', 'hold_days',
        'signal_reason', 'exit_reason', 'enter_reason', 'candidate_status',
        'bi_start_time', 'bi_end_time'
    ]
    
    valid_prefixes = [
        'prev_', 'current_5min_', 'has_down_bi', 'price_vs_bi',
        'bi_1_', 'bi_2_', 'bi_3_', 'bi_4_', 'bi_5_',
        'duan_1_', 'duan_2_', 'duan_3_', 'zhongshu_',
        'limit_up_', 'signal_price'
    ]
    
    feature_cols = []
    for col in df.columns:
        if col not in exclude_cols:
            is_valid = any(col.startswith(prefix) for prefix in valid_prefixes)
            if is_valid:
                feature_cols.append(col)
    
    X = df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)
    y = df['label']
    
    # 训练测试分割
    train_mask = df['year'].isin([2021, 2022, 2023])
    test_mask = df['year'] == 2024
    
    X_train = X[train_mask]
    y_train = y[train_mask]
    X_test = X[test_mask]
    y_test = y[test_mask]
    
    df_2024 = df[test_mask].copy()
    
    print(f"📊 数据概况:")
    print(f"  训练样本: {len(X_train)} 个")
    print(f"  测试样本: {len(X_test)} 个")
    print(f"  特征维度: {len(feature_cols)} 个")
    
    # 定义模型
    models = {
        'RandomForest': RandomForestClassifier(
            n_estimators=50, max_depth=5, min_samples_split=20, 
            min_samples_leaf=10, random_state=42
        ),
        'GradientBoosting': GradientBoostingClassifier(
            n_estimators=50, max_depth=3, learning_rate=0.1,
            min_samples_split=20, min_samples_leaf=10, random_state=42
        ),
        'LightGBM': lgb.LGBMClassifier(
            n_estimators=50, max_depth=3, learning_rate=0.1,
            min_child_samples=20, random_state=42, verbose=-1
        ),
        'LogisticRegression': LogisticRegression(
            C=1.0, max_iter=1000, random_state=42
        ),
        'SVM': SVC(
            C=1.0, kernel='rbf', probability=True, random_state=42
        )
    }
    
    # 存储结果
    model_results = {}
    
    print(f"\n🔧 训练和评估各模型:")
    
    for name, model in models.items():
        print(f"\n  📈 {name}:")
        
        try:
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # 基础指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            print(f"    准确率: {accuracy:.4f}")
            print(f"    精确率: {precision:.4f}")
            print(f"    召回率: {recall:.4f}")
            print(f"    F1分数: {f1:.4f}")
            
            # 预测分布
            pred_positive = sum(y_pred == 1)
            actual_positive = sum(y_test == 1)
            print(f"    预测正样本: {pred_positive} 个")
            print(f"    实际正样本: {actual_positive} 个")
            
            # 如果有概率预测，分析不同阈值下的表现
            if y_pred_proba is not None:
                df_2024_model = df_2024.copy()
                df_2024_model['pred_proba'] = y_pred_proba
                
                # 分析不同阈值
                best_threshold = None
                best_nav = 0
                threshold_results = {}
                
                for threshold in [0.2, 0.25, 0.3, 0.4, 0.5]:
                    selected_mask = df_2024_model['pred_proba'] >= threshold
                    selected_df = df_2024_model[selected_mask]
                    
                    if len(selected_df) >= 3:  # 至少3个信号
                        win_count = sum(selected_df['label'] == 1)
                        win_rate = win_count / len(selected_df)
                        
                        # 计算累计净值
                        selected_sorted = selected_df.sort_values('open_date')
                        nav = (1 + selected_sorted['pnl_pct']).prod()
                        
                        threshold_results[threshold] = {
                            'count': len(selected_df),
                            'wins': win_count,
                            'win_rate': win_rate,
                            'nav': nav,
                            'return': (nav - 1) * 100
                        }
                        
                        if nav > best_nav:
                            best_nav = nav
                            best_threshold = threshold
                
                # 显示最佳阈值结果
                if best_threshold is not None:
                    best_result = threshold_results[best_threshold]
                    print(f"    最佳阈值: {best_threshold}")
                    print(f"    筛选信号: {best_result['count']} 个")
                    print(f"    胜率: {best_result['win_rate']:.2%}")
                    print(f"    累计净值: {best_result['nav']:.4f}")
                    print(f"    累计收益: {best_result['return']:.2f}%")
                
                # 保存模型结果
                model_results[name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'pred_positive': pred_positive,
                    'best_threshold': best_threshold,
                    'best_nav': best_nav,
                    'best_return': (best_nav - 1) * 100 if best_nav > 0 else 0,
                    'threshold_results': threshold_results
                }
            
        except Exception as e:
            print(f"    ❌ 训练失败: {e}")
            model_results[name] = None
    
    # 汇总对比
    print(f"\n" + "=" * 60)
    print(f"📊 模型综合对比")
    print(f"=" * 60)
    
    # 创建对比表格
    comparison_data = []
    for name, result in model_results.items():
        if result is not None:
            comparison_data.append({
                'Model': name,
                'Accuracy': f"{result['accuracy']:.4f}",
                'Best_Threshold': result['best_threshold'],
                'Best_NAV': f"{result['best_nav']:.4f}",
                'Best_Return': f"{result['best_return']:.2f}%",
                'Pred_Signals': result['pred_positive']
            })
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))
        
        # 找出最佳模型
        best_model = max(model_results.items(), 
                        key=lambda x: x[1]['best_nav'] if x[1] is not None else 0)
        
        print(f"\n🏆 最佳模型: {best_model[0]}")
        print(f"   最佳净值: {best_model[1]['best_nav']:.4f}")
        print(f"   最佳收益: {best_model[1]['best_return']:.2f}%")
        print(f"   最佳阈值: {best_model[1]['best_threshold']}")
        
        # 详细阈值分析
        print(f"\n📈 {best_model[0]} 详细阈值分析:")
        best_thresholds = best_model[1]['threshold_results']
        for threshold, result in sorted(best_thresholds.items()):
            print(f"   阈值 {threshold}: {result['count']}个信号, "
                  f"胜率{result['win_rate']:.2%}, "
                  f"净值{result['nav']:.4f}, "
                  f"收益{result['return']:.2f}%")
    
    # 保存结果
    results_dir = os.path.join(os.path.dirname(current_dir), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    if comparison_data:
        output_file = os.path.join(results_dir, 'model_comparison_2024.csv')
        comparison_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n✅ 模型对比结果已保存到: {output_file}")

if __name__ == "__main__":
    compare_models_2024()
