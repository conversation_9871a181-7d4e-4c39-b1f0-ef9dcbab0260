#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用最佳模型和阈值验证2025年数据
真正的样本外测试
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score
import lightgbm as lgb
import os

def validate_models_2025():
    """验证模型在2025年的表现"""
    print("🚀 2025年样本外验证分析")
    print("=" * 60)
    
    # 加载数据
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(os.path.dirname(current_dir), 'data')
    data_file = os.path.join(data_dir, 'ml_features_complete_20250718_204428.parquet')
    
    df = pd.read_parquet(data_file)
    
    # 准备特征（修复版）
    exclude_cols = [
        'code', 'open_date', 'open_time', 'close_date', 'close_time', 'year',
        'label', 'trade_result', 'action',
        'stop_loss', 'take_profit', 'shares', 'actual_cost',
        'exit_price', 'pnl', 'pnl_pct', 'hold_days',
        'signal_reason', 'exit_reason', 'enter_reason', 'candidate_status',
        'bi_start_time', 'bi_end_time'
    ]
    
    valid_prefixes = [
        'prev_', 'current_5min_', 'has_down_bi', 'price_vs_bi',
        'bi_1_', 'bi_2_', 'bi_3_', 'bi_4_', 'bi_5_',
        'duan_1_', 'duan_2_', 'duan_3_', 'zhongshu_',
        'limit_up_', 'signal_price'
    ]
    
    feature_cols = []
    for col in df.columns:
        if col not in exclude_cols:
            is_valid = any(col.startswith(prefix) for prefix in valid_prefixes)
            if is_valid:
                feature_cols.append(col)
    
    X = df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)
    y = df['label']
    
    # 训练测试分割
    train_mask = df['year'].isin([2021, 2022, 2023])
    test_mask_2025 = df['year'] == 2025
    
    X_train = X[train_mask]
    y_train = y[train_mask]
    X_test_2025 = X[test_mask_2025]
    y_test_2025 = y[test_mask_2025]
    
    df_2025 = df[test_mask_2025].copy()
    
    print(f"📊 数据概况:")
    print(f"  训练样本: {len(X_train)} 个 (2021-2023)")
    print(f"  2025测试样本: {len(X_test_2025)} 个")
    print(f"  特征维度: {len(feature_cols)} 个")
    
    # 2025年原始策略表现
    print(f"\n📈 2025年原始策略表现:")
    print(f"  总信号数: {len(df_2025)} 个")
    print(f"  盈利信号: {sum(df_2025['label'] == 1)} 个")
    print(f"  亏损信号: {sum(df_2025['label'] == -1)} 个")
    print(f"  胜率: {sum(df_2025['label'] == 1) / len(df_2025):.2%}")
    
    # 计算原始策略累计净值
    df_2025_sorted = df_2025.sort_values('open_date')
    original_nav = (1 + df_2025_sorted['pnl_pct']).prod()
    print(f"  累计净值: {original_nav:.4f}")
    print(f"  累计收益: {(original_nav - 1) * 100:.2f}%")
    
    # 定义最佳模型配置（基于2024年分析结果）
    best_models_config = {
        'RandomForest': {
            'model': RandomForestClassifier(
                n_estimators=50, max_depth=5, min_samples_split=20, 
                min_samples_leaf=10, random_state=42
            ),
            'best_threshold': 0.25
        },
        'LightGBM': {
            'model': lgb.LGBMClassifier(
                n_estimators=50, max_depth=3, learning_rate=0.1,
                min_child_samples=20, random_state=42, verbose=-1
            ),
            'best_threshold': 0.25
        },
        'GradientBoosting': {
            'model': GradientBoostingClassifier(
                n_estimators=50, max_depth=3, learning_rate=0.1,
                min_samples_split=20, min_samples_leaf=10, random_state=42
            ),
            'best_threshold': 0.20
        }
    }
    
    print(f"\n🤖 使用最佳模型配置验证2025年:")
    
    results_2025 = {}
    
    for name, config in best_models_config.items():
        print(f"\n  📈 {name} (阈值: {config['best_threshold']}):")
        
        try:
            # 训练模型
            model = config['model']
            model.fit(X_train, y_train)
            
            # 预测2025年
            y_pred_proba = model.predict_proba(X_test_2025)[:, 1]
            
            # 使用最佳阈值筛选
            threshold = config['best_threshold']
            selected_mask = y_pred_proba >= threshold
            selected_df = df_2025[selected_mask].copy()
            
            if len(selected_df) > 0:
                # 基础统计
                win_count = sum(selected_df['label'] == 1)
                win_rate = win_count / len(selected_df)
                
                # 计算累计净值
                selected_sorted = selected_df.sort_values('open_date')
                nav = (1 + selected_sorted['pnl_pct']).prod()
                total_return = (nav - 1) * 100
                
                print(f"    筛选信号: {len(selected_df)} 个")
                print(f"    盈利信号: {win_count} 个")
                print(f"    胜率: {win_rate:.2%}")
                print(f"    累计净值: {nav:.4f}")
                print(f"    累计收益: {total_return:.2f}%")
                
                # 保存结果
                results_2025[name] = {
                    'threshold': threshold,
                    'selected_count': len(selected_df),
                    'win_count': win_count,
                    'win_rate': win_rate,
                    'nav': nav,
                    'return': total_return,
                    'selected_df': selected_sorted
                }
                
                # 如果信号不多，显示详细交易
                if len(selected_df) <= 15:
                    print(f"    详细交易:")
                    for i, (_, row) in enumerate(selected_sorted.iterrows()):
                        result = "盈利" if row['label'] == 1 else "亏损"
                        print(f"      {i+1}. {row['code']} {row['open_date']} "
                              f"预测:{y_pred_proba[df_2025.index.get_loc(row.name)]:.3f} "
                              f"{result} {row['pnl_pct']*100:.2f}%")
            else:
                print(f"    ❌ 阈值 {threshold} 未筛选出任何信号")
                results_2025[name] = None
                
        except Exception as e:
            print(f"    ❌ 验证失败: {e}")
            results_2025[name] = None
    
    # 汇总对比
    print(f"\n" + "=" * 60)
    print(f"📊 2025年模型验证汇总")
    print(f"=" * 60)
    
    print(f"原始策略: {len(df_2025)}个信号, 胜率{sum(df_2025['label'] == 1) / len(df_2025):.2%}, "
          f"净值{original_nav:.4f}, 收益{(original_nav-1)*100:.2f}%")
    
    # 创建对比表格
    valid_results = {k: v for k, v in results_2025.items() if v is not None}
    
    if valid_results:
        print(f"\n机器学习优化结果:")
        for name, result in valid_results.items():
            print(f"{name}: {result['selected_count']}个信号, "
                  f"胜率{result['win_rate']:.2%}, "
                  f"净值{result['nav']:.4f}, "
                  f"收益{result['return']:.2f}%")
        
        # 找出最佳模型
        best_model_2025 = max(valid_results.items(), key=lambda x: x[1]['nav'])
        
        print(f"\n🏆 2025年最佳模型: {best_model_2025[0]}")
        print(f"   净值: {best_model_2025[1]['nav']:.4f}")
        print(f"   收益: {best_model_2025[1]['return']:.2f}%")
        print(f"   胜率: {best_model_2025[1]['win_rate']:.2%}")
        print(f"   信号数: {best_model_2025[1]['selected_count']} 个")
        
        # 与2024年结果对比
        print(f"\n📊 2024 vs 2025 对比:")
        print(f"{'模型':<15} {'2024收益':<10} {'2025收益':<10} {'差异':<10}")
        print(f"{'-'*15} {'-'*10} {'-'*10} {'-'*10}")
        
        # 2024年最佳结果（从之前分析得出）
        results_2024 = {
            'RandomForest': 81.55,
            'LightGBM': 57.56,
            'GradientBoosting': 26.39
        }
        
        for name in valid_results.keys():
            return_2024 = results_2024.get(name, 0)
            return_2025 = valid_results[name]['return']
            diff = return_2025 - return_2024
            
            print(f"{name:<15} {return_2024:>8.2f}% {return_2025:>8.2f}% {diff:>+8.2f}%")
    
    # 保存详细结果
    results_dir = os.path.join(os.path.dirname(current_dir), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存2025年预测结果
    if valid_results:
        for name, result in valid_results.items():
            if result is not None:
                output_file = os.path.join(results_dir, f'2025_{name}_predictions.csv')
                selected_df = result['selected_df']
                output_data = selected_df[['code', 'open_date', 'signal_price', 'label', 'pnl_pct']].copy()
                output_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                print(f"\n✅ {name} 2025年预测结果已保存到: {output_file}")

if __name__ == "__main__":
    validate_models_2025()
