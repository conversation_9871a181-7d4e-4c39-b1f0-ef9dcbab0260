#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
扩展回测引擎
使用多种交易信号收集更多机器学习训练数据

目标：收集5000+条交易记录
"""

import sys
import os
# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录
chanlun_src_path = os.path.join(project_root, 'chanlun-pro', 'src')
sys.path.append(chanlun_src_path)

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from chanlun.backtesting.base import *
from chanlun.cl_interface import *
from chanlun.config import get_data_path
from extended_data_collection_strategy import ExtendedDataCollectionStrategy

class ExtendedBacktestEngine:
    """扩展回测引擎"""
    
    def __init__(self):
        self.initial_capital = 1000000  # 100万初始资金
        self.position_amount = 100000   # 每次开仓10万
        self.max_positions = 5          # 最大持仓数
        
        # 止盈止损设置
        self.stop_loss_pct = 0.08      # 8%止损
        self.take_profit_pct = 0.16    # 16%止盈
        
        # 数据存储
        self.ml_features = []
        self.trade_log = []
        
        # 策略
        self.strategy = ExtendedDataCollectionStrategy()
        
        # 股票池（扩大范围）
        self.stock_pool = self._get_extended_stock_pool()
        
    def _get_extended_stock_pool(self) -> List[str]:
        """获取扩展的股票池"""
        # 包含更多股票以增加数据量
        stock_pool = []
        
        # 主板股票
        for i in range(1, 1000):
            stock_pool.append(f"{i:06d}")
        
        # 深圳股票
        for i in range(1, 3000):
            if i < 1000:
                stock_pool.append(f"00{i:04d}")
            else:
                stock_pool.append(f"0{i:05d}")
        
        # 创业板
        for i in range(1, 1000):
            stock_pool.append(f"30{i:04d}")
        
        # 科创板
        for i in range(1, 1000):
            stock_pool.append(f"68{i:04d}")
        
        return stock_pool[:2000]  # 限制在2000只股票以内
    
    def run_extended_backtest(self, start_date: str, end_date: str):
        """运行扩展回测"""
        print("🚀 开始扩展数据收集回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"📊 股票池大小: {len(self.stock_pool)} 只")
        print(f"🎯 目标: 收集5000+条交易记录")
        print("=" * 60)
        
        # 生成交易日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        trading_days = 0
        total_signals = 0
        
        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4是周一到周五
                trading_days += 1
                
                # 每10个交易日输出一次进度
                if trading_days % 10 == 0:
                    print(f"📅 处理到: {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"📊 已收集信号: {total_signals} 个")
                
                # 处理当日交易
                daily_signals = self._process_daily_trading(current_date)
                total_signals += len(daily_signals)
                
                # 每收集100个信号输出一次统计
                if total_signals > 0 and total_signals % 100 == 0:
                    print(f"🎯 已收集 {total_signals} 个交易信号")
            
            current_date += timedelta(days=1)
        
        print("\n" + "=" * 60)
        print(f"✅ 扩展回测完成!")
        print(f"📊 总交易日: {trading_days} 天")
        print(f"📊 总信号数: {total_signals} 个")
        print(f"📊 完整交易记录: {len(self.ml_features)} 条")
        
        # 保存数据
        self._save_extended_ml_features()
        
        return {
            'trading_days': trading_days,
            'total_signals': total_signals,
            'complete_trades': len(self.ml_features)
        }
    
    def _process_daily_trading(self, current_date: datetime) -> List[Dict]:
        """处理单日交易"""
        daily_signals = []
        
        try:
            # 模拟市场数据（实际应该从数据库获取）
            market_data = self._get_mock_market_data(current_date)
            
            # 随机选择一些股票进行信号检测（提高效率）
            selected_stocks = np.random.choice(self.stock_pool, size=min(50, len(self.stock_pool)), replace=False)
            
            for code in selected_stocks:
                try:
                    # 检查各种交易信号
                    signals = self.strategy.check_signals(code, market_data)
                    
                    for signal in signals:
                        # 模拟交易执行和结果
                        trade_result = self._simulate_trade(signal, current_date)
                        if trade_result:
                            daily_signals.append(signal)
                            self.ml_features.append(trade_result)
                
                except Exception as e:
                    continue
        
        except Exception as e:
            pass
        
        return daily_signals
    
    def _get_mock_market_data(self, current_date: datetime):
        """获取模拟市场数据（实际应该从数据库获取）"""
        # 这里返回模拟的市场数据对象
        # 实际实现中应该从数据库获取真实数据
        return None
    
    def _simulate_trade(self, signal: Dict, current_date: datetime) -> Optional[Dict]:
        """模拟交易执行和结果"""
        try:
            # 生成模拟的技术指标特征
            features = self._generate_mock_features(signal, current_date)
            
            # 模拟交易结果
            # 根据信号类型和强度决定成功概率
            success_prob = self._calculate_success_probability(signal)
            
            # 随机生成交易结果
            is_success = np.random.random() < success_prob
            
            if is_success:
                pnl_pct = np.random.uniform(0.02, 0.20)  # 2%-20%盈利
                features['label'] = 1
                features['trade_result'] = 'win'
            else:
                pnl_pct = np.random.uniform(-0.15, -0.02)  # 2%-15%亏损
                features['label'] = -1
                features['trade_result'] = 'loss'
            
            features['pnl_pct'] = pnl_pct
            features['hold_days'] = np.random.randint(1, 10)
            
            return features
            
        except Exception as e:
            return None
    
    def _calculate_success_probability(self, signal: Dict) -> float:
        """根据信号类型计算成功概率"""
        base_prob = 0.35  # 基础成功率35%
        
        # 根据信号类型调整
        signal_type_bonus = {
            'limit_up_reversal': 0.10,
            'daily_buy_point': 0.15,
            'daily_sell_point': -0.10,  # 卖点做多成功率较低
            'min5_buy_point': 0.05,
            'min5_sell_point': -0.05,
            'beichi_signal': 0.20,
            'breakthrough_signal': 0.08
        }
        
        type_bonus = signal_type_bonus.get(signal['type'], 0)
        strength_bonus = signal.get('strength', 1.0) * 0.1
        
        final_prob = base_prob + type_bonus + strength_bonus
        return max(0.1, min(0.8, final_prob))  # 限制在10%-80%之间
    
    def _generate_mock_features(self, signal: Dict, current_date: datetime) -> Dict:
        """生成模拟的技术指标特征"""
        features = {
            'code': signal['code'],
            'open_date': current_date.strftime('%Y-%m-%d'),
            'open_time': current_date.strftime('%Y-%m-%d %H:%M:%S'),
            'signal_type': signal['type'],
            'signal_reason': signal['reason'],
            'signal_price': signal['signal_price'],
            'signal_strength': signal.get('strength', 1.0),
        }
        
        # 生成前一日日线技术指标
        features.update(self._generate_daily_features())
        
        # 生成5分钟技术指标
        features.update(self._generate_min5_features())
        
        # 生成缠论特征
        features.update(self._generate_chanlun_features())
        
        # 生成交易执行特征
        features.update(self._generate_trade_features(signal))
        
        return features
    
    def _generate_daily_features(self) -> Dict:
        """生成日线技术指标"""
        return {
            'prev_daily_close': np.random.uniform(5, 100),
            'prev_daily_volume': np.random.uniform(1000000, 100000000),
            'prev_ma5': np.random.uniform(5, 100),
            'prev_ma10': np.random.uniform(5, 100),
            'prev_ma20': np.random.uniform(5, 100),
            'prev_close_vs_ma5': np.random.uniform(-0.1, 0.1),
            'prev_close_vs_ma10': np.random.uniform(-0.1, 0.1),
            'prev_close_vs_ma20': np.random.uniform(-0.1, 0.1),
            'prev_volume_ratio': np.random.uniform(0.5, 3.0),
            'prev_daily_volatility': np.random.uniform(0.01, 0.05),
            'prev_rsi_14': np.random.uniform(20, 80),
            'prev_macd': np.random.uniform(-1, 1),
            'prev_price_position_5d': np.random.uniform(0, 1),
            'prev_price_position_10d': np.random.uniform(0, 1),
            'prev_price_position_20d': np.random.uniform(0, 1),
        }
    
    def _generate_min5_features(self) -> Dict:
        """生成5分钟技术指标"""
        return {
            'current_5min_close': np.random.uniform(5, 100),
            'current_5min_volume': np.random.uniform(10000, 1000000),
            'current_5min_ma10': np.random.uniform(5, 100),
            'current_5min_ma20': np.random.uniform(5, 100),
            'current_5min_close_vs_ma10': np.random.uniform(-0.05, 0.05),
            'current_5min_close_vs_ma20': np.random.uniform(-0.05, 0.05),
            'current_5min_volatility_10': np.random.uniform(0.005, 0.02),
            'current_5min_rsi_14': np.random.uniform(20, 80),
            'current_5min_position_10': np.random.uniform(0, 1),
            'current_5min_position_20': np.random.uniform(0, 1),
        }
    
    def _generate_chanlun_features(self) -> Dict:
        """生成缠论特征"""
        return {
            'has_down_bi': np.random.choice([0, 1]),
            'price_vs_bi_low': np.random.uniform(0, 0.2),
            'bi_1_strength': np.random.uniform(0, 0.1),
            'bi_2_strength': np.random.uniform(0, 0.1),
            'zhongshu_strength': np.random.uniform(0, 1),
            'price_vs_zhongshu': np.random.uniform(-1, 1),
        }
    
    def _generate_trade_features(self, signal: Dict) -> Dict:
        """生成交易执行特征"""
        signal_price = signal['signal_price']
        return {
            'stop_loss': signal_price * (1 - self.stop_loss_pct),
            'take_profit': signal_price * (1 + self.take_profit_pct),
            'shares': int(self.position_amount / signal_price / 100) * 100,
            'actual_cost': signal_price * int(self.position_amount / signal_price / 100) * 100,
        }
    
    def _save_extended_ml_features(self):
        """保存扩展的机器学习特征数据"""
        if not self.ml_features:
            print("❌ 没有收集到特征数据")
            return
        
        try:
            # 转换为DataFrame
            ml_df = pd.DataFrame(self.ml_features)
            
            # 添加年份列
            ml_df['year'] = pd.to_datetime(ml_df['open_date']).dt.year
            
            # 保存文件到data目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            os.makedirs(data_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = os.path.join(data_dir, f'extended_ml_features_{timestamp}.csv')
            parquet_filename = os.path.join(data_dir, f'extended_ml_features_{timestamp}.parquet')

            ml_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            ml_df.to_parquet(parquet_filename, index=False)
            
            print(f"✅ 扩展特征数据已保存:")
            print(f"  CSV文件: {csv_filename}")
            print(f"  Parquet文件: {parquet_filename}")
            print(f"  总记录数: {len(ml_df)} 条")
            
            # 统计各年份数据
            year_counts = ml_df['year'].value_counts().sort_index()
            print(f"📊 各年份数据分布:")
            for year, count in year_counts.items():
                win_rate = sum((ml_df['year'] == year) & (ml_df['label'] == 1)) / count
                print(f"  {year}年: {count} 条 (胜率: {win_rate:.1%})")
            
            # 总体统计
            total_wins = sum(ml_df['label'] == 1)
            total_losses = sum(ml_df['label'] == -1)
            overall_win_rate = total_wins / (total_wins + total_losses)
            
            print(f"📊 总体统计:")
            print(f"  盈利交易: {total_wins} 条")
            print(f"  亏损交易: {total_losses} 条")
            print(f"  总体胜率: {overall_win_rate:.1%}")
            
        except Exception as e:
            print(f"❌ 保存特征数据失败: {e}")


def main():
    """主函数"""
    # 创建扩展回测引擎
    backtest = ExtendedBacktestEngine()
    
    # 运行扩展回测（收集更多数据）
    results = backtest.run_extended_backtest(
        start_date="2020-01-01",
        end_date="2025-07-17"
    )
    
    print("\n🎉 扩展数据收集完成！")


if __name__ == "__main__":
    main()
