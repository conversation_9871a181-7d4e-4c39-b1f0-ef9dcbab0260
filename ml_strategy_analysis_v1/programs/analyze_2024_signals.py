#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析2024年信号和机器学习筛选结果
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
import os

def analyze_2024_signals():
    """分析2024年信号"""
    print("📊 2024年信号详细分析")
    print("=" * 60)
    
    # 加载数据
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(os.path.dirname(current_dir), 'data')
    data_file = os.path.join(data_dir, 'ml_features_complete_20250718_204428.parquet')
    
    df = pd.read_parquet(data_file)
    
    # 准备特征（修复版）
    exclude_cols = [
        'code', 'open_date', 'open_time', 'close_date', 'close_time', 'year',
        'label', 'trade_result', 'action',
        'stop_loss', 'take_profit', 'shares', 'actual_cost',
        'exit_price', 'pnl', 'pnl_pct', 'hold_days',
        'signal_reason', 'exit_reason', 'enter_reason', 'candidate_status',
        'bi_start_time', 'bi_end_time'
    ]
    
    valid_prefixes = [
        'prev_', 'current_5min_', 'has_down_bi', 'price_vs_bi',
        'bi_1_', 'bi_2_', 'bi_3_', 'bi_4_', 'bi_5_',
        'duan_1_', 'duan_2_', 'duan_3_', 'zhongshu_',
        'limit_up_', 'signal_price'
    ]
    
    feature_cols = []
    for col in df.columns:
        if col not in exclude_cols:
            is_valid = any(col.startswith(prefix) for prefix in valid_prefixes)
            if is_valid:
                feature_cols.append(col)
    
    X = df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)
    y = df['label']
    
    # 训练模型
    train_mask = df['year'].isin([2021, 2022, 2023])
    X_train = X[train_mask]
    y_train = y[train_mask]
    
    model = RandomForestClassifier(n_estimators=50, max_depth=5, random_state=42)
    model.fit(X_train, y_train)
    
    # 分析2024年数据
    test_mask = df['year'] == 2024
    df_2024 = df[test_mask].copy()
    X_test = X[test_mask]
    y_test = y[test_mask]
    
    # 预测
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    df_2024['pred_proba'] = y_pred_proba
    
    print(f"📈 2024年原始策略表现:")
    print(f"  总信号数: {len(df_2024)} 个")
    print(f"  盈利信号: {sum(df_2024['label'] == 1)} 个")
    print(f"  亏损信号: {sum(df_2024['label'] == -1)} 个")
    print(f"  胜率: {sum(df_2024['label'] == 1) / len(df_2024):.2%}")
    
    # 计算原始策略累计净值
    df_2024_sorted = df_2024.sort_values('open_date')
    original_nav = (1 + df_2024_sorted['pnl_pct']).prod()
    print(f"  累计净值: {original_nav:.4f}")
    print(f"  累计收益: {(original_nav - 1) * 100:.2f}%")
    
    print(f"\n🤖 机器学习筛选结果:")
    
    # 不同阈值分析
    thresholds = [0.3, 0.4, 0.5, 0.6, 0.7]
    
    for threshold in thresholds:
        selected_mask = df_2024['pred_proba'] >= threshold
        selected_df = df_2024[selected_mask].copy()
        
        if len(selected_df) > 0:
            win_count = sum(selected_df['label'] == 1)
            win_rate = win_count / len(selected_df)
            
            # 按时间排序计算累计净值
            selected_sorted = selected_df.sort_values('open_date')
            nav = (1 + selected_sorted['pnl_pct']).prod()
            
            print(f"\n  阈值 {threshold}:")
            print(f"    筛选信号: {len(selected_df)} 个")
            print(f"    盈利信号: {win_count} 个")
            print(f"    胜率: {win_rate:.2%}")
            print(f"    累计净值: {nav:.4f}")
            print(f"    累计收益: {(nav - 1) * 100:.2f}%")
            
            # 如果信号数量不多，显示详细信息
            if len(selected_df) <= 10:
                print(f"    详细交易:")
                for i, (_, row) in enumerate(selected_sorted.iterrows()):
                    result = "盈利" if row['label'] == 1 else "亏损"
                    print(f"      {i+1}. {row['code']} {row['open_date']} "
                          f"预测:{row['pred_proba']:.3f} {result} {row['pnl_pct']*100:.2f}%")
        else:
            print(f"\n  阈值 {threshold}: 无信号筛选出")
    
    # 找出最佳阈值
    print(f"\n📊 最佳阈值分析:")
    best_threshold = None
    best_nav = 0
    best_info = {}
    
    for threshold in np.arange(0.1, 0.9, 0.05):
        selected_mask = df_2024['pred_proba'] >= threshold
        selected_df = df_2024[selected_mask]
        
        if len(selected_df) >= 3:  # 至少3个信号
            selected_sorted = selected_df.sort_values('open_date')
            nav = (1 + selected_sorted['pnl_pct']).prod()
            
            if nav > best_nav:
                best_nav = nav
                best_threshold = threshold
                best_info = {
                    'count': len(selected_df),
                    'wins': sum(selected_df['label'] == 1),
                    'win_rate': sum(selected_df['label'] == 1) / len(selected_df),
                    'nav': nav,
                    'return': (nav - 1) * 100
                }
    
    if best_threshold is not None:
        print(f"  最佳阈值: {best_threshold:.2f}")
        print(f"  筛选信号: {best_info['count']} 个")
        print(f"  胜率: {best_info['win_rate']:.2%}")
        print(f"  累计净值: {best_info['nav']:.4f}")
        print(f"  累计收益: {best_info['return']:.2f}%")
    else:
        print(f"  未找到有效的最佳阈值")
    
    # 保存分析结果
    results_dir = os.path.join(os.path.dirname(current_dir), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存带预测概率的2024年数据
    output_file = os.path.join(results_dir, '2024_signals_with_predictions.csv')
    df_2024_output = df_2024[['code', 'open_date', 'signal_price', 'pred_proba', 'label', 'pnl_pct']].copy()
    df_2024_output = df_2024_output.sort_values('open_date')
    df_2024_output.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 分析完成，详细数据已保存到: {output_file}")

if __name__ == "__main__":
    analyze_2024_signals()
