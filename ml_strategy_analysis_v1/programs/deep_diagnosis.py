#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深度诊断分析：检查数据质量和模型问题
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
import lightgbm as lgb
import os
import matplotlib.pyplot as plt
import seaborn as sns

def deep_diagnosis():
    """深度诊断分析"""
    print("🔍 深度诊断分析")
    print("=" * 60)
    
    # 加载数据
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(os.path.dirname(current_dir), 'data')
    data_file = os.path.join(data_dir, 'ml_features_complete_20250718_204428.parquet')
    
    df = pd.read_parquet(data_file)
    
    print(f"📊 数据基本信息:")
    print(f"  总样本数: {len(df)}")
    print(f"  时间跨度: {df['year'].min()}-{df['year'].max()}")
    
    # 按年份分析数据分布
    print(f"\n📅 各年份数据分布:")
    yearly_stats = df.groupby('year').agg({
        'label': ['count', lambda x: sum(x==1), lambda x: sum(x==-1), 'mean'],
        'pnl_pct': ['mean', 'std', 'min', 'max']
    }).round(4)
    
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        total = len(year_data)
        wins = sum(year_data['label'] == 1)
        losses = sum(year_data['label'] == -1)
        win_rate = wins / total
        avg_return = year_data['pnl_pct'].mean()
        cum_return = (1 + year_data.sort_values('open_date')['pnl_pct']).prod() - 1
        
        print(f"  {year}: {total}条, 胜率{win_rate:.2%}, 平均收益{avg_return:.2%}, 累计收益{cum_return:.2%}")
    
    # 检查数据质量问题
    print(f"\n🔍 数据质量检查:")
    
    # 1. 检查是否有重复交易
    duplicates = df.duplicated(subset=['code', 'open_date']).sum()
    print(f"  重复交易: {duplicates} 条")
    
    # 2. 检查异常收益率
    extreme_returns = df[(df['pnl_pct'] > 0.2) | (df['pnl_pct'] < -0.2)]
    print(f"  极端收益率(>20%或<-20%): {len(extreme_returns)} 条")
    
    # 3. 检查止盈止损执行情况
    stop_loss_triggered = df[df['exit_reason'].str.contains('止损', na=False)]
    take_profit_triggered = df[df['exit_reason'].str.contains('止盈', na=False)]
    print(f"  止损触发: {len(stop_loss_triggered)} 条")
    print(f"  止盈触发: {len(take_profit_triggered)} 条")
    
    # 4. 检查持仓天数分布
    print(f"  平均持仓天数: {df['hold_days'].mean():.1f} 天")
    print(f"  持仓天数范围: {df['hold_days'].min()}-{df['hold_days'].max()} 天")
    
    # 准备特征
    exclude_cols = [
        'code', 'open_date', 'open_time', 'close_date', 'close_time', 'year',
        'label', 'trade_result', 'action',
        'stop_loss', 'take_profit', 'shares', 'actual_cost',
        'exit_price', 'pnl', 'pnl_pct', 'hold_days',
        'signal_reason', 'exit_reason', 'enter_reason', 'candidate_status',
        'bi_start_time', 'bi_end_time'
    ]
    
    valid_prefixes = [
        'prev_', 'current_5min_', 'has_down_bi', 'price_vs_bi',
        'bi_1_', 'bi_2_', 'bi_3_', 'bi_4_', 'bi_5_',
        'duan_1_', 'duan_2_', 'duan_3_', 'zhongshu_',
        'limit_up_', 'signal_price'
    ]
    
    feature_cols = []
    for col in df.columns:
        if col not in exclude_cols:
            is_valid = any(col.startswith(prefix) for prefix in valid_prefixes)
            if is_valid:
                feature_cols.append(col)
    
    X = df[feature_cols].fillna(0).replace([np.inf, -np.inf], 0)
    y = df['label']
    
    print(f"\n📊 特征分析:")
    print(f"  有效特征数: {len(feature_cols)}")
    print(f"  样本/特征比例: {len(df) / len(feature_cols):.2f}")
    
    # 分析不同训练策略
    print(f"\n🧪 不同训练策略对比:")
    
    strategies = [
        {
            'name': '原策略(2021-2023训练)',
            'train_years': [2021, 2022, 2023],
            'test_years': [2024, 2025]
        },
        {
            'name': '新策略(2023-2024训练)',
            'train_years': [2023, 2024],
            'test_years': [2025]
        },
        {
            'name': '滚动策略(2022-2024训练)',
            'train_years': [2022, 2023, 2024],
            'test_years': [2025]
        }
    ]
    
    models = {
        'RandomForest': RandomForestClassifier(n_estimators=50, max_depth=5, random_state=42),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=50, max_depth=3, random_state=42),
        'LightGBM': lgb.LGBMClassifier(n_estimators=50, max_depth=3, random_state=42, verbose=-1)
    }
    
    results = {}
    
    for strategy in strategies:
        print(f"\n  📈 {strategy['name']}:")
        
        # 准备训练测试数据
        train_mask = df['year'].isin(strategy['train_years'])
        X_train = X[train_mask]
        y_train = y[train_mask]
        
        print(f"    训练样本: {len(X_train)} 个 ({strategy['train_years']})")
        
        strategy_results = {}
        
        for test_year in strategy['test_years']:
            if test_year not in df['year'].values:
                continue
                
            test_mask = df['year'] == test_year
            X_test = X[test_mask]
            y_test = y[test_mask]
            df_test = df[test_mask].copy()
            
            print(f"\n    {test_year}年测试结果:")
            
            year_results = {}
            
            for model_name, model in models.items():
                try:
                    # 训练模型
                    model.fit(X_train, y_train)
                    
                    # 预测
                    y_pred_proba = model.predict_proba(X_test)[:, 1]
                    
                    # 寻找最佳阈值
                    best_threshold = None
                    best_nav = 0
                    
                    for threshold in np.arange(0.1, 0.8, 0.05):
                        selected_mask = y_pred_proba >= threshold
                        selected_df = df_test[selected_mask]
                        
                        if len(selected_df) >= 3:
                            selected_sorted = selected_df.sort_values('open_date')
                            nav = (1 + selected_sorted['pnl_pct']).prod()
                            
                            if nav > best_nav:
                                best_nav = nav
                                best_threshold = threshold
                    
                    if best_threshold is not None:
                        selected_mask = y_pred_proba >= best_threshold
                        selected_df = df_test[selected_mask]
                        win_count = sum(selected_df['label'] == 1)
                        win_rate = win_count / len(selected_df)
                        
                        print(f"      {model_name}: 阈值{best_threshold:.2f}, "
                              f"{len(selected_df)}个信号, 胜率{win_rate:.2%}, "
                              f"净值{best_nav:.4f}, 收益{(best_nav-1)*100:.2f}%")
                        
                        year_results[model_name] = {
                            'threshold': best_threshold,
                            'signals': len(selected_df),
                            'win_rate': win_rate,
                            'nav': best_nav,
                            'return': (best_nav - 1) * 100
                        }
                    else:
                        print(f"      {model_name}: 未找到有效阈值")
                        year_results[model_name] = None
                        
                except Exception as e:
                    print(f"      {model_name}: 训练失败 - {e}")
                    year_results[model_name] = None
            
            strategy_results[test_year] = year_results
        
        results[strategy['name']] = strategy_results
    
    # 汇总最佳策略
    print(f"\n" + "=" * 60)
    print(f"🏆 最佳训练策略汇总")
    print(f"=" * 60)
    
    for strategy_name, strategy_results in results.items():
        print(f"\n{strategy_name}:")
        for year, year_results in strategy_results.items():
            if year_results:
                best_model = max(year_results.items(), 
                               key=lambda x: x[1]['nav'] if x[1] is not None else 0)
                if best_model[1] is not None:
                    result = best_model[1]
                    print(f"  {year}年最佳: {best_model[0]} - "
                          f"收益{result['return']:.2f}%, 胜率{result['win_rate']:.2%}")
    
    # 保存诊断结果
    results_dir = os.path.join(os.path.dirname(current_dir), 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存年度统计
    yearly_summary = []
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        yearly_summary.append({
            'year': year,
            'total_signals': len(year_data),
            'win_signals': sum(year_data['label'] == 1),
            'loss_signals': sum(year_data['label'] == -1),
            'win_rate': sum(year_data['label'] == 1) / len(year_data),
            'avg_return': year_data['pnl_pct'].mean(),
            'cum_return': (1 + year_data.sort_values('open_date')['pnl_pct']).prod() - 1,
            'avg_hold_days': year_data['hold_days'].mean()
        })
    
    yearly_df = pd.DataFrame(yearly_summary)
    output_file = os.path.join(results_dir, 'yearly_analysis.csv')
    yearly_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 诊断完成，年度分析已保存到: {output_file}")

if __name__ == "__main__":
    deep_diagnosis()
