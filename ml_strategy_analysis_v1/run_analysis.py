#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机器学习策略分析 V1 运行脚本
"""

import os
import sys

def main():
    """主函数"""
    print("🚀 机器学习策略分析 V1")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    programs_dir = os.path.join(current_dir, 'programs')
    
    # 添加programs目录到Python路径
    sys.path.insert(0, programs_dir)
    
    print("📁 项目目录结构:")
    print(f"  项目根目录: {current_dir}")
    print(f"  程序目录: {programs_dir}")
    print(f"  数据目录: {os.path.join(current_dir, 'data')}")
    print(f"  结果目录: {os.path.join(current_dir, 'results')}")
    print()
    
    print("📋 可用的分析程序:")
    print("  1. precise_backtest_engine.py - 精确回测引擎")
    print("  2. ml_strategy_optimizer.py - 机器学习优化器")
    print("  3. test_ml_workflow.py - 工作流程测试")
    print("  4. extended_data_collection_strategy.py - 扩展数据收集策略")
    print("  5. extended_backtest_engine.py - 扩展回测引擎")
    print("  6. generate_large_ml_dataset.py - 大数据集生成器")
    print()
    
    print("💡 使用方法:")
    print("  1. 首先运行回测引擎生成数据:")
    print("     python programs/precise_backtest_engine.py")
    print()
    print("  2. 然后运行机器学习优化器:")
    print("     python programs/ml_strategy_optimizer.py")
    print()
    print("  3. 或者测试工作流程:")
    print("     python programs/test_ml_workflow.py")
    print()
    
    print("📊 当前数据文件:")
    data_dir = os.path.join(current_dir, 'data')
    if os.path.exists(data_dir):
        data_files = os.listdir(data_dir)
        if data_files:
            for file in sorted(data_files):
                print(f"  - {file}")
        else:
            print("  (暂无数据文件)")
    else:
        print("  (数据目录不存在)")
    
    print()
    print("📈 当前结果文件:")
    results_dir = os.path.join(current_dir, 'results')
    if os.path.exists(results_dir):
        result_files = os.listdir(results_dir)
        if result_files:
            for file in sorted(result_files):
                print(f"  - {file}")
        else:
            print("  (暂无结果文件)")
    else:
        print("  (结果目录不存在)")
    
    print()
    print("📖 详细说明请查看: docs/README.md")
    print("=" * 50)

if __name__ == "__main__":
    main()
