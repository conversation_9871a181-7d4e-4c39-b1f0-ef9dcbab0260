#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连续涨停反弹策略
基于缠论笔分析的量化交易策略
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加chanlun-pro路径
sys.path.append(r'D:\git\chan-pro\chanlun-pro\src')

@dataclass
class StockCandidate:
    """候选股票数据结构"""
    code: str
    enter_date: datetime
    limit_up_dates: List[datetime] = field(default_factory=list)
    status: str = "primary_monitoring"  # primary_monitoring, secondary_monitoring, entered, exited
    decline_bi_low: float = 0.0
    decline_bi_start: datetime = None
    decline_bi_end: datetime = None
    has_down_bi: bool = False
    daily_data: pd.DataFrame = None
    min5_data: pd.DataFrame = None

@dataclass
class Position:
    """持仓信息"""
    code: str
    enter_price: float
    enter_date: datetime
    stop_loss: float
    take_profit: float
    shares: int
    enter_reason: str = ""

@dataclass
class TradeRecord:
    """交易记录"""
    code: str
    action: str  # buy/sell
    price: float
    shares: int
    date: datetime
    reason: str
    pnl: float = 0.0

class DataAdapter:
    """数据适配器 - 处理不同格式的数据"""
    
    def __init__(self, data_path: str):
        self.data_path = Path(data_path)
        self.data_cache = {}
        
    def load_stock_data(self, code: str) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载股票的日线和5分钟数据"""
        if code in self.data_cache:
            return self.data_cache[code]

        daily_data = None
        min5_data = None

        # 尝试从分目录加载数据
        daily_files = [
            f"daily/{code}.SZ.parquet",
            f"daily/{code}.SH.parquet",
            f"daily/{code}.parquet",
            f"daily/{code}.pkl",
            f"daily/{code}.csv"
        ]

        min5_files = [
            f"minute_5/{code}.SZ.parquet",
            f"minute_5/{code}.SH.parquet",
            f"minute_5/{code}.parquet",
            f"minute_5/{code}.pkl",
            f"minute_5/{code}.csv"
        ]

        # 加载日线数据
        for filename in daily_files:
            file_path = self.data_path / filename
            if file_path.exists():
                try:
                    daily_data, _ = self._load_file(file_path)
                    if daily_data is not None:
                        break
                except Exception as e:
                    print(f"⚠️ 加载日线数据 {filename} 失败: {e}")
                    continue

        # 加载5分钟数据
        for filename in min5_files:
            file_path = self.data_path / filename
            if file_path.exists():
                try:
                    _, min5_data = self._load_file(file_path)
                    if min5_data is not None:
                        break
                except Exception as e:
                    print(f"⚠️ 加载5分钟数据 {filename} 失败: {e}")
                    continue

        # 缓存结果
        self.data_cache[code] = (daily_data, min5_data)
        return daily_data, min5_data
    
    def _load_file(self, file_path: Path) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载单个文件"""
        suffix = file_path.suffix.lower()
        
        if suffix == '.pkl':
            return self._load_pickle_file(file_path)
        elif suffix == '.csv':
            return self._load_csv_file(file_path)
        elif suffix in ['.h5', '.hdf5']:
            return self._load_hdf5_file(file_path)
        elif suffix == '.parquet':
            return self._load_parquet_file(file_path)
        else:
            return None, None
    
    def _load_pickle_file(self, file_path: Path) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载pickle文件"""
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        daily_data = None
        min5_data = None
        
        if isinstance(data, dict):
            # 尝试不同的键名
            daily_keys = ['daily', 'day', 'd', '日线', 'D']
            min5_keys = ['5min', '5m', 'min5', '5分钟', '5']
            
            for key in daily_keys:
                if key in data and isinstance(data[key], pd.DataFrame):
                    daily_data = data[key].copy()
                    break
            
            for key in min5_keys:
                if key in data and isinstance(data[key], pd.DataFrame):
                    min5_data = data[key].copy()
                    break
        
        elif isinstance(data, pd.DataFrame):
            # 根据数据量判断是日线还是5分钟
            if len(data) > 10000:  # 假设5分钟数据量更大
                min5_data = data.copy()
            else:
                daily_data = data.copy()
        
        # 标准化列名
        if daily_data is not None:
            daily_data = self._standardize_columns(daily_data)
        if min5_data is not None:
            min5_data = self._standardize_columns(min5_data)
        
        return daily_data, min5_data
    
    def _load_csv_file(self, file_path: Path) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                df = self._standardize_columns(df)
                
                # 根据数据量判断类型
                if len(df) > 10000:
                    return None, df
                else:
                    return df, None
            except:
                continue
        
        return None, None
    
    def _load_hdf5_file(self, file_path: Path) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载HDF5文件"""
        try:
            with pd.HDFStore(file_path, mode='r') as store:
                keys = store.keys()
                
                daily_data = None
                min5_data = None
                
                for key in keys:
                    df = store[key]
                    df = self._standardize_columns(df)
                    
                    if 'daily' in key.lower() or 'day' in key.lower():
                        daily_data = df
                    elif '5min' in key.lower() or '5m' in key.lower():
                        min5_data = df
                    elif len(df) > 10000:
                        min5_data = df
                    else:
                        daily_data = df
                
                return daily_data, min5_data
        except:
            return None, None

    def _load_parquet_file(self, file_path: Path) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """加载Parquet文件"""
        df = pd.read_parquet(file_path)
        df = self._standardize_columns(df)

        # 根据文件路径判断是日线还是5分钟数据
        if 'daily' in str(file_path):
            return df, None  # 日线数据
        elif 'minute_5' in str(file_path):
            return None, df  # 5分钟数据
        else:
            # 根据数据量判断类型
            if len(df) > 10000:
                return None, df  # 5分钟数据
            else:
                return df, None  # 日线数据
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        df = df.copy()
        
        # 列名映射
        column_mapping = {
            # 开盘价
            'open': 'open', 'o': 'open', '开盘': 'open', '开盘价': 'open',
            # 最高价
            'high': 'high', 'h': 'high', '最高': 'high', '最高价': 'high',
            # 最低价
            'low': 'low', 'l': 'low', '最低': 'low', '最低价': 'low',
            # 收盘价
            'close': 'close', 'c': 'close', '收盘': 'close', '收盘价': 'close',
            # 成交量
            'volume': 'volume', 'vol': 'volume', 'v': 'volume', '成交量': 'volume', '量': 'volume',
            # 时间
            'datetime': 'datetime', 'date': 'datetime', 'time': 'datetime',
            'dt': 'datetime', '时间': 'datetime', '日期': 'datetime'
        }
        
        # 重命名列
        new_columns = {}
        for col in df.columns:
            col_lower = col.lower()
            if col_lower in column_mapping:
                new_columns[col] = column_mapping[col_lower]
        
        if new_columns:
            df = df.rename(columns=new_columns)
        
        # 确保有datetime索引
        if 'datetime' in df.columns and df.index.name != 'datetime':
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.set_index('datetime')
        elif df.index.name is None and hasattr(df.index, 'dtype'):
            try:
                df.index = pd.to_datetime(df.index)
                df.index.name = 'datetime'
            except:
                pass
        
        # 确保数值列是float类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df

class LimitUpDetector:
    """涨停板检测器"""
    
    def __init__(self, limit_up_threshold: float = 0.098):
        self.limit_up_threshold = limit_up_threshold
    
    def detect_consecutive_limit_up(self, daily_data: pd.DataFrame, 
                                   end_date: datetime, 
                                   min_days: int = 2) -> List[datetime]:
        """检测连续涨停"""
        if daily_data is None or len(daily_data) < min_days:
            return []
        
        # 获取指定日期之前的数据
        data_before = daily_data[daily_data.index <= end_date].copy()
        if len(data_before) < min_days:
            return []
        
        # 计算涨跌幅
        data_before['pct_change'] = data_before['close'].pct_change()
        
        # 检测涨停日期
        limit_up_mask = data_before['pct_change'] >= self.limit_up_threshold
        
        # 查找连续涨停
        limit_up_dates = []
        consecutive_count = 0
        
        for date, is_limit_up in limit_up_mask.tail(10).items():  # 只检查最近10天
            if is_limit_up:
                consecutive_count += 1
                limit_up_dates.append(date)
            else:
                if consecutive_count >= min_days:
                    return limit_up_dates[-consecutive_count:]
                consecutive_count = 0
                limit_up_dates = []
        
        # 检查最后的连续涨停
        if consecutive_count >= min_days:
            return limit_up_dates[-consecutive_count:]
        
        return []
    
    def has_decline_after_limit_up(self, daily_data: pd.DataFrame, 
                                  limit_up_dates: List[datetime],
                                  decline_threshold: float = 0.03) -> bool:
        """检查涨停后是否开始下跌"""
        if not limit_up_dates or daily_data is None:
            return False
        
        last_limit_up = max(limit_up_dates)
        
        # 获取涨停后的数据
        after_data = daily_data[daily_data.index > last_limit_up]
        if len(after_data) == 0:
            return False
        
        # 计算从涨停最高点的回撤
        limit_up_high = daily_data.loc[last_limit_up, 'high']
        current_low = after_data['low'].min()
        
        decline_pct = (limit_up_high - current_low) / limit_up_high
        
        return decline_pct >= decline_threshold

class LimitUpReversalStrategy:
    """连续涨停反弹策略主类"""
    
    def __init__(self, 
                 data_path: str,
                 initial_capital: float = 1000000,
                 max_positions: int = 10,
                 monitor_days: int = 15,
                 stop_loss_pct: float = 0.01,
                 profit_loss_ratio: float = 1.5):
        
        # 基础参数
        self.data_path = data_path
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.position_size = initial_capital / max_positions
        self.monitor_days = monitor_days
        self.stop_loss_pct = stop_loss_pct
        self.profit_loss_ratio = profit_loss_ratio
        
        # 组件初始化
        self.data_adapter = DataAdapter(data_path)
        self.limit_up_detector = LimitUpDetector()
        
        # 状态管理
        self.candidates: Dict[str, StockCandidate] = {}
        self.positions: Dict[str, Position] = {}
        self.trade_records: List[TradeRecord] = []
        self.current_capital = initial_capital
        
        # 统计信息
        self.stats = {
            'total_scanned': 0,
            'candidates_found': 0,
            'positions_entered': 0,
            'trades_completed': 0
        }
    
    def get_stock_list(self) -> List[str]:
        """获取所有股票代码列表"""
        stock_codes = []

        # 从daily目录获取股票列表
        daily_path = Path(self.data_path) / "daily"
        if daily_path.exists():
            for pattern in ["*.pkl", "*.parquet", "*.csv"]:
                for file_path in daily_path.glob(pattern):
                    # 提取股票代码
                    filename = file_path.stem
                    # 处理不同的命名格式
                    if filename.startswith('SZ') or filename.startswith('SH'):
                        code = filename[2:]
                    elif '.' in filename:
                        # 处理 000001.SZ 格式
                        parts = filename.split('.')
                        code = parts[0]
                    else:
                        code = filename

                    # 验证是否是6位数字的股票代码
                    if len(code) == 6 and code.isdigit():
                        stock_codes.append(code)

        return sorted(list(set(stock_codes)))
    
    def scan_market(self, scan_date: datetime) -> int:
        """扫描市场寻找符合条件的股票"""
        stock_list = self.get_stock_list()
        candidates_found = 0
        
        print(f"📊 {scan_date.date()} 扫描市场，共 {len(stock_list)} 只股票")
        
        for i, code in enumerate(stock_list):
            if i % 100 == 0:
                print(f"  进度: {i}/{len(stock_list)}")
            
            try:
                daily_data, min5_data = self.data_adapter.load_stock_data(code)
                if daily_data is None:
                    continue
                
                # 检测连续涨停
                limit_up_dates = self.limit_up_detector.detect_consecutive_limit_up(
                    daily_data, scan_date
                )
                
                if limit_up_dates:
                    # 检查是否开始下跌
                    if self.limit_up_detector.has_decline_after_limit_up(
                        daily_data, limit_up_dates
                    ):
                        # 添加到候选池
                        candidate = StockCandidate(
                            code=code,
                            enter_date=scan_date,
                            limit_up_dates=limit_up_dates,
                            daily_data=daily_data,
                            min5_data=min5_data
                        )
                        
                        self.candidates[code] = candidate
                        candidates_found += 1
                        
                        print(f"  ✅ 发现候选股票: {code}, 涨停日期: {[d.date() for d in limit_up_dates]}")
            
            except Exception as e:
                # print(f"  ⚠️ 处理 {code} 时出错: {e}")
                continue
        
        self.stats['total_scanned'] += len(stock_list)
        self.stats['candidates_found'] += candidates_found
        
        print(f"📈 扫描完成，发现 {candidates_found} 个候选股票")
        return candidates_found
    
    def print_status(self, date: datetime):
        """打印当前状态"""
        print(f"\n📊 {date.date()} 策略状态:")
        print(f"  候选股票: {len(self.candidates)}")
        print(f"  持仓数量: {len(self.positions)}")
        print(f"  当前资金: {self.current_capital:.2f}")
        print(f"  已完成交易: {self.stats['trades_completed']}")
        
        if self.candidates:
            print("  候选股票列表:")
            for code, candidate in list(self.candidates.items())[:5]:
                days_monitored = (date - candidate.enter_date).days
                print(f"    {code}: 监控 {days_monitored} 天")
        
        if self.positions:
            print("  当前持仓:")
            for code, position in self.positions.items():
                days_held = (date - position.enter_date).days
                print(f"    {code}: 价格 {position.enter_price:.2f}, 持有 {days_held} 天")

def main():
    """主函数 - 用于测试"""
    print("🚀 连续涨停反弹策略测试")
    
    # 首先运行数据探索
    print("\n第一步：数据探索")
    os.system("python data_explorer.py")
    
    # 初始化策略
    print("\n第二步：策略初始化")
    strategy = LimitUpReversalStrategy(
        data_path=r"D:\git\czsc-new\stock_data",
        initial_capital=1000000,
        max_positions=10
    )
    
    # 测试获取股票列表
    stock_list = strategy.get_stock_list()
    print(f"📊 发现 {len(stock_list)} 只股票")
    if stock_list:
        print(f"前10只股票: {stock_list[:10]}")
    
    # 测试扫描市场
    test_date = datetime(2023, 6, 1)
    print(f"\n第三步：测试市场扫描 ({test_date.date()})")
    candidates_found = strategy.scan_market(test_date)
    
    strategy.print_status(test_date)
    
    print("\n✅ 基础测试完成！")

if __name__ == "__main__":
    main()
