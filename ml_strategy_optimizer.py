#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机器学习策略优化器
用于训练模型并测试策略优化效果

使用方法：
1. 先运行 precise_backtest_engine.py 生成完整的回测数据
2. 运行此脚本进行机器学习训练和测试
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关库
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb

# 绘图库
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class MLStrategyOptimizer:
    """机器学习策略优化器"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.results = {}
        
    def load_data(self, data_file):
        """加载回测数据"""
        try:
            if data_file.endswith('.parquet'):
                data = pd.read_parquet(data_file)
            else:
                data = pd.read_csv(data_file, encoding='utf-8-sig')
            
            print(f"✅ 成功加载数据: {len(data)} 条记录")
            print(f"📅 时间跨度: {data['year'].min()}-{data['year'].max()}")
            print(f"📊 特征维度: {len([col for col in data.columns if col not in ['code', 'open_date', 'year', 'label', 'trade_result']])} 个")
            
            return data
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def prepare_features(self, data):
        """准备特征数据"""
        # 排除非特征列
        exclude_cols = ['code', 'open_date', 'open_time', 'close_date', 'close_time',
                       'year', 'label', 'trade_result', 'action', 'signal_reason',
                       'exit_reason', 'enter_reason', 'bi_start_time', 'bi_end_time',
                       'candidate_status']  # 排除字符串类型的状态列

        feature_cols = [col for col in data.columns if col not in exclude_cols]

        # 提取特征和标签
        X = data[feature_cols].copy()
        y = data['label'].copy()

        # 处理字符串类型的列
        for col in X.columns:
            if X[col].dtype == 'object':
                # 尝试转换为数值，失败则删除该列
                try:
                    X[col] = pd.to_numeric(X[col], errors='coerce')
                except:
                    print(f"⚠️ 删除非数值列: {col}")
                    X = X.drop(columns=[col])
                    feature_cols.remove(col)

        # 处理缺失值
        X = X.fillna(0)

        # 处理无穷大值
        X = X.replace([np.inf, -np.inf], 0)
        
        print(f"📊 特征准备完成:")
        print(f"  特征数量: {len(feature_cols)}")
        print(f"  样本数量: {len(X)}")
        print(f"  正样本: {sum(y == 1)} 条 ({sum(y == 1)/len(y):.2%})")
        print(f"  负样本: {sum(y == -1)} 条 ({sum(y == -1)/len(y):.2%})")
        
        return X, y, feature_cols
    
    def train_models(self, X_train, y_train, feature_cols):
        """训练多种机器学习模型"""
        print("\n🤖 开始训练机器学习模型...")
        
        # 标准化特征
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        self.scalers['standard'] = scaler
        
        # 定义模型
        models = {
            'RandomForest': RandomForestClassifier(
                n_estimators=100, 
                max_depth=10, 
                random_state=42,
                n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            ),
            'XGBoost': xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42,
                eval_metric='logloss'
            ),
            'LightGBM': lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42,
                verbose=-1
            ),
            'LogisticRegression': LogisticRegression(
                random_state=42,
                max_iter=1000
            ),
            'SVM': SVC(
                kernel='rbf',
                probability=True,
                random_state=42
            )
        }
        
        # 训练模型
        for name, model in models.items():
            print(f"  训练 {name}...")
            try:
                if name in ['LogisticRegression', 'SVM']:
                    # 线性模型使用标准化数据
                    model.fit(X_train_scaled, y_train)
                    # 交叉验证
                    cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='accuracy')
                else:
                    # 树模型使用原始数据
                    model.fit(X_train, y_train)
                    # 交叉验证
                    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
                
                self.models[name] = model
                
                print(f"    ✅ {name} 训练完成")
                print(f"    📊 交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
                
                # 记录特征重要性（如果模型支持）
                if hasattr(model, 'feature_importances_'):
                    importance_df = pd.DataFrame({
                        'feature': feature_cols,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    self.feature_importance[name] = importance_df
                    
            except Exception as e:
                print(f"    ❌ {name} 训练失败: {e}")
        
        print(f"✅ 模型训练完成，成功训练 {len(self.models)} 个模型")
    
    def evaluate_models(self, X_test, y_test, test_data, year):
        """评估模型性能"""
        print(f"\n📊 评估 {year} 年模型性能...")
        
        results = {}
        
        for name, model in self.models.items():
            try:
                # 预测
                if name in ['LogisticRegression', 'SVM']:
                    X_test_scaled = self.scalers['standard'].transform(X_test)
                    y_pred = model.predict(X_test_scaled)
                    y_prob = model.predict_proba(X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
                else:
                    y_pred = model.predict(X_test)
                    y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
                
                # 计算指标
                accuracy = (y_pred == y_test).mean()
                
                # 只选择预测为正的交易
                positive_mask = y_pred == 1
                selected_trades = test_data[positive_mask].copy()
                
                if len(selected_trades) > 0:
                    # 计算选择后的胜率和收益
                    selected_wins = sum(selected_trades['label'] == 1)
                    selected_losses = sum(selected_trades['label'] == -1)
                    selected_win_rate = selected_wins / len(selected_trades)
                    
                    # 计算净值（简化计算）
                    selected_pnl = selected_trades['pnl_pct'].sum()
                    
                    results[name] = {
                        'accuracy': accuracy,
                        'total_trades': len(selected_trades),
                        'win_trades': selected_wins,
                        'loss_trades': selected_losses,
                        'win_rate': selected_win_rate,
                        'total_pnl_pct': selected_pnl,
                        'avg_pnl_pct': selected_pnl / len(selected_trades),
                        'predictions': y_pred,
                        'probabilities': y_prob
                    }
                    
                    print(f"  {name}:")
                    print(f"    准确率: {accuracy:.4f}")
                    print(f"    选择交易: {len(selected_trades)} 笔")
                    print(f"    选择后胜率: {selected_win_rate:.4f}")
                    print(f"    平均收益率: {selected_pnl/len(selected_trades):.4f}")
                else:
                    print(f"  {name}: 没有选择任何交易")
                    
            except Exception as e:
                print(f"  ❌ {name} 评估失败: {e}")
        
        self.results[year] = results
        return results
    
    def compare_strategies(self, original_data, year):
        """对比原策略和优化策略"""
        print(f"\n📈 {year} 年策略对比:")
        
        # 原策略统计
        original_trades = len(original_data)
        original_wins = sum(original_data['label'] == 1)
        original_win_rate = original_wins / original_trades
        original_pnl = original_data['pnl_pct'].sum()
        
        print(f"原策略:")
        print(f"  总交易: {original_trades} 笔")
        print(f"  胜率: {original_win_rate:.4f}")
        print(f"  总收益率: {original_pnl:.4f}")
        print(f"  平均收益率: {original_pnl/original_trades:.4f}")
        
        # 各模型优化后统计
        if year in self.results:
            print(f"\n优化策略:")
            for name, result in self.results[year].items():
                if result['total_trades'] > 0:
                    improvement = result['win_rate'] - original_win_rate
                    print(f"  {name}:")
                    print(f"    选择交易: {result['total_trades']} 笔 (选择率: {result['total_trades']/original_trades:.2%})")
                    print(f"    胜率: {result['win_rate']:.4f} (提升: {improvement:+.4f})")
                    print(f"    平均收益率: {result['avg_pnl_pct']:.4f}")

    def plot_feature_importance(self, model_name, top_n=20):
        """绘制特征重要性"""
        if model_name not in self.feature_importance:
            print(f"❌ {model_name} 没有特征重要性数据")
            return

        importance_df = self.feature_importance[model_name].head(top_n)

        plt.figure(figsize=(12, 8))
        sns.barplot(data=importance_df, x='importance', y='feature')
        plt.title(f'{model_name} 特征重要性 (Top {top_n})')
        plt.xlabel('重要性')
        plt.tight_layout()
        plt.savefig(f'feature_importance_{model_name}.png', dpi=300, bbox_inches='tight')
        plt.show()

    def save_results(self, filename=None):
        """保存结果"""
        if filename is None:
            filename = f'ml_optimization_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

        all_results = []
        for year, year_results in self.results.items():
            for model_name, result in year_results.items():
                all_results.append({
                    'year': year,
                    'model': model_name,
                    'accuracy': result['accuracy'],
                    'total_trades': result['total_trades'],
                    'win_rate': result['win_rate'],
                    'avg_pnl_pct': result['avg_pnl_pct'],
                    'total_pnl_pct': result['total_pnl_pct']
                })

        results_df = pd.DataFrame(all_results)
        results_df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"✅ 结果已保存到 {filename}")

        return results_df


def main():
    """主函数"""
    print("🚀 机器学习策略优化器")
    print("=" * 50)

    # 初始化优化器
    optimizer = MLStrategyOptimizer()

    # 查找最新的完整数据文件
    import glob
    import os

    data_files = glob.glob('ml_features_complete_*.parquet')
    if not data_files:
        data_files = glob.glob('ml_features_complete_*.csv')

    if not data_files:
        print("❌ 未找到回测数据文件")
        print("请先运行 precise_backtest_engine.py 生成回测数据")
        return

    # 使用最新的数据文件
    latest_file = max(data_files, key=os.path.getctime)
    print(f"📁 使用数据文件: {latest_file}")

    # 加载数据
    data = optimizer.load_data(latest_file)
    if data is None:
        return

    # 准备特征
    X, y, feature_cols = optimizer.prepare_features(data)

    # 按年份分割数据
    train_years = [2021, 2022, 2023]  # 训练数据
    test_years = [2024, 2025]         # 测试数据

    # 准备训练数据
    train_mask = data['year'].isin(train_years)
    X_train = X[train_mask]
    y_train = y[train_mask]
    train_data = data[train_mask]

    print(f"\n📚 训练数据 ({'-'.join(map(str, train_years))}):")
    print(f"  样本数量: {len(X_train)}")
    print(f"  正样本: {sum(y_train == 1)} 条 ({sum(y_train == 1)/len(y_train):.2%})")
    print(f"  负样本: {sum(y_train == -1)} 条 ({sum(y_train == -1)/len(y_train):.2%})")

    # 训练模型
    optimizer.train_models(X_train, y_train, feature_cols)

    # 测试每一年
    for test_year in test_years:
        if test_year in data['year'].values:
            test_mask = data['year'] == test_year
            X_test = X[test_mask]
            y_test = y[test_mask]
            test_data = data[test_mask]

            print(f"\n📊 测试数据 ({test_year} 年):")
            print(f"  样本数量: {len(X_test)}")
            print(f"  正样本: {sum(y_test == 1)} 条 ({sum(y_test == 1)/len(y_test):.2%})")
            print(f"  负样本: {sum(y_test == -1)} 条 ({sum(y_test == -1)/len(y_test):.2%})")

            # 评估模型
            optimizer.evaluate_models(X_test, y_test, test_data, test_year)

            # 对比策略
            optimizer.compare_strategies(test_data, test_year)

    # 绘制最佳模型的特征重要性
    if optimizer.feature_importance:
        best_model = list(optimizer.feature_importance.keys())[0]
        optimizer.plot_feature_importance(best_model)

    # 保存结果
    results_df = optimizer.save_results()

    print("\n🎉 机器学习优化完成！")
    print("=" * 50)


if __name__ == "__main__":
    main()
