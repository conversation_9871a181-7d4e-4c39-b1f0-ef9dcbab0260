#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试root用户连接并创建chanlun用户
"""

import pymysql

def test_root_connection():
    """使用root用户连接并创建chanlun用户"""
    print("🔍 使用root用户连接MariaDB...")
    print("=" * 50)
    
    try:
        # 使用root用户连接
        connection = pymysql.connect(
            host='***********',
            port=3307,
            user='root',
            password='123456',  # 根据截图，应该是默认密码
            charset='utf8mb4'
        )
        
        print("✅ Root用户连接成功!")
        
        with connection.cursor() as cursor:
            # 检查版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MariaDB版本: {version[0]}")
            
            # 检查现有用户
            cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'chanlun'")
            existing_user = cursor.fetchone()
            
            if existing_user:
                print("⚠️  用户chanlun已存在，删除后重新创建...")
                cursor.execute("DROP USER 'chanlun'@'%'")
            
            # 创建chanlun用户
            print("🔧 创建chanlun用户...")
            cursor.execute("CREATE USER 'chanlun'@'%' IDENTIFIED BY '12345678'")
            print("✅ 用户创建成功")
            
            # 创建chanlun数据库
            print("🔧 创建chanlun数据库...")
            cursor.execute("CREATE DATABASE IF NOT EXISTS chanlun CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ 数据库创建成功")
            
            # 授权
            print("🔧 授权用户...")
            cursor.execute("GRANT ALL PRIVILEGES ON chanlun.* TO 'chanlun'@'%'")
            cursor.execute("FLUSH PRIVILEGES")
            print("✅ 授权成功")
            
            # 验证用户创建
            cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'chanlun'")
            user_info = cursor.fetchone()
            print(f"👤 用户验证: {user_info}")
            
            # 验证数据库创建
            cursor.execute("SHOW DATABASES LIKE 'chanlun'")
            db_info = cursor.fetchone()
            print(f"📊 数据库验证: {db_info}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Root连接失败: {e}")
        print("\n💡 可能的原因:")
        print("1. Root密码不是123456")
        print("2. MariaDB配置问题")
        
        return False

def test_chanlun_connection():
    """测试chanlun用户连接"""
    print("\n🔍 测试chanlun用户连接...")
    print("=" * 50)
    
    try:
        connection = pymysql.connect(
            host='***********',
            port=3307,
            user='chanlun',
            password='12345678',
            database='chanlun',
            charset='utf8mb4'
        )
        
        print("✅ chanlun用户连接成功!")
        
        with connection.cursor() as cursor:
            # 测试基本操作
            cursor.execute("SELECT DATABASE()")
            db = cursor.fetchone()
            print(f"📊 当前数据库: {db[0]}")
            
            # 测试创建表权限
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ 创建表权限正常")
            
            # 测试插入数据
            cursor.execute("INSERT INTO test_table (name) VALUES ('test')")
            connection.commit()
            print("✅ 插入数据权限正常")
            
            # 测试查询数据
            cursor.execute("SELECT COUNT(*) FROM test_table")
            count = cursor.fetchone()
            print(f"📊 测试表记录数: {count[0]}")
            
            # 清理测试表
            cursor.execute("DROP TABLE test_table")
            print("✅ 删除表权限正常")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ chanlun用户连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 MariaDB用户配置和测试")
    print("=" * 60)
    
    # 1. 使用root创建用户
    if test_root_connection():
        print("\n🎉 用户创建成功!")
        
        # 2. 测试chanlun用户
        if test_chanlun_connection():
            print("\n🎉 NAS数据库配置完成!")
            print("💡 现在可以运行:")
            print("   uv run python test_nas_connection.py")
            print("   uv run python migrate_to_nas.py")
        else:
            print("\n❌ chanlun用户测试失败")
    else:
        print("\n❌ Root用户连接失败")
        print("💡 请检查:")
        print("1. MariaDB是否正在运行")
        print("2. Root密码是否正确")
    
    print("="*60)

if __name__ == "__main__":
    main()
