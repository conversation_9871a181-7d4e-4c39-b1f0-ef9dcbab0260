#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断MariaDB连接问题
"""

import pymysql
import socket

def test_socket_connection():
    """测试原始socket连接"""
    print("🔍 测试原始socket连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('***********', 3307))
        sock.close()
        
        if result == 0:
            print("✅ Socket连接成功")
            return True
        else:
            print(f"❌ Socket连接失败: {result}")
            return False
    except Exception as e:
        print(f"❌ Socket测试异常: {e}")
        return False

def test_mysql_versions():
    """测试不同的MySQL连接参数"""
    print("\n🔍 测试不同连接参数...")
    
    configs = [
        {
            'name': '基本连接',
            'config': {
                'host': '***********',
                'port': 3307,
                'user': 'root',
                'password': '12345678'
            }
        },
        {
            'name': '指定字符集',
            'config': {
                'host': '***********',
                'port': 3307,
                'user': 'root',
                'password': '12345678',
                'charset': 'utf8mb4'
            }
        },
        {
            'name': '禁用SSL',
            'config': {
                'host': '***********',
                'port': 3307,
                'user': 'root',
                'password': '12345678',
                'charset': 'utf8mb4',
                'ssl_disabled': True
            }
        },
        {
            'name': '使用mysql_native_password',
            'config': {
                'host': '***********',
                'port': 3307,
                'user': 'root',
                'password': '12345678',
                'charset': 'utf8mb4',
                'auth_plugin_map': {
                    'mysql_native_password': pymysql.auth.mysql_native_password
                }
            }
        }
    ]
    
    for test_case in configs:
        print(f"\n📋 测试: {test_case['name']}")
        try:
            connection = pymysql.connect(**test_case['config'])
            print("✅ 连接成功!")
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"📊 版本: {version[0]}")
                
                cursor.execute("SELECT USER()")
                user = cursor.fetchone()
                print(f"👤 用户: {user[0]}")
            
            connection.close()
            return test_case['config']
            
        except Exception as e:
            print(f"❌ 失败: {e}")
    
    return None

def test_empty_password():
    """测试空密码"""
    print("\n🔍 测试空密码...")
    try:
        connection = pymysql.connect(
            host='***********',
            port=3307,
            user='root',
            password='',
            charset='utf8mb4'
        )
        print("✅ 空密码连接成功!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ 空密码失败: {e}")
        return False

def test_different_users():
    """测试不同用户名"""
    print("\n🔍 测试不同用户名...")
    
    users = ['root', 'admin', 'mysql', 'mariadb']
    passwords = ['12345678', '', 'admin', 'root']
    
    for user in users:
        for password in passwords:
            try:
                print(f"📋 尝试: {user}:{password}")
                connection = pymysql.connect(
                    host='***********',
                    port=3307,
                    user=user,
                    password=password,
                    charset='utf8mb4',
                    connect_timeout=3
                )
                print(f"✅ 成功: {user}:{password}")
                connection.close()
                return user, password
            except Exception as e:
                print(f"❌ 失败: {user}:{password} - {str(e)[:50]}")
    
    return None, None

def main():
    """主函数"""
    print("🚀 MariaDB连接诊断")
    print("=" * 60)
    
    # 1. 测试socket连接
    if not test_socket_connection():
        print("❌ 基础网络连接失败，请检查:")
        print("1. NAS是否开机")
        print("2. MariaDB服务是否运行")
        print("3. 防火墙是否开放3307端口")
        return
    
    # 2. 测试空密码
    if test_empty_password():
        print("💡 发现root用户使用空密码")
        return
    
    # 3. 测试不同用户
    user, password = test_different_users()
    if user:
        print(f"💡 发现可用用户: {user}:{password}")
        return
    
    # 4. 测试不同连接参数
    working_config = test_mysql_versions()
    if working_config:
        print(f"💡 发现可用配置: {working_config}")
        return
    
    print("\n❌ 所有连接尝试都失败了")
    print("💡 建议:")
    print("1. 检查MariaDB配置文件")
    print("2. 重启MariaDB服务")
    print("3. 查看MariaDB错误日志")
    print("4. 确认用户权限设置")

if __name__ == "__main__":
    main()
