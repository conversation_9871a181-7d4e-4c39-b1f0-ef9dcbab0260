#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测引擎
完整的连续涨停反弹策略回测系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from limit_up_reversal_strategy import LimitUpReversalStrategy, TradeRecord, Position
from chanlun_analyzer import ChanLunAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, strategy: LimitUpReversalStrategy):
        self.strategy = strategy
        self.chanlun_analyzer = ChanLunAnalyzer()
        
        # 回测结果
        self.daily_returns = []
        self.daily_positions = []
        self.daily_capital = []
        self.trade_analysis = {}
        
    def run_backtest(self, start_date: str, end_date: str, 
                    scan_frequency: int = 5) -> Dict:
        """运行完整回测"""
        print("🚀 开始回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"💰 初始资金: {self.strategy.initial_capital:,.2f}")
        print(f"📊 最大持仓: {self.strategy.max_positions}")
        
        # 生成交易日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        scan_counter = 0
        
        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
            
            try:
                # 定期扫描市场（每N天扫描一次）
                if scan_counter % scan_frequency == 0:
                    self._scan_market_on_date(current_date)
                
                # 每日处理
                self._process_daily(current_date)
                
                # 记录每日状态
                self._record_daily_status(current_date)
                
                # 定期打印状态
                if current_date.day % 10 == 0:
                    self.strategy.print_status(current_date)
                
                scan_counter += 1
                current_date += timedelta(days=1)
                
            except Exception as e:
                print(f"⚠️ 处理日期 {current_date.date()} 时出错: {e}")
                current_date += timedelta(days=1)
                continue
        
        # 生成回测报告
        return self._generate_backtest_report()
    
    def _scan_market_on_date(self, date: datetime):
        """在指定日期扫描市场"""
        candidates_found = self.strategy.scan_market(date)
        if candidates_found > 0:
            print(f"📈 {date.date()} 发现 {candidates_found} 个候选股票")
    
    def _process_daily(self, date: datetime):
        """处理每日逻辑"""
        # 1. 清理超时候选股票
        self._cleanup_expired_candidates(date)
        
        # 2. 分析5分钟信号并建仓
        self._analyze_and_enter_positions(date)
        
        # 3. 检查止盈止损
        self._check_exit_conditions(date)
    
    def _cleanup_expired_candidates(self, date: datetime):
        """清理超时的候选股票"""
        expired_codes = []
        
        for code, candidate in self.strategy.candidates.items():
            days_monitored = (date - candidate.enter_date).days
            if days_monitored > self.strategy.monitor_days:
                expired_codes.append(code)
        
        for code in expired_codes:
            del self.strategy.candidates[code]
            if expired_codes:
                print(f"🗑️ 清理超时候选股票: {len(expired_codes)} 只")
    
    def _analyze_and_enter_positions(self, date: datetime):
        """分析5分钟信号并建仓"""
        if len(self.strategy.positions) >= self.strategy.max_positions:
            return  # 已满仓
        
        for code in list(self.strategy.candidates.keys()):
            if len(self.strategy.positions) >= self.strategy.max_positions:
                break
            
            candidate = self.strategy.candidates[code]
            
            # 获取5分钟数据
            if candidate.min5_data is None:
                continue
            
            # 获取当日及之前的5分钟数据
            date_str = date.strftime('%Y-%m-%d')
            available_data = candidate.min5_data[
                candidate.min5_data.index.date <= date.date()
            ]
            
            if len(available_data) < 50:  # 需要足够的数据进行分析
                continue
            
            # 使用缠论分析器分析
            analysis_result = self.chanlun_analyzer.analyze_5min_data(
                available_data.tail(200), code  # 使用最近200根K线
            )
            
            # 检查是否有交易信号
            signals = analysis_result.get('signals', [])
            if not signals:
                continue
            
            # 选择最强的信号
            best_signal = max(signals, key=lambda x: x.get('strength', 0))
            if best_signal['strength'] < 0.7:  # 信号强度阈值
                continue
            
            # 建仓
            self._enter_position(code, best_signal, date, candidate)
    
    def _enter_position(self, code: str, signal: Dict, date: datetime, candidate):
        """建立仓位"""
        enter_price = signal['price']
        
        # 计算止损止盈
        bi_low = signal.get('bi_low', enter_price * 0.95)
        stop_loss = bi_low * (1 - self.strategy.stop_loss_pct)
        loss_amount = enter_price - stop_loss
        take_profit = enter_price + loss_amount * self.strategy.profit_loss_ratio
        
        # 计算股数（整手）
        shares = int(self.strategy.position_size / enter_price / 100) * 100
        if shares <= 0:
            return
        
        # 创建持仓
        position = Position(
            code=code,
            enter_price=enter_price,
            enter_date=date,
            stop_loss=stop_loss,
            take_profit=take_profit,
            shares=shares,
            enter_reason=signal['reason']
        )
        
        self.strategy.positions[code] = position
        
        # 记录交易
        trade = TradeRecord(
            code=code,
            action="buy",
            price=enter_price,
            shares=shares,
            date=date,
            reason=signal['reason']
        )
        self.strategy.trade_records.append(trade)
        
        # 更新资金
        cost = enter_price * shares
        self.strategy.current_capital -= cost
        
        # 从候选池移除
        if code in self.strategy.candidates:
            del self.strategy.candidates[code]
        
        print(f"📈 建仓 {code}: 价格{enter_price:.2f}, 止损{stop_loss:.2f}, 止盈{take_profit:.2f}")
    
    def _check_exit_conditions(self, date: datetime):
        """检查出场条件"""
        for code in list(self.strategy.positions.keys()):
            position = self.strategy.positions[code]
            
            # 获取当前价格（使用5分钟数据的最新价格）
            current_price = self._get_current_price(code, date)
            if current_price is None:
                continue
            
            # 检查止损
            if current_price <= position.stop_loss:
                self._exit_position(code, current_price, date, "止损")
            
            # 检查止盈
            elif current_price >= position.take_profit:
                self._exit_position(code, current_price, date, "止盈")
    
    def _get_current_price(self, code: str, date: datetime) -> Optional[float]:
        """获取股票在指定日期的当前价格"""
        try:
            # 从数据适配器获取数据
            daily_data, min5_data = self.strategy.data_adapter.load_stock_data(code)
            
            if min5_data is not None:
                # 使用5分钟数据
                date_data = min5_data[min5_data.index.date <= date.date()]
                if len(date_data) > 0:
                    return date_data['close'].iloc[-1]
            
            if daily_data is not None:
                # 使用日线数据
                date_data = daily_data[daily_data.index.date <= date.date()]
                if len(date_data) > 0:
                    return date_data['close'].iloc[-1]
            
            return None
        except:
            return None
    
    def _exit_position(self, code: str, exit_price: float, date: datetime, reason: str):
        """平仓"""
        position = self.strategy.positions[code]
        
        # 计算盈亏
        pnl = (exit_price - position.enter_price) * position.shares
        
        # 记录交易
        trade = TradeRecord(
            code=code,
            action="sell",
            price=exit_price,
            shares=position.shares,
            date=date,
            reason=reason,
            pnl=pnl
        )
        self.strategy.trade_records.append(trade)
        
        # 更新资金
        proceeds = exit_price * position.shares
        self.strategy.current_capital += proceeds
        
        # 更新统计
        self.strategy.stats['trades_completed'] += 1
        
        # 移除持仓
        del self.strategy.positions[code]
        
        pnl_pct = pnl / (position.enter_price * position.shares) * 100
        print(f"📉 平仓 {code}: 价格{exit_price:.2f}, 盈亏{pnl:.2f}({pnl_pct:+.1f}%), {reason}")
    
    def _record_daily_status(self, date: datetime):
        """记录每日状态"""
        # 计算当前总资产
        total_asset = self.strategy.current_capital
        
        # 加上持仓市值
        for code, position in self.strategy.positions.items():
            current_price = self._get_current_price(code, date)
            if current_price:
                total_asset += current_price * position.shares
        
        # 记录
        self.daily_capital.append({
            'date': date,
            'capital': self.strategy.current_capital,
            'total_asset': total_asset,
            'positions_count': len(self.strategy.positions),
            'candidates_count': len(self.strategy.candidates)
        })
        
        # 计算日收益率
        if len(self.daily_capital) > 1:
            prev_asset = self.daily_capital[-2]['total_asset']
            daily_return = (total_asset - prev_asset) / prev_asset
            self.daily_returns.append({
                'date': date,
                'return': daily_return,
                'cumulative_return': (total_asset - self.strategy.initial_capital) / self.strategy.initial_capital
            })
    
    def _generate_backtest_report(self) -> Dict:
        """生成回测报告"""
        print("\n" + "="*60)
        print("📊 回测报告")
        print("="*60)
        
        # 基础统计
        total_trades = len([t for t in self.strategy.trade_records if t.action == 'buy'])
        completed_trades = len([t for t in self.strategy.trade_records if t.action == 'sell'])
        
        if completed_trades > 0:
            sell_trades = [t for t in self.strategy.trade_records if t.action == 'sell']
            total_pnl = sum(t.pnl for t in sell_trades)
            win_trades = len([t for t in sell_trades if t.pnl > 0])
            win_rate = win_trades / completed_trades
            avg_pnl = total_pnl / completed_trades
            max_win = max(t.pnl for t in sell_trades)
            max_loss = min(t.pnl for t in sell_trades)
        else:
            total_pnl = win_rate = avg_pnl = max_win = max_loss = 0
        
        # 计算最终收益
        final_asset = self.daily_capital[-1]['total_asset'] if self.daily_capital else self.strategy.initial_capital
        total_return = (final_asset - self.strategy.initial_capital) / self.strategy.initial_capital
        
        # 打印报告
        print(f"初始资金: {self.strategy.initial_capital:,.2f}")
        print(f"最终资产: {final_asset:,.2f}")
        print(f"总收益: {total_pnl:,.2f}")
        print(f"总收益率: {total_return:.2%}")
        print(f"总交易次数: {total_trades}")
        print(f"完成交易次数: {completed_trades}")
        print(f"胜率: {win_rate:.2%}")
        print(f"平均每笔盈亏: {avg_pnl:,.2f}")
        print(f"最大单笔盈利: {max_win:,.2f}")
        print(f"最大单笔亏损: {max_loss:,.2f}")
        
        # 计算最大回撤
        if self.daily_capital:
            max_drawdown = self._calculate_max_drawdown()
            print(f"最大回撤: {max_drawdown:.2%}")
        
        # 生成图表
        self._plot_results()
        
        return {
            'initial_capital': self.strategy.initial_capital,
            'final_asset': final_asset,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'total_trades': total_trades,
            'completed_trades': completed_trades,
            'win_rate': win_rate,
            'avg_pnl': avg_pnl,
            'max_win': max_win,
            'max_loss': max_loss,
            'daily_capital': self.daily_capital,
            'daily_returns': self.daily_returns,
            'trade_records': self.strategy.trade_records
        }
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if not self.daily_capital:
            return 0.0
        
        assets = [d['total_asset'] for d in self.daily_capital]
        peak = assets[0]
        max_dd = 0.0
        
        for asset in assets:
            if asset > peak:
                peak = asset
            drawdown = (peak - asset) / peak
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _plot_results(self):
        """绘制回测结果图表"""
        if not self.daily_capital:
            return
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('连续涨停反弹策略回测结果', fontsize=16)
        
        # 1. 资产曲线
        dates = [d['date'] for d in self.daily_capital]
        assets = [d['total_asset'] for d in self.daily_capital]
        
        ax1.plot(dates, assets, 'b-', linewidth=2, label='总资产')
        ax1.axhline(y=self.strategy.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
        ax1.set_title('资产曲线')
        ax1.set_ylabel('资产(元)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 收益率曲线
        if self.daily_returns:
            return_dates = [d['date'] for d in self.daily_returns]
            cum_returns = [d['cumulative_return'] for d in self.daily_returns]
            
            ax2.plot(return_dates, [r*100 for r in cum_returns], 'g-', linewidth=2)
            ax2.set_title('累计收益率')
            ax2.set_ylabel('收益率(%)')
            ax2.grid(True, alpha=0.3)
        
        # 3. 持仓数量
        positions_count = [d['positions_count'] for d in self.daily_capital]
        ax3.plot(dates, positions_count, 'orange', linewidth=2)
        ax3.set_title('持仓数量')
        ax3.set_ylabel('持仓数量')
        ax3.grid(True, alpha=0.3)
        
        # 4. 交易盈亏分布
        if self.strategy.trade_records:
            sell_trades = [t for t in self.strategy.trade_records if t.action == 'sell']
            if sell_trades:
                pnls = [t.pnl for t in sell_trades]
                ax4.hist(pnls, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                ax4.axvline(x=0, color='r', linestyle='--', alpha=0.7)
                ax4.set_title('交易盈亏分布')
                ax4.set_xlabel('盈亏(元)')
                ax4.set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 图表已保存为 backtest_results.png")

def main():
    """主函数 - 运行完整回测"""
    print("🚀 连续涨停反弹策略完整回测")
    
    # 初始化策略
    strategy = LimitUpReversalStrategy(
        data_path=r"D:\git\czsc-new\stock_data",
        initial_capital=1000000,
        max_positions=10,
        monitor_days=15,
        stop_loss_pct=0.01,
        profit_loss_ratio=1.5
    )
    
    # 初始化回测引擎
    backtest = BacktestEngine(strategy)
    
    # 运行回测
    results = backtest.run_backtest(
        start_date="2023-01-01",
        end_date="2023-12-31",
        scan_frequency=3  # 每3天扫描一次市场
    )
    
    print("\n✅ 回测完成！")

if __name__ == "__main__":
    main()
