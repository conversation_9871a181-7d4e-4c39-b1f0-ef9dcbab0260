#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索脚本
用于分析 D:\git\czsc-new\stock_data 目录下的数据格式
"""

import os
import pandas as pd
import pickle
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class DataExplorer:
    """数据探索器"""
    
    def __init__(self, data_path: str):
        self.data_path = Path(data_path)
        self.sample_files = []
        self.data_summary = {}
        
    def explore_directory(self):
        """探索数据目录结构"""
        print("=" * 60)
        print("📁 数据目录探索")
        print("=" * 60)
        
        if not self.data_path.exists():
            print(f"❌ 数据目录不存在: {self.data_path}")
            return False
        
        # 统计文件类型和数量
        file_types = {}
        total_files = 0
        
        for file_path in self.data_path.rglob("*"):
            if file_path.is_file():
                suffix = file_path.suffix.lower()
                file_types[suffix] = file_types.get(suffix, 0) + 1
                total_files += 1
                
                # 收集样本文件（每种类型最多5个）
                if len([f for f in self.sample_files if f.suffix == suffix]) < 5:
                    self.sample_files.append(file_path)
        
        print(f"📊 总文件数: {total_files}")
        print("📋 文件类型分布:")
        for suffix, count in sorted(file_types.items()):
            print(f"  {suffix or '无扩展名'}: {count} 个文件")
        
        print(f"\n🔍 将分析以下样本文件:")
        for i, file_path in enumerate(self.sample_files[:10], 1):
            print(f"  {i}. {file_path.name}")
        
        return True
    
    def analyze_sample_files(self):
        """分析样本文件"""
        print("\n" + "=" * 60)
        print("📊 样本文件分析")
        print("=" * 60)
        
        for file_path in self.sample_files[:5]:  # 只分析前5个文件
            print(f"\n🔍 分析文件: {file_path.name}")
            print("-" * 40)
            
            try:
                self._analyze_single_file(file_path)
            except Exception as e:
                print(f"❌ 分析失败: {e}")
                continue
    
    def _analyze_single_file(self, file_path: Path):
        """分析单个文件"""
        suffix = file_path.suffix.lower()
        
        try:
            if suffix == '.pkl':
                self._analyze_pickle_file(file_path)
            elif suffix == '.csv':
                self._analyze_csv_file(file_path)
            elif suffix in ['.h5', '.hdf5']:
                self._analyze_hdf5_file(file_path)
            elif suffix == '.parquet':
                self._analyze_parquet_file(file_path)
            else:
                print(f"⚠️ 未知文件类型: {suffix}")
                
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    
    def _analyze_pickle_file(self, file_path: Path):
        """分析pickle文件"""
        print("📦 Pickle文件分析:")
        
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"  数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"  字典键: {list(data.keys())}")
            for key, value in data.items():
                print(f"    {key}: {type(value)}")
                if isinstance(value, pd.DataFrame):
                    self._analyze_dataframe(value, f"    {key}")
                elif isinstance(value, (list, np.ndarray)):
                    print(f"      长度: {len(value)}")
                    if len(value) > 0:
                        print(f"      首个元素类型: {type(value[0])}")
        
        elif isinstance(data, pd.DataFrame):
            self._analyze_dataframe(data, "  ")
        
        elif isinstance(data, list):
            print(f"  列表长度: {len(data)}")
            if len(data) > 0:
                print(f"  首个元素类型: {type(data[0])}")
                if isinstance(data[0], dict):
                    print(f"  首个元素键: {list(data[0].keys())}")
    
    def _analyze_csv_file(self, file_path: Path):
        """分析CSV文件"""
        print("📄 CSV文件分析:")
        
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, nrows=1000)
                print(f"  编码: {encoding}")
                break
            except:
                continue
        
        if df is not None:
            self._analyze_dataframe(df, "  ")
        else:
            print("  ❌ 无法读取CSV文件")
    
    def _analyze_hdf5_file(self, file_path: Path):
        """分析HDF5文件"""
        print("🗃️ HDF5文件分析:")
        
        try:
            # 列出所有键
            with pd.HDFStore(file_path, mode='r') as store:
                keys = store.keys()
                print(f"  键列表: {keys}")
                
                for key in keys[:3]:  # 只分析前3个键
                    df = store[key]
                    print(f"  键 {key}:")
                    self._analyze_dataframe(df, "    ")
        except Exception as e:
            print(f"  ❌ HDF5分析失败: {e}")
    
    def _analyze_parquet_file(self, file_path: Path):
        """分析Parquet文件"""
        print("🗂️ Parquet文件分析:")
        
        df = pd.read_parquet(file_path)
        self._analyze_dataframe(df, "  ")
    
    def _analyze_dataframe(self, df: pd.DataFrame, prefix: str = ""):
        """分析DataFrame"""
        print(f"{prefix}DataFrame信息:")
        print(f"{prefix}  形状: {df.shape}")
        print(f"{prefix}  列名: {list(df.columns)}")
        
        # 分析索引
        print(f"{prefix}  索引类型: {type(df.index)}")
        if hasattr(df.index, 'dtype'):
            print(f"{prefix}  索引数据类型: {df.index.dtype}")
        
        # 显示前几行
        print(f"{prefix}  前3行数据:")
        for i, (idx, row) in enumerate(df.head(3).iterrows()):
            print(f"{prefix}    {i+1}. 索引={idx}")
            for col in df.columns:
                value = row[col]
                print(f"{prefix}       {col}={value} ({type(value).__name__})")
        
        # 检查是否包含时间列
        time_columns = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['time', 'date', 'datetime', '时间', '日期']):
                time_columns.append(col)
        
        if time_columns:
            print(f"{prefix}  疑似时间列: {time_columns}")
        
        # 检查是否包含OHLCV数据
        ohlcv_mapping = {
            'open': ['open', 'o', '开盘', '开盘价'],
            'high': ['high', 'h', '最高', '最高价'],
            'low': ['low', 'l', '最低', '最低价'],
            'close': ['close', 'c', '收盘', '收盘价'],
            'volume': ['volume', 'vol', 'v', '成交量', '量']
        }
        
        found_ohlcv = {}
        for ohlcv_type, keywords in ohlcv_mapping.items():
            for col in df.columns:
                if any(keyword in col.lower() for keyword in keywords):
                    found_ohlcv[ohlcv_type] = col
                    break
        
        if found_ohlcv:
            print(f"{prefix}  OHLCV映射: {found_ohlcv}")
        
        # 数据范围分析
        if len(df) > 0:
            print(f"{prefix}  数据时间范围:")
            if hasattr(df.index, 'min') and hasattr(df.index, 'max'):
                try:
                    print(f"{prefix}    索引范围: {df.index.min()} 到 {df.index.max()}")
                except:
                    print(f"{prefix}    索引范围: 无法确定")
    
    def generate_data_format_guide(self):
        """生成数据格式指南"""
        print("\n" + "=" * 60)
        print("📋 数据格式适配指南")
        print("=" * 60)
        
        print("""
基于分析结果，我将创建数据适配器来处理你的数据格式。

请确认以下信息：
1. 股票代码格式 (如: 000001, SZ000001, 000001.SZ)
2. 时间字段名称和格式
3. OHLCV字段的确切名称
4. 是否需要复权处理
5. 5分钟数据和日线数据是否在同一文件中

我将根据分析结果自动适配，如有问题会进一步调整。
        """)
    
    def run_exploration(self):
        """运行完整的数据探索"""
        print("🚀 开始数据探索...")
        
        if not self.explore_directory():
            return False
        
        self.analyze_sample_files()
        self.generate_data_format_guide()
        
        print("\n✅ 数据探索完成！")
        return True

def main():
    """主函数"""
    data_path = r"D:\git\czsc-new\stock_data"
    
    print("🔍 股票数据探索器")
    print(f"📁 目标目录: {data_path}")
    
    explorer = DataExplorer(data_path)
    explorer.run_exploration()

if __name__ == "__main__":
    main()
