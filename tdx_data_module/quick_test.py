#!/usr/bin/env python3
"""
通达信数据模块快速测试
验证核心功能是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX


def quick_test():
    """快速测试核心功能"""
    print("通达信数据模块快速测试")
    print("=" * 40)
    
    try:
        # 创建A股交易所实例
        print("1. 创建A股交易所实例...")
        tdx = ExchangeTDX()
        print("   ✓ 创建成功")
        
        # 测试获取股票列表
        print("\n2. 获取股票列表...")
        stocks = tdx.all_stocks()
        if stocks and len(stocks) > 0:
            print(f"   ✓ 成功获取 {len(stocks)} 只股票")
            print(f"   示例: {stocks[0]['code']} - {stocks[0]['name']}")
        else:
            print("   ✗ 获取股票列表失败")
            return False
        
        # 测试获取K线数据
        print("\n3. 获取K线数据...")
        test_code = "SZ.000001"  # 平安银行
        klines = tdx.klines(test_code, "d")
        if klines is not None and len(klines) > 0:
            print(f"   ✓ 成功获取 {test_code} 日线数据 {len(klines)} 条")
            latest = klines.iloc[-1]
            print(f"   最新数据: {latest['date']} 收盘价: {latest['close']}")
        else:
            print(f"   ✗ 获取 {test_code} K线数据失败")
            return False
        
        # 测试获取实时行情
        print("\n4. 获取实时行情...")
        test_codes = ["SZ.000001", "SH.600000"]
        ticks = tdx.ticks(test_codes)
        if ticks and len(ticks) > 0:
            print(f"   ✓ 成功获取 {len(ticks)} 只股票实时行情")
            for code, tick in ticks.items():
                print(f"   {code}: 最新价 {tick.last}, 涨跌幅 {tick.rate}%")
        else:
            print("   ✗ 获取实时行情失败")
            return False
        
        # 测试缓存功能
        print("\n5. 测试缓存功能...")
        from utils.file_cache import get_file_cache_db
        cache_db = get_file_cache_db()
        cache_info = cache_db.get_cache_info()
        print(f"   ✓ 缓存系统正常，缓存路径: {cache_info['cache_path']}")
        
        print("\n" + "=" * 40)
        print("✓ 所有核心功能测试通过！")
        print("模块可以正常使用。")
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n使用建议:")
        print("- 运行 python main.py 查看完整功能演示")
        print("- 查看 examples/ 目录下的使用示例")
        print("- 阅读 功能说明.md 了解详细功能")
    else:
        print("\n请检查:")
        print("- 网络连接是否正常")
        print("- 是否安装了所有依赖包")
        print("- 防火墙是否阻止了网络访问")
    
    sys.exit(0 if success else 1)
