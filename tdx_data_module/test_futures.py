#!/usr/bin/env python3
"""
期货数据专项测试程序
全面测试期货数据获取功能
"""

import sys
import os
import traceback
import time
import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx_futures import ExchangeTDXFutures


def test_futures_connection():
    """测试期货连接"""
    print("1. 测试期货服务器连接...")
    try:
        futures_exchange = ExchangeTDXFutures()
        print(f"   ✓ 期货交易所创建成功")
        print(f"   服务器信息: {futures_exchange.connect_info}")
        print(f"   市场映射: {len(futures_exchange.market_maps)} 个市场")
        return futures_exchange
    except Exception as e:
        print(f"   ✗ 期货交易所创建失败: {e}")
        traceback.print_exc()
        return None


def test_futures_contracts(futures_exchange):
    """测试期货合约列表获取"""
    print("\n2. 测试期货合约列表获取...")
    try:
        contracts = futures_exchange.all_stocks()
        if contracts and len(contracts) > 0:
            print(f"   ✓ 成功获取 {len(contracts)} 个期货合约")
            
            # 按类型分类统计
            contract_types = {}
            for contract in contracts:
                code = contract['code']
                if '.' in code:
                    prefix = code.split('.')[1][:2]  # 取前两个字符作为品种
                    contract_types[prefix] = contract_types.get(prefix, 0) + 1
            
            print(f"   合约品种统计: {len(contract_types)} 个品种")
            
            # 显示前10个合约
            print("   前10个合约:")
            for i, contract in enumerate(contracts[:10]):
                print(f"     {i+1}. {contract['code']} - {contract['name']}")
            
            return contracts
        else:
            print("   ✗ 获取期货合约列表失败")
            return None
    except Exception as e:
        print(f"   ✗ 获取期货合约异常: {e}")
        traceback.print_exc()
        return None


def test_futures_klines(futures_exchange, contracts):
    """测试期货K线数据获取"""
    print("\n3. 测试期货K线数据获取...")
    
    # 选择几个测试合约
    test_contracts = []
    if contracts:
        # 直接使用获取到的前几个合约
        test_contracts = contracts[:5]

    # 如果没找到，使用默认的
    if not test_contracts:
        test_contracts = [
            {"code": "FUTURES.IF2412", "name": "沪深300股指期货"},
            {"code": "FUTURES.IC2412", "name": "中证500股指期货"},
            {"code": "FUTURES.CU2501", "name": "沪铜期货"},
        ]
    
    success_count = 0
    total_count = len(test_contracts)
    
    for i, contract in enumerate(test_contracts, 1):
        code = contract['code']
        name = contract['name']
        print(f"   [{i}/{total_count}] 测试 {code} ({name})...")
        
        try:
            # 测试日线数据
            klines = futures_exchange.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"     ✓ 日线数据: {len(klines)} 条")
                latest = klines.iloc[-1]
                print(f"     最新数据: {latest['date']} 收盘价: {latest['close']}")
                
                # 检查数据完整性
                required_columns = ['code', 'date', 'open', 'close', 'high', 'low', 'volume']
                missing_columns = [col for col in required_columns if col not in klines.columns]
                if missing_columns:
                    print(f"     ⚠️ 缺少列: {missing_columns}")
                else:
                    print(f"     ✓ 数据完整")
                
                success_count += 1
            else:
                print(f"     ✗ 获取日线数据失败")
                
        except Exception as e:
            print(f"     ✗ 获取数据异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n   K线数据测试结果: {success_count}/{total_count} 成功")
    return success_count > 0


def test_futures_realtime(futures_exchange, contracts):
    """测试期货实时行情"""
    print("\n4. 测试期货实时行情...")
    
    # 选择几个测试合约
    test_codes = []
    if contracts:
        for contract in contracts[:5]:
            test_codes.append(contract['code'])
    else:
        test_codes = ["FUTURES.IF2412", "FUTURES.CU2501", "FUTURES.RB2505"]
    
    try:
        ticks = futures_exchange.ticks(test_codes)
        if ticks and len(ticks) > 0:
            print(f"   ✓ 成功获取 {len(ticks)} 个合约实时行情")
            print(f"   {'代码':<15} {'最新价':<10} {'涨跌幅':<8} {'成交量':<12}")
            print("   " + "-" * 50)
            
            for code, tick in ticks.items():
                print(f"   {code:<15} {tick.last:<10.2f} {tick.rate:<8.2f}% {tick.volume:<12.0f}")
            
            return True
        else:
            print("   ✗ 获取实时行情失败")
            return False
    except Exception as e:
        print(f"   ✗ 获取实时行情异常: {e}")
        traceback.print_exc()
        return False


def test_futures_frequencies(futures_exchange):
    """测试期货不同周期数据"""
    print("\n5. 测试期货不同周期数据...")

    # 使用实际获取到的期货合约
    test_code = "CZ.IC500"  # 使用中证500主力合约
    frequencies = ["1m", "5m", "15m", "30m", "60m", "d"]

    success_count = 0

    for freq in frequencies:
        try:
            print(f"   测试 {test_code} {freq} 周期...")
            klines = futures_exchange.klines(test_code, freq)
            if klines is not None and len(klines) > 0:
                print(f"     ✓ {freq}: {len(klines)} 条数据")
                success_count += 1
            else:
                print(f"     ✗ {freq}: 获取失败")
        except Exception as e:
            print(f"     ✗ {freq}: 异常 {e}")

    print(f"\n   周期测试结果: {success_count}/{len(frequencies)} 成功")
    return success_count > 0


def test_futures_market_mapping(futures_exchange):
    """测试期货市场映射"""
    print("\n6. 测试期货市场映射...")
    
    test_codes = [
        "FUTURES.IF2412",  # 中金所
        "FUTURES.CU2501",  # 上期所
        "FUTURES.C2505",   # 大商所
        "FUTURES.CF505",   # 郑商所
    ]
    
    success_count = 0
    
    for code in test_codes:
        try:
            market, tdx_code = futures_exchange.to_tdx_code(code)
            if market is not None:
                print(f"   ✓ {code} -> 市场: {market}, 代码: {tdx_code}")
                success_count += 1
            else:
                print(f"   ✗ {code} -> 映射失败")
        except Exception as e:
            print(f"   ✗ {code} -> 异常: {e}")
    
    print(f"\n   市场映射测试结果: {success_count}/{len(test_codes)} 成功")
    return success_count > 0


def main():
    """主测试函数"""
    print("期货数据专项测试程序")
    print("=" * 50)
    print(f"测试开始时间: {datetime.datetime.now()}")
    print()
    
    # 测试步骤
    test_results = []
    
    # 1. 测试连接
    futures_exchange = test_futures_connection()
    test_results.append(("期货连接", futures_exchange is not None))
    
    if futures_exchange is None:
        print("\n期货连接失败，无法继续测试")
        return False
    
    # 2. 测试合约列表
    contracts = test_futures_contracts(futures_exchange)
    test_results.append(("合约列表", contracts is not None and len(contracts) > 0))
    
    # 3. 测试K线数据
    klines_success = test_futures_klines(futures_exchange, contracts)
    test_results.append(("K线数据", klines_success))
    
    # 4. 测试实时行情
    realtime_success = test_futures_realtime(futures_exchange, contracts)
    test_results.append(("实时行情", realtime_success))
    
    # 5. 测试不同周期
    freq_success = test_futures_frequencies(futures_exchange)
    test_results.append(("多周期数据", freq_success))
    
    # 6. 测试市场映射
    mapping_success = test_futures_market_mapping(futures_exchange)
    test_results.append(("市场映射", mapping_success))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("期货数据测试结果汇总")
    print("=" * 50)
    
    success_count = 0
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<12} {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print(f"测试结束时间: {datetime.datetime.now()}")
    
    return success_count == total_count


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
