#!/usr/bin/env python3
"""
通达信数据模块主程序
演示如何使用各种交易所接口获取数据
"""

import sys
import os
import traceback
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX
from core.exchange_tdx_hk import ExchangeTDXHK
from core.exchange_tdx_us import ExchangeTDXUS
from core.exchange_tdx_futures import ExchangeTDXFutures
from core.exchange_tdx_fx import ExchangeTDXFX
from utils.file_cache import get_file_cache_db
import config


def test_a_stock_data():
    """测试A股数据获取"""
    print("=" * 50)
    print("测试A股数据获取")
    print("=" * 50)
    
    try:
        # 创建A股交易所实例
        tdx = ExchangeTDX()
        
        # 测试获取股票列表
        print("1. 获取A股股票列表...")
        stocks = tdx.all_stocks()
        if stocks:
            print(f"   获取到 {len(stocks)} 只股票")
            # 显示前5只股票
            for i, stock in enumerate(stocks[:5]):
                print(f"   {i+1}. {stock['code']} - {stock['name']} ({stock.get('type', 'unknown')})")
        else:
            print("   获取股票列表失败")
            return False
        
        # 测试获取K线数据
        print("\n2. 获取K线数据...")
        test_codes = ["SZ.000001", "SH.600000", "SZ.300015"]
        
        for code in test_codes:
            print(f"   获取 {code} 日线数据...")
            klines = tdx.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"   成功获取 {len(klines)} 条K线数据")
                print(f"   最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
            else:
                print(f"   获取 {code} K线数据失败")
        
        # 测试获取实时行情
        print("\n3. 获取实时行情...")
        ticks = tdx.ticks(test_codes)
        for code, tick in ticks.items():
            print(f"   {code}: 最新价 {tick.last}, 涨跌幅 {tick.rate}%")
        
        return True
        
    except Exception as e:
        print(f"A股数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_hk_stock_data():
    """测试港股数据获取"""
    print("=" * 50)
    print("测试港股数据获取")
    print("=" * 50)
    
    try:
        # 创建港股交易所实例
        hk_exchange = ExchangeTDXHK()
        
        # 测试获取股票列表
        print("1. 获取港股股票列表...")
        stocks = hk_exchange.all_stocks()
        if stocks:
            print(f"   获取到 {len(stocks)} 只港股")
            # 显示前5只股票
            for i, stock in enumerate(stocks[:5]):
                print(f"   {i+1}. {stock['code']} - {stock['name']}")
        else:
            print("   获取港股列表失败")
            return False
        
        # 测试获取K线数据
        print("\n2. 获取港股K线数据...")
        test_codes = ["HK.00700", "HK.09988", "HK.03690"]
        
        for code in test_codes:
            print(f"   获取 {code} 日线数据...")
            klines = hk_exchange.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"   成功获取 {len(klines)} 条K线数据")
                print(f"   最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
            else:
                print(f"   获取 {code} K线数据失败")
        
        return True
        
    except Exception as e:
        print(f"港股数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_us_stock_data():
    """测试美股数据获取"""
    print("=" * 50)
    print("测试美股数据获取")
    print("=" * 50)
    
    try:
        # 创建美股交易所实例
        us_exchange = ExchangeTDXUS()
        
        # 测试获取股票列表
        print("1. 获取美股股票列表...")
        stocks = us_exchange.all_stocks()
        if stocks:
            print(f"   获取到 {len(stocks)} 只美股")
            for i, stock in enumerate(stocks):
                print(f"   {i+1}. {stock['code']} - {stock['name']}")
        else:
            print("   获取美股列表失败")
            return False
        
        # 测试获取K线数据
        print("\n2. 获取美股K线数据...")
        test_codes = ["US.AAPL", "US.MSFT", "US.GOOGL"]
        
        for code in test_codes:
            print(f"   获取 {code} 日线数据...")
            klines = us_exchange.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"   成功获取 {len(klines)} 条K线数据")
                print(f"   最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
            else:
                print(f"   获取 {code} K线数据失败")
        
        return True
        
    except Exception as e:
        print(f"美股数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_futures_data():
    """测试期货数据获取"""
    print("=" * 50)
    print("测试期货数据获取")
    print("=" * 50)
    
    try:
        # 创建期货交易所实例
        futures_exchange = ExchangeTDXFutures()
        
        # 测试获取合约列表
        print("1. 获取期货合约列表...")
        contracts = futures_exchange.all_stocks()
        if contracts:
            print(f"   获取到 {len(contracts)} 个期货合约")
            for i, contract in enumerate(contracts):
                print(f"   {i+1}. {contract['code']} - {contract['name']}")
        else:
            print("   获取期货合约列表失败")
            return False
        
        # 测试获取K线数据
        print("\n2. 获取期货K线数据...")
        test_codes = ["FUTURES.IF2412", "FUTURES.CU2412"]
        
        for code in test_codes:
            print(f"   获取 {code} 日线数据...")
            klines = futures_exchange.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"   成功获取 {len(klines)} 条K线数据")
                print(f"   最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
            else:
                print(f"   获取 {code} K线数据失败")
        
        return True
        
    except Exception as e:
        print(f"期货数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_fx_data():
    """测试外汇数据获取"""
    print("=" * 50)
    print("测试外汇数据获取")
    print("=" * 50)
    
    try:
        # 创建外汇交易所实例
        fx_exchange = ExchangeTDXFX()
        
        # 测试获取外汇对列表
        print("1. 获取外汇对列表...")
        pairs = fx_exchange.all_stocks()
        if pairs:
            print(f"   获取到 {len(pairs)} 个外汇对")
            for i, pair in enumerate(pairs):
                print(f"   {i+1}. {pair['code']} - {pair['name']}")
        else:
            print("   获取外汇对列表失败")
            return False
        
        # 测试获取K线数据
        print("\n2. 获取外汇K线数据...")
        test_codes = ["FX.EURUSD", "FX.GBPUSD"]
        
        for code in test_codes:
            print(f"   获取 {code} 日线数据...")
            klines = fx_exchange.klines(code, "d")
            if klines is not None and len(klines) > 0:
                print(f"   成功获取 {len(klines)} 条K线数据")
                print(f"   最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
            else:
                print(f"   获取 {code} K线数据失败")
        
        return True
        
    except Exception as e:
        print(f"外汇数据测试失败: {e}")
        traceback.print_exc()
        return False


def test_cache_system():
    """测试缓存系统"""
    print("=" * 50)
    print("测试缓存系统")
    print("=" * 50)
    
    try:
        cache_db = get_file_cache_db()
        
        # 获取缓存信息
        cache_info = cache_db.get_cache_info()
        print("缓存信息:")
        print(f"  缓存路径: {cache_info['cache_path']}")
        
        for market, info in cache_info['markets'].items():
            print(f"  {market} 市场: {info['file_count']} 个文件, {info['total_size_mb']} MB")
        
        return True
        
    except Exception as e:
        print(f"缓存系统测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("通达信数据模块测试程序")
    print(f"开始时间: {datetime.now()}")
    print(f"数据路径: {config.get_data_path()}")
    
    # 测试结果统计
    test_results = {}
    
    # 依次测试各个模块
    test_functions = [
        ("A股数据", test_a_stock_data),
        ("港股数据", test_hk_stock_data),
        ("美股数据", test_us_stock_data),
        ("期货数据", test_futures_data),
        ("外汇数据", test_fx_data),
        ("缓存系统", test_cache_system),
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            test_results[test_name] = "成功" if result else "失败"
        except Exception as e:
            print(f"测试 {test_name} 时发生异常: {e}")
            test_results[test_name] = "异常"
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    for test_name, result in test_results.items():
        status_symbol = "✓" if result == "成功" else "✗"
        print(f"{status_symbol} {test_name}: {result}")
    
    success_count = sum(1 for result in test_results.values() if result == "成功")
    total_count = len(test_results)
    
    print(f"\n总计: {success_count}/{total_count} 项测试成功")
    print(f"结束时间: {datetime.now()}")


if __name__ == "__main__":
    main()
