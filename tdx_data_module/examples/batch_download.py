#!/usr/bin/env python3
"""
批量下载数据示例
演示如何批量下载股票数据并保存到本地
"""

import sys
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exchange_tdx import ExchangeTDX
from utils.file_cache import get_file_cache_db


def download_single_stock(tdx, code, frequency="d"):
    """下载单只股票数据"""
    try:
        print(f"下载 {code} {frequency} 数据...")
        klines = tdx.klines(code, frequency)
        
        if klines is not None and len(klines) > 0:
            return {
                "code": code,
                "frequency": frequency,
                "count": len(klines),
                "start_date": klines.iloc[0]['date'],
                "end_date": klines.iloc[-1]['date'],
                "status": "成功"
            }
        else:
            return {
                "code": code,
                "frequency": frequency,
                "count": 0,
                "status": "失败"
            }
    except Exception as e:
        return {
            "code": code,
            "frequency": frequency,
            "count": 0,
            "status": f"异常: {str(e)}"
        }


def batch_download_stocks():
    """批量下载股票数据"""
    print("批量下载股票数据示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 要下载的股票列表（示例）
    stock_list = [
        "SZ.000001",  # 平安银行
        "SZ.000002",  # 万科A
        "SH.600000",  # 浦发银行
        "SH.600036",  # 招商银行
        "SZ.300015",  # 爱尔眼科
        "SH.600519",  # 贵州茅台
        "SZ.000858",  # 五粮液
        "SH.600276",  # 恒瑞医药
        "SZ.002415",  # 海康威视
        "SH.601318",  # 中国平安
    ]
    
    # 要下载的周期
    frequencies = ["d", "60m", "30m"]
    
    print(f"准备下载 {len(stock_list)} 只股票的 {len(frequencies)} 种周期数据")
    print(f"股票列表: {', '.join(stock_list)}")
    print(f"周期列表: {', '.join(frequencies)}")
    print()
    
    # 记录开始时间
    start_time = time.time()
    
    # 下载结果统计
    results = []
    
    # 逐个下载
    for i, code in enumerate(stock_list, 1):
        print(f"[{i}/{len(stock_list)}] 处理 {code}...")
        
        for freq in frequencies:
            result = download_single_stock(tdx, code, freq)
            results.append(result)
            
            if result["status"] == "成功":
                print(f"  {freq}: 成功下载 {result['count']} 条数据 "
                      f"({result['start_date']} 到 {result['end_date']})")
            else:
                print(f"  {freq}: {result['status']}")
        
        # 避免请求过于频繁
        time.sleep(0.5)
    
    # 统计结果
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    success_count = sum(1 for r in results if r["status"] == "成功")
    total_count = len(results)
    total_records = sum(r["count"] for r in results if r["status"] == "成功")
    
    print(f"\n下载完成!")
    print(f"耗时: {elapsed_time:.2f} 秒")
    print(f"成功: {success_count}/{total_count}")
    print(f"总记录数: {total_records}")
    
    # 显示失败的任务
    failed_results = [r for r in results if r["status"] != "成功"]
    if failed_results:
        print(f"\n失败任务 ({len(failed_results)} 个):")
        for r in failed_results:
            print(f"  {r['code']} {r['frequency']}: {r['status']}")


def parallel_download_stocks():
    """并行下载股票数据"""
    print("并行下载股票数据示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 要下载的股票列表
    stock_list = [
        "SZ.000001", "SZ.000002", "SH.600000", "SH.600036", "SZ.300015",
        "SH.600519", "SZ.000858", "SH.600276", "SZ.002415", "SH.601318",
        "SZ.002594", "SH.600887", "SZ.300059", "SH.601166", "SZ.000063",
    ]
    
    frequency = "d"  # 只下载日线数据
    
    print(f"准备并行下载 {len(stock_list)} 只股票的日线数据")
    print()
    
    # 记录开始时间
    start_time = time.time()
    
    # 使用线程池并行下载
    max_workers = 5  # 最大并发数
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有下载任务
        future_to_code = {
            executor.submit(download_single_stock, tdx, code, frequency): code
            for code in stock_list
        }
        
        # 获取结果
        for future in as_completed(future_to_code):
            code = future_to_code[future]
            try:
                result = future.result()
                results.append(result)
                
                if result["status"] == "成功":
                    print(f"✓ {code}: 成功下载 {result['count']} 条数据")
                else:
                    print(f"✗ {code}: {result['status']}")
                    
            except Exception as e:
                print(f"✗ {code}: 异常 {str(e)}")
                results.append({
                    "code": code,
                    "frequency": frequency,
                    "count": 0,
                    "status": f"异常: {str(e)}"
                })
    
    # 统计结果
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    success_count = sum(1 for r in results if r["status"] == "成功")
    total_count = len(results)
    total_records = sum(r["count"] for r in results if r["status"] == "成功")
    
    print(f"\n并行下载完成!")
    print(f"耗时: {elapsed_time:.2f} 秒")
    print(f"成功: {success_count}/{total_count}")
    print(f"总记录数: {total_records}")
    print(f"平均速度: {total_records/elapsed_time:.1f} 条/秒")


def download_by_stock_list():
    """根据股票列表文件下载"""
    print("根据股票列表文件下载示例")
    print("-" * 30)
    
    # 创建示例股票列表文件
    stock_list_file = "stock_list.txt"
    
    # 如果文件不存在，创建一个示例文件
    if not os.path.exists(stock_list_file):
        sample_stocks = [
            "SZ.000001  # 平安银行",
            "SZ.000002  # 万科A", 
            "SH.600000  # 浦发银行",
            "SH.600036  # 招商银行",
            "SZ.300015  # 爱尔眼科",
        ]
        
        with open(stock_list_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sample_stocks))
        
        print(f"已创建示例股票列表文件: {stock_list_file}")
    
    # 读取股票列表
    stock_codes = []
    try:
        with open(stock_list_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 提取股票代码（忽略注释）
                    code = line.split('#')[0].strip()
                    if code:
                        stock_codes.append(code)
        
        print(f"从文件读取到 {len(stock_codes)} 只股票")
        
    except Exception as e:
        print(f"读取股票列表文件失败: {e}")
        return
    
    if not stock_codes:
        print("股票列表为空")
        return
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 下载数据
    frequency = "d"
    results = []
    
    for i, code in enumerate(stock_codes, 1):
        print(f"[{i}/{len(stock_codes)}] 下载 {code}...")
        result = download_single_stock(tdx, code, frequency)
        results.append(result)
        
        if result["status"] == "成功":
            print(f"  成功: {result['count']} 条数据")
        else:
            print(f"  失败: {result['status']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r["status"] == "成功")
    total_records = sum(r["count"] for r in results if r["status"] == "成功")
    
    print(f"\n下载完成: {success_count}/{len(stock_codes)} 成功")
    print(f"总记录数: {total_records}")


def export_downloaded_data():
    """导出已下载的数据"""
    print("导出已下载数据示例")
    print("-" * 30)
    
    # 获取缓存信息
    cache_db = get_file_cache_db()
    cache_info = cache_db.get_cache_info()
    
    print("缓存数据统计:")
    for market, info in cache_info['markets'].items():
        print(f"  {market} 市场: {info['file_count']} 个文件, {info['total_size_mb']} MB")
    
    # 创建导出目录
    export_dir = "exported_data"
    os.makedirs(export_dir, exist_ok=True)
    
    # 示例：导出A股日线数据
    print(f"\n导出A股日线数据到 {export_dir} 目录...")
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 获取一些股票的数据并导出
    test_codes = ["SZ.000001", "SH.600000", "SZ.000002"]
    
    for code in test_codes:
        try:
            # 获取数据
            klines = tdx.klines(code, "d")
            
            if klines is not None and len(klines) > 0:
                # 导出到CSV
                filename = os.path.join(export_dir, f"{code.replace('.', '_')}_daily.csv")
                klines.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"  {code}: 导出 {len(klines)} 条记录到 {filename}")
            else:
                print(f"  {code}: 无数据可导出")
                
        except Exception as e:
            print(f"  {code}: 导出失败 - {e}")
    
    print(f"\n数据已导出到 {export_dir} 目录")


def main():
    """主函数"""
    print("通达信批量下载数据示例")
    print("=" * 50)
    
    # 提供选择菜单
    examples = [
        ("1", "批量下载股票数据", batch_download_stocks),
        ("2", "并行下载股票数据", parallel_download_stocks),
        ("3", "根据股票列表文件下载", download_by_stock_list),
        ("4", "导出已下载数据", export_downloaded_data),
    ]
    
    print("请选择要运行的示例:")
    for code, name, _ in examples:
        print(f"  {code}. {name}")
    
    choice = input("\n请输入选择 (1-4, 或按回车运行所有): ").strip()
    
    if choice:
        for code, name, func in examples:
            if choice == code:
                print(f"\n运行示例: {name}")
                print("=" * 30)
                try:
                    func()
                except Exception as e:
                    print(f"示例运行失败: {e}")
                return
        
        print("无效选择")
    else:
        print("\n运行所有示例...")
        
        # 运行所有示例
        for _, name, func in examples:
            print(f"\n运行示例: {name}")
            print("=" * 30)
            try:
                func()
            except Exception as e:
                print(f"示例 {name} 运行失败: {e}")


if __name__ == "__main__":
    main()
