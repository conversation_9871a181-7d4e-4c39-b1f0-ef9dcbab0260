#!/usr/bin/env python3
"""
获取股票数据示例
演示如何使用通达信数据模块获取各种股票数据
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exchange_tdx import ExchangeTDX
import pandas as pd


def get_single_stock_data():
    """获取单只股票数据"""
    print("获取单只股票数据示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 股票代码
    code = "SZ.000001"  # 平安银行
    
    # 获取不同周期的K线数据
    frequencies = ["d", "60m", "30m", "15m", "5m"]
    
    for freq in frequencies:
        print(f"获取 {code} {freq} 周期数据...")
        klines = tdx.klines(code, freq)
        
        if klines is not None and len(klines) > 0:
            print(f"  成功获取 {len(klines)} 条数据")
            print(f"  时间范围: {klines.iloc[0]['date']} 到 {klines.iloc[-1]['date']}")
            print(f"  最新价格: 开盘 {klines.iloc[-1]['open']}, "
                  f"最高 {klines.iloc[-1]['high']}, "
                  f"最低 {klines.iloc[-1]['low']}, "
                  f"收盘 {klines.iloc[-1]['close']}")
            print(f"  成交量: {klines.iloc[-1]['volume']}")
        else:
            print(f"  获取 {freq} 数据失败")
        print()


def get_multiple_stocks_data():
    """获取多只股票数据"""
    print("获取多只股票数据示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 股票代码列表
    codes = [
        "SZ.000001",  # 平安银行
        "SH.600000",  # 浦发银行
        "SZ.000002",  # 万科A
        "SH.600036",  # 招商银行
        "SZ.300015",  # 爱尔眼科
    ]
    
    # 获取日线数据
    all_data = {}
    
    for code in codes:
        print(f"获取 {code} 日线数据...")
        klines = tdx.klines(code, "d")
        
        if klines is not None and len(klines) > 0:
            all_data[code] = klines
            print(f"  成功获取 {len(klines)} 条数据")
        else:
            print(f"  获取 {code} 数据失败")
    
    # 分析数据
    if all_data:
        print("\n数据分析:")
        for code, data in all_data.items():
            latest = data.iloc[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest
            
            change = latest['close'] - prev['close']
            change_pct = (change / prev['close']) * 100 if prev['close'] != 0 else 0
            
            print(f"  {code}: 最新价 {latest['close']:.2f}, "
                  f"涨跌 {change:+.2f} ({change_pct:+.2f}%)")


def get_realtime_quotes():
    """获取实时行情"""
    print("获取实时行情示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 股票代码列表
    codes = [
        "SZ.000001",  # 平安银行
        "SH.600000",  # 浦发银行
        "SZ.000002",  # 万科A
        "SH.600036",  # 招商银行
        "SZ.300015",  # 爱尔眼科
    ]
    
    print("获取实时行情...")
    ticks = tdx.ticks(codes)
    
    if ticks:
        print("实时行情数据:")
        print(f"{'代码':<12} {'最新价':<8} {'买一':<8} {'卖一':<8} {'涨跌幅':<8} {'成交量':<12}")
        print("-" * 60)
        
        for code, tick in ticks.items():
            print(f"{code:<12} {tick.last:<8.2f} {tick.buy1:<8.2f} "
                  f"{tick.sell1:<8.2f} {tick.rate:<8.2f}% {tick.volume:<12.0f}")
    else:
        print("获取实时行情失败")


def analyze_stock_trend():
    """分析股票趋势"""
    print("股票趋势分析示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    code = "SZ.000001"  # 平安银行
    
    # 获取日线数据
    print(f"分析 {code} 趋势...")
    klines = tdx.klines(code, "d")
    
    if klines is None or len(klines) < 20:
        print("数据不足，无法分析")
        return
    
    # 计算移动平均线
    klines['ma5'] = klines['close'].rolling(window=5).mean()
    klines['ma10'] = klines['close'].rolling(window=10).mean()
    klines['ma20'] = klines['close'].rolling(window=20).mean()
    
    # 最新数据
    latest = klines.iloc[-1]
    
    print(f"最新收盘价: {latest['close']:.2f}")
    print(f"5日均线: {latest['ma5']:.2f}")
    print(f"10日均线: {latest['ma10']:.2f}")
    print(f"20日均线: {latest['ma20']:.2f}")
    
    # 简单趋势判断
    if latest['close'] > latest['ma5'] > latest['ma10'] > latest['ma20']:
        trend = "强势上涨"
    elif latest['close'] > latest['ma5'] > latest['ma10']:
        trend = "上涨"
    elif latest['close'] < latest['ma5'] < latest['ma10'] < latest['ma20']:
        trend = "强势下跌"
    elif latest['close'] < latest['ma5'] < latest['ma10']:
        trend = "下跌"
    else:
        trend = "震荡"
    
    print(f"趋势判断: {trend}")
    
    # 计算近期涨跌幅
    if len(klines) >= 5:
        recent_change = (latest['close'] - klines.iloc[-5]['close']) / klines.iloc[-5]['close'] * 100
        print(f"近5日涨跌幅: {recent_change:+.2f}%")


def export_data_to_csv():
    """导出数据到CSV文件"""
    print("导出数据到CSV示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    code = "SZ.000001"  # 平安银行
    
    # 获取日线数据
    print(f"获取 {code} 数据并导出...")
    klines = tdx.klines(code, "d")
    
    if klines is not None and len(klines) > 0:
        # 导出到CSV文件
        filename = f"{code.replace('.', '_')}_daily.csv"
        klines.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已导出到: {filename}")
        print(f"共导出 {len(klines)} 条记录")
    else:
        print("获取数据失败，无法导出")


def main():
    """主函数"""
    print("通达信股票数据获取示例")
    print("=" * 50)
    
    # 运行各种示例
    examples = [
        get_single_stock_data,
        get_multiple_stocks_data,
        get_realtime_quotes,
        analyze_stock_trend,
        export_data_to_csv,
    ]
    
    for example in examples:
        try:
            example()
            print()
        except Exception as e:
            print(f"示例 {example.__name__} 运行失败: {e}")
            print()


if __name__ == "__main__":
    main()
