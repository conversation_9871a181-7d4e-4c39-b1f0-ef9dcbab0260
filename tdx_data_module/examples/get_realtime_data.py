#!/usr/bin/env python3
"""
获取实时数据示例
演示如何使用通达信数据模块获取实时行情数据
"""

import sys
import os
import time
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.exchange_tdx import ExchangeTDX
from core.exchange_tdx_hk import ExchangeTDXHK
from core.exchange_tdx_us import ExchangeTDXUS


def monitor_realtime_quotes():
    """监控实时行情"""
    print("实时行情监控示例")
    print("-" * 30)
    
    # 创建交易所实例
    tdx = ExchangeTDX()
    
    # 监控的股票列表
    watch_list = [
        "SZ.000001",  # 平安银行
        "SH.600000",  # 浦发银行
        "SZ.000002",  # 万科A
        "SH.600036",  # 招商银行
        "SZ.300015",  # 爱尔眼科
    ]
    
    print(f"开始监控 {len(watch_list)} 只股票的实时行情...")
    print("按 Ctrl+C 停止监控")
    print()
    
    try:
        while True:
            # 获取当前时间
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # 获取实时行情
            ticks = tdx.ticks(watch_list)
            
            if ticks:
                print(f"[{current_time}] 实时行情:")
                print(f"{'代码':<12} {'最新价':<8} {'涨跌幅':<8} {'买一':<8} {'卖一':<8} {'成交量':<12}")
                print("-" * 65)
                
                for code in watch_list:
                    if code in ticks:
                        tick = ticks[code]
                        # 根据涨跌幅设置颜色标识
                        if tick.rate > 0:
                            rate_str = f"+{tick.rate:.2f}%"
                        elif tick.rate < 0:
                            rate_str = f"{tick.rate:.2f}%"
                        else:
                            rate_str = f"{tick.rate:.2f}%"
                        
                        print(f"{code:<12} {tick.last:<8.2f} {rate_str:<8} "
                              f"{tick.buy1:<8.2f} {tick.sell1:<8.2f} {tick.volume:<12.0f}")
                    else:
                        print(f"{code:<12} {'无数据':<8}")
                
                print()
            else:
                print(f"[{current_time}] 获取实时行情失败")
            
            # 等待5秒后继续
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n监控已停止")


def compare_multi_market_quotes():
    """比较多市场实时行情"""
    print("多市场实时行情比较")
    print("-" * 30)
    
    # 创建不同市场的交易所实例
    exchanges = {
        "A股": ExchangeTDX(),
        "港股": ExchangeTDXHK(),
        "美股": ExchangeTDXUS(),
    }
    
    # 不同市场的股票代码
    market_codes = {
        "A股": ["SZ.000001", "SH.600000", "SZ.000002"],
        "港股": ["HK.00700", "HK.09988", "HK.03690"],
        "美股": ["US.AAPL", "US.MSFT", "US.GOOGL"],
    }
    
    print("获取多市场实时行情...")
    
    for market_name, exchange in exchanges.items():
        print(f"\n{market_name}市场:")
        codes = market_codes[market_name]
        
        try:
            ticks = exchange.ticks(codes)
            
            if ticks:
                print(f"{'代码':<12} {'最新价':<10} {'涨跌幅':<8} {'成交量':<12}")
                print("-" * 45)
                
                for code in codes:
                    if code in ticks:
                        tick = ticks[code]
                        print(f"{code:<12} {tick.last:<10.2f} {tick.rate:<8.2f}% {tick.volume:<12.0f}")
                    else:
                        print(f"{code:<12} {'无数据':<10}")
            else:
                print("  获取行情失败")
                
        except Exception as e:
            print(f"  获取{market_name}行情异常: {e}")


def alert_price_changes():
    """价格变动提醒"""
    print("价格变动提醒示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 监控配置
    watch_config = {
        "SZ.000001": {"name": "平安银行", "alert_threshold": 2.0},  # 涨跌幅超过2%提醒
        "SH.600000": {"name": "浦发银行", "alert_threshold": 1.5},  # 涨跌幅超过1.5%提醒
        "SZ.000002": {"name": "万科A", "alert_threshold": 3.0},     # 涨跌幅超过3%提醒
    }
    
    codes = list(watch_config.keys())
    
    print("开始监控价格变动...")
    print("提醒条件:")
    for code, config in watch_config.items():
        print(f"  {code} ({config['name']}): 涨跌幅超过 ±{config['alert_threshold']}%")
    print()
    
    try:
        while True:
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # 获取实时行情
            ticks = tdx.ticks(codes)
            
            if ticks:
                for code, tick in ticks.items():
                    config = watch_config[code]
                    threshold = config["alert_threshold"]
                    
                    # 检查是否触发提醒
                    if abs(tick.rate) >= threshold:
                        alert_type = "上涨" if tick.rate > 0 else "下跌"
                        print(f"🚨 [{current_time}] 价格提醒: "
                              f"{config['name']} ({code}) {alert_type} {tick.rate:.2f}% "
                              f"当前价格: {tick.last:.2f}")
            
            # 等待10秒后继续
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n价格监控已停止")


def get_market_overview():
    """获取市场概览"""
    print("市场概览示例")
    print("-" * 30)
    
    # 创建A股交易所实例
    tdx = ExchangeTDX()
    
    # 获取一些代表性股票
    representative_stocks = [
        ("SH.000001", "上证指数"),
        ("SZ.399001", "深证成指"),
        ("SZ.399006", "创业板指"),
        ("SZ.000001", "平安银行"),
        ("SH.600519", "贵州茅台"),
        ("SZ.000858", "五粮液"),
        ("SZ.300015", "爱尔眼科"),
    ]
    
    codes = [code for code, _ in representative_stocks]
    
    print("获取市场概览数据...")
    ticks = tdx.ticks(codes)
    
    if ticks:
        print("\n市场概览:")
        print(f"{'名称':<12} {'代码':<12} {'最新价':<10} {'涨跌幅':<8} {'状态':<6}")
        print("-" * 55)
        
        up_count = 0
        down_count = 0
        flat_count = 0
        
        for code, name in representative_stocks:
            if code in ticks:
                tick = ticks[code]
                
                # 判断涨跌状态
                if tick.rate > 0:
                    status = "上涨"
                    up_count += 1
                elif tick.rate < 0:
                    status = "下跌"
                    down_count += 1
                else:
                    status = "平盘"
                    flat_count += 1
                
                print(f"{name:<12} {code:<12} {tick.last:<10.2f} "
                      f"{tick.rate:<8.2f}% {status:<6}")
            else:
                print(f"{name:<12} {code:<12} {'无数据':<10}")
        
        print(f"\n统计: 上涨 {up_count} 只, 下跌 {down_count} 只, 平盘 {flat_count} 只")
        
        # 计算市场情绪
        total = up_count + down_count + flat_count
        if total > 0:
            up_ratio = up_count / total * 100
            if up_ratio > 60:
                market_mood = "乐观"
            elif up_ratio > 40:
                market_mood = "中性"
            else:
                market_mood = "悲观"
            
            print(f"市场情绪: {market_mood} (上涨比例: {up_ratio:.1f}%)")
    else:
        print("获取市场数据失败")


def main():
    """主函数"""
    print("通达信实时数据获取示例")
    print("=" * 50)
    
    # 提供选择菜单
    examples = [
        ("1", "监控实时行情", monitor_realtime_quotes),
        ("2", "多市场行情比较", compare_multi_market_quotes),
        ("3", "价格变动提醒", alert_price_changes),
        ("4", "市场概览", get_market_overview),
    ]
    
    print("请选择要运行的示例:")
    for code, name, _ in examples:
        print(f"  {code}. {name}")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    for code, name, func in examples:
        if choice == code:
            print(f"\n运行示例: {name}")
            print("=" * 30)
            try:
                func()
            except Exception as e:
                print(f"示例运行失败: {e}")
            return
    
    print("无效选择，运行所有非交互示例...")
    
    # 运行非交互示例
    non_interactive = [compare_multi_market_quotes, get_market_overview]
    
    for func in non_interactive:
        try:
            func()
            print()
        except Exception as e:
            print(f"示例 {func.__name__} 运行失败: {e}")
            print()


if __name__ == "__main__":
    main()
