# 通达信数据模块 (TDX Data Module)

这是一个从 Chanlun-PRO 框架中提取出来的独立通达信数据获取模块，可以独立运行并获取各种市场的股票数据。

## 功能特性

### 支持的市场
- **A股市场** (沪深股市)：股票、指数数据
- **港股市场**：香港股票数据  
- **美股市场**：美国股票数据
- **期货市场**：国内期货数据
- **外汇市场**：外汇数据

### 支持的数据类型
- **K线数据**：日线、分钟线等多种周期
- **实时行情**：最新价格、买卖盘等tick数据
- **股票列表**：获取各市场所有可交易股票代码
- **基本信息**：股票名称、市场分类等

### 核心功能
- **自动IP优选**：自动选择最优的通达信服务器
- **数据缓存**：本地文件缓存，提高数据获取效率
- **错误重试**：网络异常自动重试机制
- **多市场支持**：统一接口访问不同市场数据
- **前复权处理**：支持股票前复权数据计算

## 目录结构

```
tdx_data_module/
├── README.md                    # 说明文档
├── requirements.txt             # 依赖包列表
├── config.py                    # 配置文件
├── main.py                      # 主程序示例
├── core/                        # 核心模块
│   ├── __init__.py
│   ├── exchange_base.py         # 交易所基础类
│   ├── exchange_tdx.py          # A股通达信接口
│   ├── exchange_tdx_hk.py       # 港股通达信接口
│   ├── exchange_tdx_us.py       # 美股通达信接口
│   ├── exchange_tdx_futures.py  # 期货通达信接口
│   └── exchange_tdx_fx.py       # 外汇通达信接口
├── utils/                       # 工具模块
│   ├── __init__.py
│   ├── tdx_best_ip.py          # IP优选工具
│   ├── file_cache.py           # 文件缓存
│   ├── common_utils.py         # 通用工具函数
│   └── tdx_codes.py            # 股票代码映射
├── data/                        # 数据目录
│   ├── cache/                  # 缓存文件
│   └── logs/                   # 日志文件
└── examples/                    # 使用示例
    ├── get_stock_data.py       # 获取股票数据示例
    ├── get_realtime_data.py    # 获取实时数据示例
    └── batch_download.py       # 批量下载示例
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用
```python
from core.exchange_tdx import ExchangeTDX

# 创建A股数据接口
tdx = ExchangeTDX()

# 获取股票K线数据
klines = tdx.klines('SZ.000001', 'd')  # 获取平安银行日线数据
print(klines.head())

# 获取实时行情
ticks = tdx.ticks(['SZ.000001', 'SH.000001'])
print(ticks)

# 获取所有股票列表
stocks = tdx.all_stocks()
print(f"共有 {len(stocks)} 只股票")
```

### 3. 多市场数据获取
```python
from core.exchange_tdx_hk import ExchangeTDXHK
from core.exchange_tdx_us import ExchangeTDXUS

# 港股数据
hk_exchange = ExchangeTDXHK()
hk_klines = hk_exchange.klines('HK.00700', 'd')  # 腾讯控股

# 美股数据  
us_exchange = ExchangeTDXUS()
us_klines = us_exchange.klines('US.AAPL', 'd')   # 苹果公司
```

## 配置说明

在 `config.py` 中可以配置：
- 数据缓存路径
- 服务器连接超时时间
- 数据获取页数限制
- 日志级别等

## 注意事项

1. **网络要求**：需要能够访问通达信服务器
2. **使用限制**：请合理使用，避免频繁请求
3. **数据准确性**：数据仅供参考，投资需谨慎
4. **版权声明**：数据来源于通达信，请遵守相关使用协议

## 更新日志

- v1.0.0: 初始版本，支持A股、港股、美股、期货、外汇数据获取
