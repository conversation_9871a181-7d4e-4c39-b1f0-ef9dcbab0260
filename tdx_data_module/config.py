"""
通达信数据模块配置文件
"""
import pathlib
import os

# 数据缓存路径
DATA_PATH = "./data"

# 通达信服务器连接配置
TDX_CONNECT_TIMEOUT = 0.7  # 连接超时时间（秒）
TDX_RETRY_TIMES = 3        # 重试次数
TDX_PAGES_LIMIT = 8        # 数据获取页数限制（每页700条）

# 缓存配置
CACHE_ENABLED = True       # 是否启用缓存
CACHE_EXPIRE_DAYS = 15     # 缓存过期天数

# 日志配置
LOG_LEVEL = "INFO"         # 日志级别：DEBUG, INFO, WARNING, ERROR
LOG_TO_FILE = True         # 是否写入日志文件

# 数据库配置（可选，用于高级缓存）
DB_ENABLED = False         # 是否启用数据库
DB_TYPE = "sqlite"         # 数据库类型：sqlite, mysql
DB_HOST = "127.0.0.1"
DB_PORT = 3306
DB_USER = "root"
DB_PASSWORD = "123456"
DB_DATABASE = "tdx_data"

# 代理配置（如果需要）
PROXY_ENABLED = False
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 7890

def get_data_path():
    """获取数据目录路径"""
    data_path = pathlib.Path(DATA_PATH)
    if not data_path.is_absolute():
        # 相对路径，基于当前文件目录
        current_dir = pathlib.Path(__file__).parent
        data_path = current_dir / DATA_PATH
    
    # 创建目录
    data_path.mkdir(parents=True, exist_ok=True)
    
    # 创建子目录
    (data_path / "cache").mkdir(exist_ok=True)
    (data_path / "logs").mkdir(exist_ok=True)
    
    return data_path

def get_cache_path():
    """获取缓存目录路径"""
    return get_data_path() / "cache"

def get_log_path():
    """获取日志目录路径"""
    return get_data_path() / "logs"

# 市场配置
MARKETS = {
    "A": {
        "name": "A股市场",
        "exchange_class": "ExchangeTDX",
        "enabled": True
    },
    "HK": {
        "name": "港股市场", 
        "exchange_class": "ExchangeTDXHK",
        "enabled": True
    },
    "US": {
        "name": "美股市场",
        "exchange_class": "ExchangeTDXUS", 
        "enabled": True
    },
    "FUTURES": {
        "name": "期货市场",
        "exchange_class": "ExchangeTDXFutures",
        "enabled": True
    },
    "FX": {
        "name": "外汇市场",
        "exchange_class": "ExchangeTDXFX",
        "enabled": True
    }
}

# 支持的周期
FREQUENCIES = {
    "1m": "1分钟",
    "5m": "5分钟", 
    "15m": "15分钟",
    "30m": "30分钟",
    "60m": "60分钟",
    "d": "日线",
    "w": "周线",
    "M": "月线"
}
