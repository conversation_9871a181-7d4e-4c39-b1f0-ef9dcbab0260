#!/usr/bin/env python3
"""
A股板块数据测试程序
测试板块指数历史数据和成分股数据
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX


def test_a_stock_boards():
    """测试A股板块数据功能"""
    print("A股板块数据测试程序")
    print("=" * 50)
    
    try:
        # 创建A股交易所实例
        tdx = ExchangeTDX()
        print("✓ A股交易所创建成功")
        
        # 1. 测试获取所有板块列表
        print("\n1. 测试获取所有板块列表...")
        all_plates = tdx.get_all_plates()
        if all_plates:
            industries = all_plates.get("industries", [])
            concepts = all_plates.get("concepts", [])
            print(f"   ✓ 行业板块数量: {len(industries)}")
            print(f"   ✓ 概念板块数量: {len(concepts)}")
            
            # 显示前10个行业板块
            print("   前10个行业板块:")
            for i, industry in enumerate(industries[:10]):
                print(f"     {i+1}. {industry}")
            
            # 显示前10个概念板块
            print("   前10个概念板块:")
            for i, concept in enumerate(concepts[:10]):
                print(f"     {i+1}. {concept}")
        else:
            print("   ✗ 获取板块列表失败")
            return False
        
        # 2. 测试获取板块指数K线数据
        print("\n2. 测试获取板块指数K线数据...")
        test_industries = ["银行", "保险", "证券", "房地产", "钢铁"]
        
        for industry in test_industries:
            try:
                klines = tdx.get_plate_klines(industry, "hy", "d")
                if klines is not None and len(klines) > 0:
                    print(f"   ✓ {industry}行业指数: {len(klines)} 条K线数据")
                    latest = klines.iloc[-1]
                    print(f"     最新数据: {latest['date'].strftime('%Y-%m-%d')} 收盘价: {latest['close']}")
                else:
                    print(f"   ✗ {industry}行业指数: 获取失败")
            except Exception as e:
                print(f"   ✗ {industry}行业指数: 异常 {e}")
        
        # 3. 测试获取概念板块指数数据
        print("\n3. 测试获取概念板块指数数据...")
        test_concepts = ["人工智能", "5G", "新能源汽车", "芯片", "区块链"]
        
        for concept in test_concepts:
            try:
                klines = tdx.get_plate_klines(concept, "gn", "d")
                if klines is not None and len(klines) > 0:
                    print(f"   ✓ {concept}概念指数: {len(klines)} 条K线数据")
                    latest = klines.iloc[-1]
                    print(f"     最新数据: {latest['date'].strftime('%Y-%m-%d')} 收盘价: {latest['close']}")
                else:
                    print(f"   ✗ {concept}概念指数: 获取失败")
            except Exception as e:
                print(f"   ✗ {concept}概念指数: 异常 {e}")
        
        # 4. 测试获取板块成分股
        print("\n4. 测试获取板块成分股...")
        test_plate = "银行"
        
        try:
            stocks = tdx.plate_stocks(test_plate)
            if stocks and len(stocks) > 0:
                print(f"   ✓ {test_plate}板块成分股: {len(stocks)} 只股票")
                print("   前10只成分股:")
                for i, stock in enumerate(stocks[:10]):
                    print(f"     {i+1}. {stock['code']} - {stock.get('name', '未知')}")
            else:
                print(f"   ⚠️ {test_plate}板块: 暂无成分股数据（需要真实数据源）")
        except Exception as e:
            print(f"   ✗ {test_plate}板块成分股: 异常 {e}")
        
        # 5. 测试获取股票所属板块
        print("\n5. 测试获取股票所属板块...")
        test_stocks = ["SZ.000001", "SH.600000", "SZ.300015"]
        
        for stock_code in test_stocks:
            try:
                plates = tdx.stock_owner_plate(stock_code)
                if plates:
                    hys = plates.get("HY", [])
                    gns = plates.get("GN", [])
                    print(f"   ✓ {stock_code}:")
                    if hys:
                        print(f"     行业板块: {[hy['name'] for hy in hys]}")
                    else:
                        print(f"     行业板块: 暂无数据")
                    if gns:
                        print(f"     概念板块: {[gn['name'] for gn in gns]}")
                    else:
                        print(f"     概念板块: 暂无数据")
                else:
                    print(f"   ✗ {stock_code}: 获取板块信息失败")
            except Exception as e:
                print(f"   ✗ {stock_code}: 异常 {e}")
        
        # 6. 测试板块指数数据质量
        print("\n6. 测试板块指数数据质量...")
        test_industry = "银行"
        
        try:
            klines = tdx.get_plate_klines(test_industry, "hy", "d")
            if klines is not None and len(klines) > 0:
                print(f"   ✓ {test_industry}指数数据质量检查:")
                
                # 检查必需字段
                required_columns = ['code', 'date', 'open', 'close', 'high', 'low', 'volume']
                missing_columns = [col for col in required_columns if col not in klines.columns]
                if missing_columns:
                    print(f"     ⚠️ 缺少字段: {missing_columns}")
                else:
                    print(f"     ✓ 数据字段完整")
                
                # 检查数据范围
                print(f"     数据时间范围: {klines['date'].min().strftime('%Y-%m-%d')} 到 {klines['date'].max().strftime('%Y-%m-%d')}")
                print(f"     价格范围: {klines['close'].min():.2f} - {klines['close'].max():.2f}")
                print(f"     平均成交量: {klines['volume'].mean():.0f}")
                
                # 检查数据连续性
                date_diff = klines['date'].diff().dt.days
                max_gap = date_diff.max()
                print(f"     最大时间间隔: {max_gap} 天")
                
            else:
                print(f"   ✗ {test_industry}指数: 无数据")
        except Exception as e:
            print(f"   ✗ 数据质量检查异常: {e}")
        
        print(f"\n{'='*50}")
        print("A股板块数据测试完成")
        print(f"{'='*50}")
        
        # 总结
        print("\n测试总结:")
        print("✅ 板块列表获取: 支持")
        print("✅ 板块指数K线: 支持（模拟数据）")
        print("⚠️ 板块成分股: 需要真实数据源")
        print("⚠️ 股票板块归属: 需要真实数据源")
        
        print("\n说明:")
        print("- 板块指数K线数据使用模拟数据，实际应用需要接入真实数据源")
        print("- 成分股数据需要从东方财富、同花顺等数据源获取")
        print("- 可以通过akshare等库获取真实的板块数据")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_a_stock_boards()
    if success:
        print("\n🎉 A股板块数据功能基本可用！")
        print("建议接入真实数据源以获得完整功能。")
    else:
        print("\n❌ A股板块数据功能测试失败")
    
    sys.exit(0 if success else 1)
