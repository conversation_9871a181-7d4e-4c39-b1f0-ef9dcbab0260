#!/usr/bin/env python3
"""
全市场数据测试程序
测试所有市场的数据获取功能
"""

import sys
import os
import traceback
import time
import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX
from core.exchange_tdx_hk import ExchangeTDXHK
from core.exchange_tdx_us import ExchangeTDXUS
from core.exchange_tdx_futures import ExchangeTDXFutures
from core.exchange_tdx_fx import ExchangeTDXFX


class MarketTester:
    """市场测试器"""
    
    def __init__(self, name, exchange_class):
        self.name = name
        self.exchange_class = exchange_class
        self.exchange = None
        self.test_results = {}
    
    def test_connection(self):
        """测试连接"""
        try:
            self.exchange = self.exchange_class()
            self.test_results['connection'] = True
            print(f"   ✓ {self.name}连接成功")
            return True
        except Exception as e:
            self.test_results['connection'] = False
            print(f"   ✗ {self.name}连接失败: {e}")
            return False
    
    def test_stock_list(self):
        """测试股票/合约列表"""
        if not self.exchange:
            self.test_results['stock_list'] = False
            return False
        
        try:
            stocks = self.exchange.all_stocks()
            if stocks and len(stocks) > 0:
                self.test_results['stock_list'] = True
                print(f"   ✓ {self.name}获取 {len(stocks)} 个标的")
                return True
            else:
                self.test_results['stock_list'] = False
                print(f"   ✗ {self.name}获取标的列表失败")
                return False
        except Exception as e:
            self.test_results['stock_list'] = False
            print(f"   ✗ {self.name}获取标的列表异常: {e}")
            return False
    
    def test_klines(self, test_codes):
        """测试K线数据"""
        if not self.exchange:
            self.test_results['klines'] = False
            return False

        # 对于期货，使用实际获取到的合约代码
        if self.name == "期货":
            try:
                stocks = self.exchange.all_stocks()
                if stocks and len(stocks) > 0:
                    test_codes = [stocks[i]['code'] for i in range(min(3, len(stocks)))]
            except:
                pass

        success_count = 0
        for code in test_codes:
            try:
                klines = self.exchange.klines(code, "d")
                if klines is not None and len(klines) > 0:
                    success_count += 1
                    print(f"     ✓ {code}: {len(klines)} 条数据")
                else:
                    print(f"     ✗ {code}: 获取失败")
            except Exception as e:
                print(f"     ✗ {code}: 异常 {e}")

        success = success_count > 0
        self.test_results['klines'] = success
        print(f"   K线测试: {success_count}/{len(test_codes)} 成功")
        return success
    
    def test_ticks(self, test_codes):
        """测试实时行情"""
        if not self.exchange:
            self.test_results['ticks'] = False
            return False

        # 对于期货，使用实际获取到的合约代码
        if self.name == "期货":
            try:
                stocks = self.exchange.all_stocks()
                if stocks and len(stocks) > 0:
                    test_codes = [stocks[i]['code'] for i in range(min(3, len(stocks)))]
            except:
                pass

        try:
            ticks = self.exchange.ticks(test_codes)
            if ticks and len(ticks) > 0:
                self.test_results['ticks'] = True
                print(f"   ✓ {self.name}实时行情: {len(ticks)} 个标的")
                return True
            else:
                self.test_results['ticks'] = False
                print(f"   ✗ {self.name}实时行情获取失败")
                return False
        except Exception as e:
            self.test_results['ticks'] = False
            print(f"   ✗ {self.name}实时行情异常: {e}")
            return False
    
    def get_success_rate(self):
        """获取成功率"""
        if not self.test_results:
            return 0.0
        
        success_count = sum(1 for result in self.test_results.values() if result)
        total_count = len(self.test_results)
        return success_count / total_count * 100


def test_market(market_name, exchange_class, test_codes):
    """测试单个市场"""
    print(f"\n{market_name}市场测试:")
    print("-" * 30)
    
    tester = MarketTester(market_name, exchange_class)
    
    # 测试连接
    if not tester.test_connection():
        return tester
    
    # 测试股票列表
    tester.test_stock_list()
    
    # 测试K线数据
    print(f"   测试K线数据...")
    tester.test_klines(test_codes)
    
    # 测试实时行情
    print(f"   测试实时行情...")
    tester.test_ticks(test_codes)
    
    return tester


def main():
    """主测试函数"""
    print("全市场数据测试程序")
    print("=" * 50)
    print(f"测试开始时间: {datetime.datetime.now()}")
    
    # 定义各市场的测试配置
    market_configs = [
        {
            "name": "A股",
            "class": ExchangeTDX,
            "test_codes": ["SZ.000001", "SH.600000", "SZ.300015"]
        },
        {
            "name": "港股",
            "class": ExchangeTDXHK,
            "test_codes": ["HK.00700", "HK.09988", "HK.03690"]
        },
        {
            "name": "美股",
            "class": ExchangeTDXUS,
            "test_codes": ["US.AAPL", "US.MSFT", "US.GOOGL"]
        },
        {
            "name": "期货",
            "class": ExchangeTDXFutures,
            "test_codes": ["FUTURES.IF2412", "FUTURES.CU2501", "FUTURES.RB2505"]
        },
        {
            "name": "外汇",
            "class": ExchangeTDXFX,
            "test_codes": ["FX.EURUSD", "FX.GBPUSD", "FX.USDJPY"]
        }
    ]
    
    # 测试所有市场
    testers = []
    for config in market_configs:
        tester = test_market(config["name"], config["class"], config["test_codes"])
        testers.append(tester)
    
    # 输出汇总结果
    print("\n" + "=" * 50)
    print("全市场测试结果汇总")
    print("=" * 50)
    
    print(f"{'市场':<8} {'连接':<6} {'列表':<6} {'K线':<6} {'行情':<6} {'成功率':<8}")
    print("-" * 50)
    
    overall_success = 0
    overall_total = 0
    
    for tester in testers:
        results = tester.test_results
        connection = "✓" if results.get('connection', False) else "✗"
        stock_list = "✓" if results.get('stock_list', False) else "✗"
        klines = "✓" if results.get('klines', False) else "✗"
        ticks = "✓" if results.get('ticks', False) else "✗"
        success_rate = tester.get_success_rate()
        
        print(f"{tester.name:<8} {connection:<6} {stock_list:<6} {klines:<6} {ticks:<6} {success_rate:<8.1f}%")
        
        # 统计总体成功率
        for result in results.values():
            overall_total += 1
            if result:
                overall_success += 1
    
    overall_rate = overall_success / overall_total * 100 if overall_total > 0 else 0
    
    print("-" * 50)
    print(f"总体成功率: {overall_success}/{overall_total} ({overall_rate:.1f}%)")
    
    # 详细分析
    print(f"\n详细分析:")
    
    # 连接成功的市场
    connected_markets = [t.name for t in testers if t.test_results.get('connection', False)]
    print(f"连接成功: {', '.join(connected_markets)}")
    
    # 数据获取成功的市场
    data_success_markets = [t.name for t in testers if t.test_results.get('klines', False)]
    print(f"数据获取成功: {', '.join(data_success_markets)}")
    
    # 实时行情成功的市场
    realtime_success_markets = [t.name for t in testers if t.test_results.get('ticks', False)]
    print(f"实时行情成功: {', '.join(realtime_success_markets)}")
    
    # 建议
    print(f"\n使用建议:")
    if "A股" in connected_markets:
        print("- A股数据功能完整，推荐作为主要数据源")
    
    if "期货" in connected_markets:
        print("- 期货数据可用，适合期货交易分析")
    
    if len(connected_markets) >= 3:
        print("- 多市场数据可用，支持跨市场分析")
    else:
        print("- 建议重点使用连接成功的市场数据")
    
    print(f"\n测试结束时间: {datetime.datetime.now()}")
    
    # 返回是否有至少一个市场完全可用
    fully_working_markets = [t for t in testers if all(t.test_results.values())]
    return len(fully_working_markets) > 0


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 至少有一个市场完全可用，模块可以正常使用")
    else:
        print("\n⚠️ 所有市场都存在问题，请检查网络连接和配置")
    
    sys.exit(0 if success else 1)
