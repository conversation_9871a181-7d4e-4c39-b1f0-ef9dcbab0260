#!/usr/bin/env python3
"""
美股数据调试程序
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx_us import ExchangeTDXUS


def debug_us_data():
    """调试美股数据获取"""
    print("美股数据调试程序")
    print("=" * 40)
    
    try:
        # 创建美股交易所实例
        us_exchange = ExchangeTDXUS()
        
        # 获取美股列表
        stocks = us_exchange.all_stocks()
        print(f"获取到 {len(stocks)} 只美股")
        
        # 显示前10只美股
        print("前10只美股:")
        for i, stock in enumerate(stocks[:10]):
            print(f"  {i+1}. {stock['code']} - {stock['name']}")
        
        # 测试实际获取到的美股代码
        if stocks:
            test_code = stocks[0]['code']
            print(f"\n测试获取 {test_code} 数据...")
            
            # 测试代码映射
            market, tdx_code = us_exchange.to_tdx_code(test_code)
            print(f"代码映射: {test_code} -> 市场: {market}, 代码: {tdx_code}")
            
            # 测试K线数据
            try:
                klines = us_exchange.klines(test_code, "d")
                if klines is not None and len(klines) > 0:
                    print(f"✓ 成功获取 {len(klines)} 条K线数据")
                    print(f"最新数据: {klines.iloc[-1]['date']} - 收盘价: {klines.iloc[-1]['close']}")
                else:
                    print("✗ K线数据获取失败")
            except Exception as e:
                print(f"✗ K线数据获取异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试实时行情
            try:
                ticks = us_exchange.ticks([test_code])
                if ticks and len(ticks) > 0:
                    print(f"✓ 成功获取实时行情")
                    for code, tick in ticks.items():
                        print(f"  {code}: 最新价 {tick.last}, 涨跌幅 {tick.rate}%")
                else:
                    print("✗ 实时行情获取失败")
            except Exception as e:
                print(f"✗ 实时行情获取异常: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_us_data()
