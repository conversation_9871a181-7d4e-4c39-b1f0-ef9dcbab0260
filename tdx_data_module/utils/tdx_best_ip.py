# coding: utf-8
"""
通达信最优IP选择工具
基于 pytdx 项目的IP寻优功能
"""

import datetime
from pytdx.exhq import TdxExHq_API
from pytdx.hq import TdxHq_API

# 通达信股票行情服务器列表
stock_ip = [
    {"ip": "**************", "port": 7709, "name": "广州电信"},
    {"ip": "***************", "port": 7709, "name": "深圳电信"},
    {"ip": "**************", "port": 7709, "name": "上海电信"},
    {"ip": "**************", "port": 7709, "name": "上海电信2"},
    {"ip": "***************", "port": 7709, "name": "北京联通"},
    {"ip": "***************", "port": 7709, "name": "北京联通2"},
    {"ip": "*************", "port": 7709, "name": "上海联通"},
    {"ip": "************", "port": 7709, "name": "上海联通2"},
    {"ip": "**************", "port": 7709, "name": "浙江杭州"},
    {"ip": "**************", "port": 7709, "name": "浙江杭州2"},
    {"ip": "**************", "port": 7709, "name": "河北石家庄"},
    {"ip": "*************", "port": 7709, "name": "浙江温州"},
    {"ip": "**************", "port": 7709, "name": "四川成都"},
    {"ip": "*************", "port": 7709, "name": "四川成都2"},
    {"ip": "***********", "port": 7709, "name": "广东茂名"},
    {"ip": "*************", "port": 7709, "name": "广东茂名2"},
    {"ip": "*************", "port": 7709, "name": "广西南宁"},
    {"ip": "*************", "port": 7709, "name": "广东深圳"},
    {"ip": "**************", "port": 7709, "name": "广东深圳2"},
    {"ip": "*************", "port": 7709, "name": "广东深圳3"},
    {"ip": "*************", "port": 7709, "name": "阿里云"},
    {"ip": "*************", "port": 7709, "name": "阿里云2"},
    {"ip": "**************", "port": 7709, "name": "江苏南京"},
    {"ip": "**********", "port": 7709, "name": "江苏南京2"},
    {"ip": "*************", "port": 7709, "name": "湖北武汉"},
    {"ip": "**************", "port": 7709, "name": "湖北武汉2"},
    {"ip": "**************", "port": 7709, "name": "四川成都3"},
    {"ip": "**************", "port": 7709, "name": "四川成都4"},
    {"ip": "**************", "port": 7709, "name": "辽宁大连"},
    {"ip": "**************", "port": 7709, "name": "辽宁大连2"},
    {"ip": "*************", "port": 7709, "name": "河南郑州"},
    {"ip": "*************", "port": 7709, "name": "河南郑州2"},
    {"ip": "*************", "port": 7709, "name": "北京"},
    {"ip": "*************", "port": 7709, "name": "北京2"},
    {"ip": "************", "port": 7709, "name": "安徽合肥"},
    {"ip": "************", "port": 7709, "name": "安徽合肥2"},
    {"ip": "***************", "port": 7709, "name": "天津"},
    {"ip": "***************", "port": 7709, "name": "天津2"},
]

# 通达信期货行情服务器列表
future_ip = [
    {"ip": "**************", "port": 7727, "name": "扩展市场广州电信1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场上海联通1"},
    {"ip": "************", "port": 7727, "name": "扩展市场上海联通2"},
    {"ip": "**************", "port": 7727, "name": "扩展市场浙江杭州1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场浙江杭州2"},
    {"ip": "**************", "port": 7727, "name": "扩展市场河北石家庄"},
    {"ip": "*************", "port": 7727, "name": "扩展市场浙江温州"},
    {"ip": "**************", "port": 7727, "name": "扩展市场四川成都1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场四川成都2"},
    {"ip": "***********", "port": 7727, "name": "扩展市场广东茂名1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场广东茂名2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场广西南宁"},
    {"ip": "*************", "port": 7727, "name": "扩展市场广东深圳1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场广东深圳2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场广东深圳3"},
    {"ip": "*************", "port": 7727, "name": "扩展市场阿里云1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场阿里云2"},
    {"ip": "**************", "port": 7727, "name": "扩展市场江苏南京1"},
    {"ip": "**********", "port": 7727, "name": "扩展市场江苏南京2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场湖北武汉1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场湖北武汉2"},
    {"ip": "**************", "port": 7727, "name": "扩展市场四川成都3"},
    {"ip": "**************", "port": 7727, "name": "扩展市场四川成都4"},
    {"ip": "**************", "port": 7727, "name": "扩展市场辽宁大连1"},
    {"ip": "**************", "port": 7727, "name": "扩展市场辽宁大连2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场河南郑州1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场河南郑州2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场北京1"},
    {"ip": "*************", "port": 7727, "name": "扩展市场北京2"},
    {"ip": "************", "port": 7727, "name": "扩展市场安徽合肥1"},
    {"ip": "************", "port": 7727, "name": "扩展市场安徽合肥2"},
    {"ip": "***************", "port": 7727, "name": "扩展市场天津1"},
    {"ip": "***************", "port": 7727, "name": "扩展市场天津2"},
    {"ip": "*************", "port": 7727, "name": "扩展市场广州双线4"},
    {"ip": "**************", "port": 7727, "name": "扩展市场上海双线5"},
    {"ip": "*************", "port": 7727, "name": "扩展市场上海双线6"},
]


def ping(ip, port=7709, type_="stock"):
    """
    测试通达信服务器连接速度
    
    Args:
        ip: 服务器IP地址
        port: 服务器端口
        type_: 服务器类型，stock或future
        
    Returns:
        datetime.timedelta: 连接耗时，如果连接失败返回很大的值
    """
    api = TdxHq_API()
    apix = TdxExHq_API()
    __time1 = datetime.datetime.now()
    
    try:
        if type_ in ["stock"]:
            with api.connect(ip, port, time_out=0.7):
                res = api.get_security_list(0, 1)
                if res is not None:
                    if len(res) > 800:
                        print("GOOD RESPONSE {}".format(ip))
                        return datetime.datetime.now() - __time1
                    else:
                        print("BAD RESPONSE {}".format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:
                    print("BAD RESPONSE {}".format(ip))
                    return datetime.timedelta(9, 9, 0)
        elif type_ in ["future"]:
            with apix.connect(ip, port, time_out=0.7):
                res = apix.get_instrument_count()
                if res is not None:
                    if res > 20000:
                        print("GOOD RESPONSE {}".format(ip))
                        return datetime.datetime.now() - __time1
                    else:
                        print("️Bad FUTUREIP REPSONSE {}".format(ip))
                        return datetime.timedelta(9, 9, 0)
                else:
                    print("️Bad FUTUREIP REPSONSE {}".format(ip))
                    return datetime.timedelta(9, 9, 0)
    except Exception as e:
        if isinstance(e, TypeError):
            pass
        else:
            print("BAD RESPONSE {}".format(ip))
        return datetime.timedelta(9, 9, 0)


def select_best_ip(_type="stock"):
    """
    选择最优的通达信服务器IP
    
    Args:
        _type: 服务器类型，stock或future
        
    Returns:
        dict: 最优服务器信息，包含ip、port、name
    """
    ip_list = stock_ip if _type == "stock" else future_ip
    
    print(f"正在测试 {len(ip_list)} 个{_type}服务器...")
    data = [ping(x["ip"], x["port"], _type) for x in ip_list]
    results = []
    
    for i in range(len(data)):
        # 删除ping不通的数据
        if data[i] < datetime.timedelta(0, 9, 0):
            results.append((data[i], ip_list[i]))
    
    if not results:
        print("警告：没有找到可用的服务器，使用默认服务器")
        return ip_list[0]
    
    # 按照ping值从小到大排序
    results = [x[1] for x in sorted(results, key=lambda x: x[0])]
    
    best_server = results[0]
    print(f"选择最优服务器：{best_server['name']} ({best_server['ip']}:{best_server['port']})")
    
    return best_server


if __name__ == "__main__":
    print("测试股票服务器...")
    print(f"共有 {len(stock_ip)} 个股票服务器")
    ip = select_best_ip("stock")
    print(f"最优股票服务器：{ip}")
    
    print("\n测试期货服务器...")
    print(f"共有 {len(future_ip)} 个期货服务器")
    ip = select_best_ip("future")
    print(f"最优期货服务器：{ip}")
