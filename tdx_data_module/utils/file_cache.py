"""
文件缓存数据库
用于缓存通达信K线数据到本地文件
"""
import datetime
import random
import pandas as pd
from pathlib import Path
from typing import Union
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import get_cache_path, CACHE_EXPIRE_DAYS
from utils.common_utils import datetime_to_int


class FileCacheDB:
    """文件缓存数据库类"""
    
    def __init__(self):
        self.cache_path = get_cache_path()
        self.klines_path = self.cache_path / "klines"
        
        # 创建缓存目录
        self.klines_path.mkdir(parents=True, exist_ok=True)
        
        # 为不同市场创建子目录
        markets = ["A", "HK", "US", "FUTURES", "FX"]
        for market in markets:
            (self.klines_path / market).mkdir(exist_ok=True)
    
    def get_tdx_klines(
        self, market: str, code: str, frequency: str
    ) -> Union[None, pd.DataFrame]:
        """
        获取缓存在文件中的K线数据
        
        Args:
            market: 市场代码
            code: 股票代码
            frequency: 周期
            
        Returns:
            pd.DataFrame: K线数据，如果没有缓存返回None
        """
        file_pathname = (
            self.klines_path / market / f"{code.replace('.', '_')}_{frequency}.csv"
        )
        
        if not file_pathname.is_file():
            return None
        
        try:
            _klines = pd.read_csv(file_pathname)
        except Exception as e:
            print(f"读取缓存文件失败: {e}")
            # 删除损坏的文件
            file_pathname.unlink(missing_ok=True)
            return None
        
        if len(_klines) > 0:
            # 检查日期列名
            date_col = None
            for col in ["date", "datetime"]:
                if col in _klines.columns:
                    date_col = col
                    break

            if date_col:
                try:
                    _klines["date"] = pd.to_datetime(_klines[date_col], errors='coerce')
                    # 如果 date 有 NaN 或转换失败则返回 None
                    if _klines["date"].isnull().any():
                        print(f"缓存文件日期数据异常，删除缓存: {file_pathname}")
                        # 删除损坏的缓存文件
                        try:
                            os.remove(file_pathname)
                        except:
                            pass
                        return None
                    # 不返回最后一行（可能不完整）
                    _klines = _klines.iloc[0:-1:]
                except Exception as e:
                    print(f"缓存文件时间格式错误，删除缓存: {file_pathname}, 错误: {e}")
                    # 删除损坏的缓存文件
                    try:
                        os.remove(file_pathname)
                    except:
                        pass
                    return None
            else:
                print(f"缓存文件缺少日期列: {file_pathname}")
                return None
        
        # 随机清理过期缓存
        if random.randint(0, 1000) <= 5:
            self.clear_tdx_old_klines(market)
        
        return _klines
    
    def save_tdx_klines(
        self, market: str, code: str, frequency: str, kline: pd.DataFrame
    ) -> bool:
        """
        保存通达信K线数据到文件
        
        Args:
            market: 市场代码
            code: 股票代码
            frequency: 周期
            kline: K线数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            file_pathname = (
                self.klines_path / market / f"{code.replace('.', '_')}_{frequency}.csv"
            )
            
            # 确保目录存在
            file_pathname.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存数据
            kline.to_csv(file_pathname, index=False)
            print(f"缓存K线数据: {code} {frequency} -> {file_pathname}")
            return True
            
        except Exception as e:
            print(f"保存缓存文件失败: {e}")
            return False
    
    def clear_tdx_old_klines(self, market: str):
        """
        删除过期的K线数据缓存文件
        
        Args:
            market: 市场代码
        """
        try:
            del_lt_times = datetime_to_int(datetime.datetime.now()) - (
                CACHE_EXPIRE_DAYS * 24 * 60 * 60
            )
            
            market_path = self.klines_path / market
            if not market_path.exists():
                return
            
            deleted_count = 0
            for filename in market_path.glob("*.csv"):
                try:
                    if filename.stat().st_mtime < del_lt_times:
                        filename.unlink()
                        deleted_count += 1
                except Exception:
                    pass
            
            if deleted_count > 0:
                print(f"清理过期缓存文件: {market} 市场 {deleted_count} 个文件")
                
        except Exception as e:
            print(f"清理缓存文件失败: {e}")
    
    def clear_all_cache(self):
        """清理所有缓存文件"""
        try:
            import shutil
            if self.klines_path.exists():
                shutil.rmtree(self.klines_path)
                self.klines_path.mkdir(parents=True, exist_ok=True)
                print("已清理所有缓存文件")
        except Exception as e:
            print(f"清理所有缓存失败: {e}")
    
    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        info = {
            "cache_path": str(self.cache_path),
            "markets": {}
        }
        
        try:
            for market_dir in self.klines_path.iterdir():
                if market_dir.is_dir():
                    market_name = market_dir.name
                    files = list(market_dir.glob("*.csv"))
                    total_size = sum(f.stat().st_size for f in files)
                    
                    info["markets"][market_name] = {
                        "file_count": len(files),
                        "total_size_mb": round(total_size / 1024 / 1024, 2)
                    }
        except Exception as e:
            print(f"获取缓存信息失败: {e}")
        
        return info
    
    def cache_exists(self, market: str, code: str, frequency: str) -> bool:
        """检查缓存是否存在"""
        file_pathname = (
            self.klines_path / market / f"{code.replace('.', '_')}_{frequency}.csv"
        )
        return file_pathname.is_file()
    
    def get_cache_file_path(self, market: str, code: str, frequency: str) -> Path:
        """获取缓存文件路径"""
        return self.klines_path / market / f"{code.replace('.', '_')}_{frequency}.csv"


# 全局缓存实例
_file_cache_db = None


def get_file_cache_db() -> FileCacheDB:
    """获取全局文件缓存实例"""
    global _file_cache_db
    if _file_cache_db is None:
        _file_cache_db = FileCacheDB()
    return _file_cache_db


if __name__ == "__main__":
    # 测试缓存功能
    cache_db = FileCacheDB()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'date': pd.date_range('2024-01-01', periods=10),
        'open': [100 + i for i in range(10)],
        'high': [105 + i for i in range(10)],
        'low': [95 + i for i in range(10)],
        'close': [102 + i for i in range(10)],
        'volume': [1000 + i * 100 for i in range(10)]
    })
    
    # 测试保存
    cache_db.save_tdx_klines("A", "SZ.000001", "d", test_data)
    
    # 测试读取
    cached_data = cache_db.get_tdx_klines("A", "SZ.000001", "d")
    print("缓存数据:")
    print(cached_data)
    
    # 获取缓存信息
    info = cache_db.get_cache_info()
    print("缓存信息:")
    print(info)
