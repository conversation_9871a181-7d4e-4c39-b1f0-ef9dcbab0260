"""
通用工具函数
"""
import datetime
import time
import functools
import pytz

# 时区设置
TZ = pytz.timezone("Asia/Shanghai")


def singleton(cls):
    """单例装饰器"""
    instances = {}
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


def datetime_to_str(dt: datetime.datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    将datetime对象转换为字符串
    """
    if dt is None:
        return ""
    return dt.strftime(fmt)


def str_to_datetime(date_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> datetime.datetime:
    """
    将字符串转换为datetime对象
    """
    if not date_str:
        return None
    return datetime.datetime.strptime(date_str, fmt)


def datetime_to_int(dt: datetime.datetime) -> int:
    """
    将datetime对象转换为时间戳
    """
    if dt is None:
        return 0
    return int(dt.timestamp())


def timeint_to_datetime(timestamp: int) -> datetime.datetime:
    """
    将时间戳转换为datetime对象
    """
    if timestamp == 0:
        return None
    return datetime.datetime.fromtimestamp(timestamp)


def timeint_to_str(timestamp: int, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    将时间戳转换为字符串
    """
    if timestamp == 0:
        return ""
    dt = timeint_to_datetime(timestamp)
    return datetime_to_str(dt, fmt)


def str_to_timeint(date_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> int:
    """
    将字符串转换为时间戳
    """
    if not date_str:
        return 0
    dt = str_to_datetime(date_str, fmt)
    return datetime_to_int(dt)


def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """
    异常重试装饰器
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        time.sleep(delay)
                        continue
                    else:
                        raise last_exception
            return None
        return wrapper
    return decorator


def safe_float(value, default: float = 0.0) -> float:
    """
    安全转换为float类型
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value, default: int = 0) -> int:
    """
    安全转换为int类型
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def format_code(code: str) -> str:
    """
    格式化股票代码
    """
    if not code:
        return ""
    
    # 移除空格
    code = code.strip().upper()
    
    # 确保格式为 MARKET.CODE
    if "." not in code:
        # 根据代码前缀判断市场
        if code.startswith("0") or code.startswith("3"):
            code = f"SZ.{code}"
        elif code.startswith("6") or code.startswith("9"):
            code = f"SH.{code}"
    
    return code


def get_trading_dates(start_date: str, end_date: str) -> list:
    """
    获取交易日期列表（简化版，实际应该排除节假日）
    """
    start = str_to_datetime(start_date, "%Y-%m-%d")
    end = str_to_datetime(end_date, "%Y-%m-%d")
    
    dates = []
    current = start
    while current <= end:
        # 排除周末
        if current.weekday() < 5:
            dates.append(datetime_to_str(current, "%Y-%m-%d"))
        current += datetime.timedelta(days=1)
    
    return dates


def is_trading_time() -> bool:
    """
    判断当前是否为交易时间（简化版）
    """
    now = datetime.datetime.now(TZ)
    
    # 排除周末
    if now.weekday() >= 5:
        return False
    
    # 交易时间段
    morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
    morning_end = now.replace(hour=11, minute=30, second=0, microsecond=0)
    afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
    afternoon_end = now.replace(hour=15, minute=0, second=0, microsecond=0)
    
    return (morning_start <= now <= morning_end) or (afternoon_start <= now <= afternoon_end)


def log_execution_time(func):
    """
    记录函数执行时间的装饰器
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper
