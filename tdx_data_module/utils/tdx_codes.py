"""
通达信股票代码映射
包含各种特殊代码和错误代码的映射关系
"""

# 北交所股票代码映射（部分）
tdx_codes_by_bj = {
    "BJ.430017": "星昊医药",
    "BJ.430047": "诺思兰德", 
    "BJ.430090": "同辉信息",
    "BJ.430139": "华岭股份",
    "BJ.430198": "微创光电",
    "BJ.430300": "辰光医疗",
    "BJ.430418": "苏轴股份",
    "BJ.430425": "乐创技术",
    "BJ.430476": "海能技术",
    "BJ.430478": "峆一药业",
    "BJ.430489": "佳先股份",
    "BJ.430510": "丰光精密",
    "BJ.430556": "雅达股份",
    "BJ.430564": "天润科技",
    "BJ.430685": "新芝生物",
    "BJ.430718": "合肥高科",
    "BJ.830779": "武汉蓝电",
    "BJ.830799": "艾融软件",
    "BJ.830809": "安达科技",
    "BJ.830832": "齐鲁华信",
    "BJ.830839": "万通液压",
    "BJ.830879": "基康仪器",
    "BJ.830896": "旺成科技",
    "BJ.830946": "森萱医药",
    "BJ.830964": "润农节水",
    "BJ.830974": "凯大催化",
    "BJ.831010": "凯添燃气",
    "BJ.831039": "国义招标",
    "BJ.831087": "秋乐种业",
    "BJ.831152": "昆工科技",
    "BJ.831167": "鑫汇科",
    "BJ.831175": "派诺科技",
    "BJ.831195": "三祥科技",
    "BJ.831278": "泰德股份",
    "BJ.831304": "迪尔化工",
}

# 错误或无效的股票代码列表
tdx_codes_by_error = [
    "SZ.395001", "SZ.395002", "SZ.395004", "SZ.395005", "SZ.395006",
    "SZ.395011", "SZ.395012", "SZ.395013", "SZ.395015", "SZ.395032",
    "SZ.395033", "SZ.395034", "SZ.395041", "SZ.395099", "SZ.395000",
    "SZ.000584", "SZ.000622", "SZ.000627", "SZ.000695", "SZ.002128",
    "SZ.002336", "SZ.002750", "SZ.159210", "SZ.159218", "SZ.159226",
    "SZ.159230", "SZ.159233", "SZ.159239", "SZ.159241", "SZ.159311",
    "SZ.159372", "SZ.159721", "SZ.160137", "SZ.160220", "SZ.160418",
    "SZ.160419", "SZ.160420", "SZ.160425", "SZ.160516", "SZ.160517",
    "SZ.160518", "SZ.160527", "SZ.160603", "SZ.160605", "SZ.160616",
    "SZ.160618", "SZ.160620", "SZ.160624", "SZ.160627", "SZ.160634",
    "SZ.160636", "SZ.160646", "SZ.160718", "SZ.160722", "SZ.160727",
    "SZ.160806", "SZ.161022", "SH.519943", "SH.519947", "SH.519949",
    "SH.519951", "SH.519956", "SH.519957", "SH.519959", "SH.519965",
    "SH.519967", "SH.519971", "SH.519972", "SH.519973", "SH.519975",
    "SH.519976", "SH.519977", "SH.519979", "SH.519983", "SH.519985",
    "SH.519989", "SH.519991", "SH.519993", "SH.519995", "SH.519997",
    "BJ.430489", "BJ.830799", "BJ.831445", "BJ.833819", "BJ.834682",
    "BJ.839167",
]

# 市场代码映射
MARKET_CODE_MAP = {
    "SH": 1,  # 上海证券交易所
    "SZ": 0,  # 深圳证券交易所
    "BJ": 2,  # 北京证券交易所
}

# 通达信市场代码到交易所的映射
TDX_MARKET_MAP = {
    0: "SZ",  # 深圳
    1: "SH",  # 上海
    2: "BJ",  # 北京
}

# 股票类型判断
def get_stock_type(code: str) -> str:
    """
    根据股票代码判断股票类型
    
    Args:
        code: 股票代码，格式如 SZ.000001
        
    Returns:
        str: 股票类型
    """
    if not code or "." not in code:
        return "unknown"
    
    market, stock_code = code.split(".", 1)
    
    if market == "SH":
        if stock_code.startswith("6"):
            return "stock"  # 主板股票
        elif stock_code.startswith("688"):
            return "stock_kcb"  # 科创板
        elif stock_code.startswith("000") or stock_code.startswith("880"):
            return "index"  # 指数
        elif stock_code.startswith("5"):
            return "fund"  # 基金
        else:
            return "other"
    
    elif market == "SZ":
        if stock_code.startswith("000") or stock_code.startswith("001"):
            return "stock"  # 主板股票
        elif stock_code.startswith("002"):
            return "stock"  # 中小板（已并入主板）
        elif stock_code.startswith("300"):
            return "stock_cyb"  # 创业板
        elif stock_code.startswith("399"):
            return "index"  # 指数
        elif stock_code.startswith("1"):
            return "fund"  # 基金/ETF
        else:
            return "other"
    
    elif market == "BJ":
        return "stock_bj"  # 北交所股票
    
    return "unknown"


def is_valid_code(code: str) -> bool:
    """
    检查股票代码是否有效
    
    Args:
        code: 股票代码
        
    Returns:
        bool: 是否有效
    """
    if not code:
        return False
    
    # 检查是否在错误代码列表中
    if code in tdx_codes_by_error:
        return False
    
    # 基本格式检查
    if "." not in code:
        return False
    
    market, stock_code = code.split(".", 1)
    
    # 检查市场代码
    if market not in ["SH", "SZ", "BJ", "HK", "US"]:
        return False
    
    # 检查股票代码长度
    if len(stock_code) < 4:
        return False
    
    return True


def format_tdx_code(code: str) -> str:
    """
    格式化股票代码为通达信格式
    
    Args:
        code: 原始股票代码
        
    Returns:
        str: 格式化后的代码
    """
    if not code:
        return ""
    
    code = code.strip().upper()
    
    # 如果已经是正确格式，直接返回
    if "." in code and len(code.split(".")) == 2:
        market, stock_code = code.split(".")
        if market in ["SH", "SZ", "BJ", "HK", "US"]:
            return code
    
    # 根据代码前缀判断市场
    if code.startswith("0") or code.startswith("3"):
        return f"SZ.{code}"
    elif code.startswith("6") or code.startswith("9"):
        return f"SH.{code}"
    elif code.startswith("8") or code.startswith("4"):
        return f"BJ.{code}"
    
    return code


def get_market_from_code(code: str) -> str:
    """
    从股票代码获取市场代码
    
    Args:
        code: 股票代码
        
    Returns:
        str: 市场代码
    """
    if not code or "." not in code:
        return ""
    
    return code.split(".")[0]


def get_stock_code_from_full(code: str) -> str:
    """
    从完整代码获取股票代码部分
    
    Args:
        code: 完整股票代码，如 SZ.000001
        
    Returns:
        str: 股票代码部分，如 000001
    """
    if not code or "." not in code:
        return code
    
    return code.split(".", 1)[1]


# 常用股票代码示例
SAMPLE_CODES = {
    "A股": [
        "SZ.000001",  # 平安银行
        "SZ.000002",  # 万科A
        "SH.600000",  # 浦发银行
        "SH.600036",  # 招商银行
        "SZ.300015",  # 爱尔眼科
        "SH.688981",  # 中芯国际
    ],
    "港股": [
        "HK.00700",   # 腾讯控股
        "HK.09988",   # 阿里巴巴
        "HK.03690",   # 美团
    ],
    "美股": [
        "US.AAPL",    # 苹果
        "US.MSFT",    # 微软
        "US.GOOGL",   # 谷歌
    ]
}


if __name__ == "__main__":
    # 测试代码
    test_codes = ["000001", "SZ.000001", "600000", "SH.600000", "invalid"]
    
    for code in test_codes:
        formatted = format_tdx_code(code)
        is_valid = is_valid_code(formatted)
        stock_type = get_stock_type(formatted)
        
        print(f"原始代码: {code}")
        print(f"格式化后: {formatted}")
        print(f"是否有效: {is_valid}")
        print(f"股票类型: {stock_type}")
        print("-" * 30)
