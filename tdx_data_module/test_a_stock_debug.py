#!/usr/bin/env python3
"""
A股数据调试程序
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX


def debug_a_stock_data():
    """调试A股数据获取"""
    print("A股数据调试程序")
    print("=" * 40)
    
    try:
        # 创建A股交易所实例
        tdx = ExchangeTDX()
        
        # 获取A股列表
        stocks = tdx.all_stocks()
        print(f"获取到 {len(stocks)} 只A股")
        
        # 显示前10只A股
        print("前10只A股:")
        for i, stock in enumerate(stocks[:10]):
            print(f"  {i+1}. {stock['code']} - {stock['name']} ({stock.get('type', 'unknown')})")
        
        # 测试几个不同类型的股票
        test_codes = [
            "SZ.000001",  # 平安银行 - 主板
            "SZ.300015",  # 爱尔眼科 - 创业板
            "SH.688981",  # 中芯国际 - 科创板（如果有）
            "SH.000001",  # 上证指数
        ]
        
        for test_code in test_codes:
            print(f"\n测试 {test_code}:")
            
            # 测试代码映射
            market, tdx_code, stock_type = tdx.to_tdx_code(test_code)
            print(f"  代码映射: {test_code} -> 市场: {market}, 代码: {tdx_code}, 类型: {stock_type}")
            
            # 测试K线数据
            try:
                klines = tdx.klines(test_code, "d")
                if klines is not None and len(klines) > 0:
                    print(f"  ✓ 成功获取 {len(klines)} 条K线数据")
                    latest = klines.iloc[-1]
                    print(f"  最新数据: {latest['date']} - 收盘价: {latest['close']}")
                    
                    # 检查数据完整性
                    required_columns = ['code', 'date', 'open', 'close', 'high', 'low', 'volume']
                    missing_columns = [col for col in required_columns if col not in klines.columns]
                    if missing_columns:
                        print(f"  ⚠️ 缺少列: {missing_columns}")
                    else:
                        print(f"  ✓ 数据完整")
                else:
                    print(f"  ✗ K线数据获取失败")
            except Exception as e:
                print(f"  ✗ K线数据获取异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试实时行情
            try:
                ticks = tdx.ticks([test_code])
                if ticks and len(ticks) > 0:
                    print(f"  ✓ 成功获取实时行情")
                    for code, tick in ticks.items():
                        print(f"    {code}: 最新价 {tick.last}, 涨跌幅 {tick.rate}%")
                else:
                    print(f"  ✗ 实时行情获取失败")
            except Exception as e:
                print(f"  ✗ 实时行情获取异常: {e}")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_a_stock_data()
