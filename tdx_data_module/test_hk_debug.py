#!/usr/bin/env python3
"""
港股数据调试程序
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx_hk import ExchangeTDXHK


def debug_hk_data():
    """调试港股数据获取"""
    print("港股数据调试程序")
    print("=" * 40)
    
    try:
        # 创建港股交易所实例
        hk_exchange = ExchangeTDXHK()
        
        # 获取港股列表
        stocks = hk_exchange.all_stocks()
        print(f"获取到 {len(stocks)} 只港股")
        
        # 显示前10只港股
        print("前10只港股:")
        for i, stock in enumerate(stocks[:10]):
            print(f"  {i+1}. {stock['code']} - {stock['name']}")
        
        # 测试几个知名港股
        test_codes = []
        if stocks:
            # 使用实际获取到的港股代码
            test_codes = [stocks[i]['code'] for i in range(min(5, len(stocks)))]
        
        # 如果没有获取到，使用默认的
        if not test_codes:
            test_codes = ["KH.00700", "KH.09988", "KH.03690"]
        
        for test_code in test_codes:
            print(f"\n测试 {test_code}:")
            
            # 测试代码映射
            market, tdx_code = hk_exchange.to_tdx_code(test_code)
            print(f"  代码映射: {test_code} -> 市场: {market}, 代码: {tdx_code}")
            
            # 测试K线数据
            try:
                klines = hk_exchange.klines(test_code, "d")
                if klines is not None and len(klines) > 0:
                    print(f"  ✓ 成功获取 {len(klines)} 条K线数据")
                    latest = klines.iloc[-1]
                    print(f"  最新数据: {latest['date']} - 收盘价: {latest['close']}")
                    
                    # 检查数据完整性
                    required_columns = ['code', 'date', 'open', 'close', 'high', 'low', 'volume']
                    missing_columns = [col for col in required_columns if col not in klines.columns]
                    if missing_columns:
                        print(f"  ⚠️ 缺少列: {missing_columns}")
                    else:
                        print(f"  ✓ 数据完整")
                else:
                    print(f"  ✗ K线数据获取失败")
            except Exception as e:
                print(f"  ✗ K线数据获取异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试实时行情
            try:
                ticks = hk_exchange.ticks([test_code])
                if ticks and len(ticks) > 0:
                    print(f"  ✓ 成功获取实时行情")
                    for code, tick in ticks.items():
                        print(f"    {code}: 最新价 {tick.last}, 涨跌幅 {tick.rate}%")
                else:
                    print(f"  ✗ 实时行情获取失败")
            except Exception as e:
                print(f"  ✗ 实时行情获取异常: {e}")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_hk_data()
