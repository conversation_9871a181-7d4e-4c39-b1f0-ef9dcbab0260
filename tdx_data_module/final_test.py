#!/usr/bin/env python3
"""
最终完整测试程序
验证所有市场的完整功能
"""

import sys
import os
import traceback
import time
import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.exchange_tdx import ExchangeTDX
from core.exchange_tdx_hk import ExchangeTDXHK
from core.exchange_tdx_us import ExchangeTDXUS
from core.exchange_tdx_futures import ExchangeTDXFutures
from core.exchange_tdx_fx import ExchangeTDXFX


class FinalTester:
    """最终测试器"""
    
    def __init__(self):
        self.results = {}
        self.total_tests = 0
        self.passed_tests = 0
    
    def test_market(self, name, exchange_class, description):
        """测试单个市场"""
        print(f"\n{'='*60}")
        print(f"测试 {name} ({description})")
        print(f"{'='*60}")
        
        market_results = {
            'connection': False,
            'stock_list': False,
            'klines': False,
            'ticks': False,
            'details': {}
        }
        
        try:
            # 1. 测试连接
            print(f"1. 创建{name}交易所实例...")
            exchange = exchange_class()
            market_results['connection'] = True
            print(f"   ✓ 连接成功")
            
            # 2. 测试股票列表
            print(f"2. 获取{name}标的列表...")
            stocks = exchange.all_stocks()
            if stocks and len(stocks) > 0:
                market_results['stock_list'] = True
                market_results['details']['stock_count'] = len(stocks)
                print(f"   ✓ 成功获取 {len(stocks)} 个标的")
                
                # 显示前5个标的
                print("   前5个标的:")
                for i, stock in enumerate(stocks[:5]):
                    print(f"     {i+1}. {stock['code']} - {stock['name']}")
            else:
                print(f"   ✗ 获取{name}标的列表失败")
            
            # 3. 测试K线数据
            print(f"3. 测试{name}K线数据...")
            if stocks and len(stocks) > 0:
                # 选择测试代码
                test_codes = [stocks[i]['code'] for i in range(min(3, len(stocks)))]
                success_count = 0
                
                for code in test_codes:
                    try:
                        klines = exchange.klines(code, "d")
                        if klines is not None and len(klines) > 0:
                            success_count += 1
                            print(f"     ✓ {code}: {len(klines)} 条数据")
                            
                            # 记录最新数据
                            latest = klines.iloc[-1]
                            market_results['details'][f'{code}_latest'] = {
                                'date': str(latest['date']),
                                'close': float(latest['close'])
                            }
                        else:
                            print(f"     ✗ {code}: 获取失败")
                    except Exception as e:
                        print(f"     ✗ {code}: 异常 {str(e)[:50]}...")
                
                if success_count > 0:
                    market_results['klines'] = True
                    market_results['details']['klines_success_rate'] = f"{success_count}/{len(test_codes)}"
                
                print(f"   K线测试结果: {success_count}/{len(test_codes)} 成功")
            
            # 4. 测试实时行情
            print(f"4. 测试{name}实时行情...")
            if stocks and len(stocks) > 0:
                test_codes = [stocks[i]['code'] for i in range(min(3, len(stocks)))]
                try:
                    ticks = exchange.ticks(test_codes)
                    if ticks and len(ticks) > 0:
                        market_results['ticks'] = True
                        market_results['details']['ticks_count'] = len(ticks)
                        print(f"   ✓ 成功获取 {len(ticks)} 个标的实时行情")
                        
                        # 显示实时行情
                        for code, tick in list(ticks.items())[:3]:
                            print(f"     {code}: 最新价 {tick.last:.2f}, 涨跌幅 {tick.rate:.2f}%")
                    else:
                        print(f"   ✗ 实时行情获取失败")
                except Exception as e:
                    print(f"   ✗ 实时行情异常: {str(e)[:50]}...")
            
        except Exception as e:
            print(f"   ✗ {name}测试异常: {e}")
            traceback.print_exc()
        
        # 计算成功率
        test_items = ['connection', 'stock_list', 'klines', 'ticks']
        success_count = sum(1 for item in test_items if market_results[item])
        success_rate = success_count / len(test_items) * 100
        
        market_results['success_rate'] = success_rate
        self.results[name] = market_results
        
        # 更新总体统计
        self.total_tests += len(test_items)
        self.passed_tests += success_count
        
        print(f"\n{name}测试完成，成功率: {success_rate:.1f}% ({success_count}/{len(test_items)})")
        
        return market_results
    
    def run_all_tests(self):
        """运行所有测试"""
        print("通达信数据模块最终完整测试")
        print(f"测试开始时间: {datetime.datetime.now()}")
        
        # 定义所有市场
        markets = [
            ("A股", ExchangeTDX, "中国A股市场"),
            ("港股", ExchangeTDXHK, "香港股票市场"),
            ("美股", ExchangeTDXUS, "美国股票市场"),
            ("期货", ExchangeTDXFutures, "中国期货市场"),
            ("外汇", ExchangeTDXFX, "外汇市场"),
        ]
        
        # 测试所有市场
        for name, exchange_class, description in markets:
            self.test_market(name, exchange_class, description)
        
        # 输出最终结果
        self.print_final_results()
    
    def print_final_results(self):
        """输出最终测试结果"""
        print(f"\n{'='*80}")
        print("最终测试结果汇总")
        print(f"{'='*80}")
        
        # 总体成功率
        overall_rate = self.passed_tests / self.total_tests * 100 if self.total_tests > 0 else 0
        print(f"总体成功率: {self.passed_tests}/{self.total_tests} ({overall_rate:.1f}%)")
        
        # 详细结果表格
        print(f"\n{'市场':<8} {'连接':<6} {'列表':<6} {'K线':<6} {'行情':<6} {'成功率':<8} {'状态':<10}")
        print("-" * 70)
        
        fully_working = []
        partially_working = []
        not_working = []
        
        for market_name, results in self.results.items():
            connection = "✓" if results['connection'] else "✗"
            stock_list = "✓" if results['stock_list'] else "✗"
            klines = "✓" if results['klines'] else "✗"
            ticks = "✓" if results['ticks'] else "✗"
            success_rate = results['success_rate']
            
            if success_rate == 100:
                status = "🟢 完全可用"
                fully_working.append(market_name)
            elif success_rate >= 50:
                status = "🟡 部分可用"
                partially_working.append(market_name)
            else:
                status = "🔴 不可用"
                not_working.append(market_name)
            
            print(f"{market_name:<8} {connection:<6} {stock_list:<6} {klines:<6} {ticks:<6} {success_rate:<8.1f}% {status:<10}")
        
        # 功能分析
        print(f"\n功能分析:")
        print(f"完全可用市场: {', '.join(fully_working) if fully_working else '无'}")
        print(f"部分可用市场: {', '.join(partially_working) if partially_working else '无'}")
        print(f"不可用市场: {', '.join(not_working) if not_working else '无'}")
        
        # 数据统计
        print(f"\n数据统计:")
        for market_name, results in self.results.items():
            details = results.get('details', {})
            if 'stock_count' in details:
                print(f"{market_name}: {details['stock_count']} 个标的")
        
        # 使用建议
        print(f"\n使用建议:")
        if len(fully_working) >= 2:
            print("✅ 多个市场完全可用，模块功能完整，推荐生产使用")
        elif len(fully_working) >= 1:
            print("✅ 核心市场可用，可以满足主要数据需求")
        elif len(partially_working) >= 2:
            print("⚠️ 部分功能可用，建议针对性使用")
        else:
            print("❌ 功能不完整，需要进一步调试")
        
        # 最终评价
        if overall_rate >= 90:
            final_grade = "A+ 优秀"
        elif overall_rate >= 80:
            final_grade = "A 良好"
        elif overall_rate >= 70:
            final_grade = "B 合格"
        elif overall_rate >= 60:
            final_grade = "C 基本可用"
        else:
            final_grade = "D 需要改进"
        
        print(f"\n最终评价: {final_grade}")
        print(f"测试结束时间: {datetime.datetime.now()}")


def main():
    """主函数"""
    tester = FinalTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
