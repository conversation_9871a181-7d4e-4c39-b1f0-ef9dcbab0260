# Chanlun-PRO 数据库配置说明

## 📋 概述

本文档记录了Chanlun-PRO项目的数据库配置信息，包括本地数据库和NAS远程数据库的详细配置。

---

## 🖥️ 当前主数据库配置（NAS）

### QNAP NAS MariaDB（主数据源）
```
数据库类型: MariaDB 10.5.8
主机地址: ***********
端口: 3307
用户名: chanlun
密码: 123@Baooo
数据库名: chanlun
字符集: utf8mb4
存储引擎: MyISAM (默认)
```

### 配置文件位置
- **配置文件**: `src/chanlun/config.py`
- **主数据库配置变量**:
  ```python
  DB_TYPE = "mysql"
  DB_HOST = '***********'    # NAS数据库地址
  DB_PORT = 3307             # NAS数据库端口
  DB_USER = 'chanlun'        # NAS数据库用户
  DB_PWD = '123@Baooo'       # NAS数据库密码
  DB_DATABASE = 'chanlun'    # NAS数据库名
  ```

## 🖥️ 本地数据库配置（备份）

### MySQL 本地实例（备份配置）
```
数据库类型: MySQL
主机地址: 127.0.0.1
端口: 3306
用户名: root
密码: 123456
数据库名: chanlun
字符集: utf8mb4
```

### 备份配置变量
- **配置文件**: `src/chanlun/config.py`
- **备份配置变量**:
  ```python
  LOCAL_DB_HOST = '127.0.0.1'
  LOCAL_DB_PORT = 3306
  LOCAL_DB_USER = 'root'
  LOCAL_DB_PWD = '123456'
  LOCAL_DB_DATABASE = 'chanlun'
  ```

---

## 🏠 NAS远程数据库配置

### QNAP NAS MariaDB
```
数据库类型: MariaDB 10.5.8
主机地址: ***********
端口: 3307 (非标准端口)
用户名: chanlun
密码: 123@Baooo
数据库名: chanlun
字符集: utf8mb4
存储引擎: MyISAM (默认)
```

### 配置文件位置
- **配置文件**: `src/chanlun/config.py`
- **配置变量**:
  ```python
  NAS_DB_HOST = '***********'
  NAS_DB_PORT = 3307
  NAS_DB_USER = 'chanlun'
  NAS_DB_PWD = '123@Baooo'
  NAS_DB_DATABASE = 'chanlun'
  ```

### NAS数据库特殊配置

#### ⚠️ 重要注意事项
- **存储引擎**: 必须使用 `MyISAM`，不能使用 `InnoDB`
- **原因**: QNAP NAS的MariaDB使用InnoDB会导致数据库无法访问和数据损坏
- **参考**: [QNAP MariaDB配置最佳实践](https://sanguok.com/blog/dont-enable-innodb-for-qnap-nas-mariadb10/)

#### 服务器配置文件
- **配置文件**: `/mnt/ext/opt/mariadb10/etc/mariadb.conf`
- **数据目录**: `/share/ZFS1_DATA/.mariadb10/`
- **Socket文件**: `/var/run/mariadb10.sock`
- **客户端**: `/mnt/ext/opt/mariadb/bin/mysql`

---

## 🔗 连接方式

### 本地数据库连接
```bash
# 命令行连接
mysql -h 127.0.0.1 -P 3306 -u root -p123456 chanlun

# Python连接示例
import pymysql
connection = pymysql.connect(
    host='127.0.0.1',
    port=3306,
    user='root',
    password='123456',
    database='chanlun',
    charset='utf8mb4'
)
```

### NAS数据库连接
```bash
# 从NAS本地连接 (Socket方式)
/mnt/ext/opt/mariadb/bin/mysql --socket=/var/run/mariadb10.sock -u root -p

# 远程网络连接
mysql -h *********** -P 3307 -u chanlun -p'123@Baooo' chanlun

# Python连接示例
import pymysql
from urllib.parse import quote_plus
connection = pymysql.connect(
    host='***********',
    port=3307,
    user='chanlun',
    password='123@Baooo',
    database='chanlun',
    charset='utf8mb4'
)

# SQLAlchemy连接 (注意密码URL编码)
from sqlalchemy import create_engine
nas_url = f"mysql+pymysql://chanlun:{quote_plus('123@Baooo')}@***********:3307/chanlun?charset=utf8mb4"
engine = create_engine(nas_url)
```

---

## 📊 数据库用途

### NAS数据库（主数据源）
- **用途**: 生产环境和主数据存储
- **数据**: 完整的股票历史数据（日线+周线）
- **性能**: 网络访问，适合大数据存储
- **备份**: 自动备份到NAS存储
- **状态**: ✅ **当前框架默认数据源**
- **优势**:
  - 大容量存储
  - 网络共享访问
  - 数据安全性高
  - 支持远程访问
  - 所有回测数据从此获取

### 本地数据库（备份）
- **用途**: 开发和测试环境（备份配置）
- **数据**: 历史股票数据（用于开发测试）
- **性能**: 高速本地访问
- **备份**: 手动备份
- **状态**: 🔄 备份配置，可用于开发测试

---

## 🛠️ 相关工具和脚本

### 数据库测试工具
```bash
# 测试NAS数据库配置和连接
python test_nas_migration_config.py

# 快速验证数据迁移功能
python quick_migration_test.py

# 测试NAS数据下载功能
python test_nas_download.py
```

### 数据迁移工具
```bash
# 完整数据迁移 (本地 -> NAS)
python data_migration_to_nas.py

# 高性能数据迁移
python fast_migration_to_nas.py
```

### 股票数据下载
```bash
# 直接下载股票数据到NAS (推荐)
python sync_tushare_to_nas.py

# 原版本下载到本地
python chanlun-pro/script/crontab/reboot_sync_tushare_a_klines_v2.py
```

---

## 🔐 安全注意事项

### 密码安全
- **本地数据库**: 使用简单密码 `123456` (仅限开发环境)
- **NAS数据库**: 使用复杂密码 `123@Baooo` (符合强度要求)
- **建议**: 定期更换密码，特别是生产环境

### 网络安全
- **NAS访问**: 仅限内网访问 (192.168.1.x)
- **防火墙**: 确保3307端口仅对内网开放
- **用户权限**: `chanlun` 用户具有完整数据库权限

### 数据备份
- **本地数据**: 定期导出重要数据
- **NAS数据**: 利用QNAP的自动备份功能
- **建议**: 设置定期备份计划

---

## 📈 性能优化

### 本地数据库优化
- **存储引擎**: InnoDB (支持事务)
- **索引优化**: 根据查询模式创建合适索引
- **内存配置**: 根据可用内存调整缓冲区大小

### NAS数据库优化
- **存储引擎**: MyISAM (必须，QNAP兼容性)
- **网络优化**: 使用连接池减少连接开销
- **批量操作**: 使用批量插入提高写入性能
- **索引策略**: 合理创建索引，避免过多索引影响写入性能

---

## 🚨 故障排除

### 常见问题

#### 1. NAS数据库连接失败
```bash
# 检查网络连通性
ping ***********

# 检查端口是否开放
telnet *********** 3307

# 检查MariaDB服务状态
ssh admin@*********** "ps aux | grep mariadb"
```

#### 2. 密码认证失败
- 确认密码中的特殊字符 `@` 是否正确处理
- 使用URL编码: `quote_plus('123@Baooo')`
- 检查用户权限: `SELECT User, Host FROM mysql.user;`

#### 3. 存储引擎问题
- 确认使用MyISAM: `SHOW ENGINES;`
- 检查表引擎: `SHOW CREATE TABLE table_name;`
- 修改引擎: `ALTER TABLE table_name ENGINE=MyISAM;`

### 联系信息
- **项目**: Chanlun-PRO
- **文档更新**: 2025-07-05
- **维护**: 定期更新配置和密码信息

---

## 📝 更新日志

### 2025-07-05
- ✅ 初始化数据库配置文档
- ✅ 添加本地和NAS数据库配置
- ✅ 创建连接示例和工具说明
- ✅ 添加安全和性能优化建议
- ✅ 完善故障排除指南
- ✅ **框架数据源切换到NAS**
- ✅ 修复DB类密码URL编码问题
- ✅ 验证框架NAS连接功能
- ✅ 更新配置文档反映当前状态
