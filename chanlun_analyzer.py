#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缠论分析器
集成chanlun-pro框架进行5分钟笔分析
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加chanlun-pro路径
sys.path.append(r'D:\git\chan-pro\chanlun-pro\src')

try:
    from chanlun import cl
    from chanlun.cl_interface import ICL, BI, FX
    CHANLUN_AVAILABLE = True
    print("✅ chanlun-pro 框架加载成功")
except ImportError as e:
    print(f"⚠️ chanlun-pro 框架加载失败: {e}")
    print("将使用简化的笔分析逻辑")
    CHANLUN_AVAILABLE = False

class SimpleBiAnalyzer:
    """简化的笔分析器（当chanlun-pro不可用时使用）"""
    
    def __init__(self):
        self.bis = []
        self.fxs = []
    
    def process_klines(self, df: pd.DataFrame):
        """处理K线数据，识别简单的笔"""
        if len(df) < 10:
            return
        
        # 简化的分型识别
        self.fxs = self._find_simple_fractals(df)
        
        # 简化的笔识别
        self.bis = self._find_simple_bis(df, self.fxs)
    
    def _find_simple_fractals(self, df: pd.DataFrame) -> List[Dict]:
        """简化的分型识别"""
        fractals = []
        
        for i in range(1, len(df) - 1):
            current = df.iloc[i]
            prev = df.iloc[i-1]
            next_k = df.iloc[i+1]
            
            # 顶分型：中间K线最高价是三根K线中最高的
            if (current['high'] >= prev['high'] and 
                current['high'] >= next_k['high'] and
                current['high'] > min(prev['high'], next_k['high'])):
                
                fractals.append({
                    'index': i,
                    'type': 'ding',
                    'value': current['high'],
                    'datetime': current.name,
                    'done': True
                })
            
            # 底分型：中间K线最低价是三根K线中最低的
            elif (current['low'] <= prev['low'] and 
                  current['low'] <= next_k['low'] and
                  current['low'] < max(prev['low'], next_k['low'])):
                
                fractals.append({
                    'index': i,
                    'type': 'di',
                    'value': current['low'],
                    'datetime': current.name,
                    'done': True
                })
        
        return fractals
    
    def _find_simple_bis(self, df: pd.DataFrame, fractals: List[Dict]) -> List[Dict]:
        """简化的笔识别"""
        if len(fractals) < 2:
            return []
        
        bis = []
        
        for i in range(len(fractals) - 1):
            start_fx = fractals[i]
            end_fx = fractals[i + 1]
            
            # 确保分型类型不同
            if start_fx['type'] != end_fx['type']:
                bi_type = "up" if start_fx['type'] == 'di' else "down"
                
                bis.append({
                    'index': len(bis),
                    'type': bi_type,
                    'start': start_fx,
                    'end': end_fx,
                    'high': max(start_fx['value'], end_fx['value']),
                    'low': min(start_fx['value'], end_fx['value']),
                    'done': end_fx['done']
                })
        
        return bis
    
    def get_bis(self) -> List[Dict]:
        """获取笔列表"""
        return self.bis
    
    def get_fxs(self) -> List[Dict]:
        """获取分型列表"""
        return self.fxs

class ChanLunAnalyzer:
    """缠论分析器主类"""
    
    def __init__(self, use_simple_analyzer: bool = False):
        self.use_simple_analyzer = use_simple_analyzer or not CHANLUN_AVAILABLE
        
        if self.use_simple_analyzer:
            print("使用简化笔分析器")
            self.analyzer = SimpleBiAnalyzer()
        else:
            print("使用chanlun-pro框架")
            self.analyzer = None
    
    def analyze_5min_data(self, df_5min: pd.DataFrame, code: str = "temp") -> Dict:
        """分析5分钟数据"""
        if df_5min is None or len(df_5min) < 10:
            return {
                'bis': [],
                'fxs': [],
                'latest_bi': None,
                'signals': []
            }
        
        try:
            if self.use_simple_analyzer:
                return self._analyze_with_simple(df_5min)
            else:
                return self._analyze_with_chanlun(df_5min, code)
        except Exception as e:
            print(f"⚠️ 缠论分析失败: {e}")
            return {
                'bis': [],
                'fxs': [],
                'latest_bi': None,
                'signals': []
            }
    
    def _analyze_with_simple(self, df_5min: pd.DataFrame) -> Dict:
        """使用简化分析器"""
        self.analyzer.process_klines(df_5min)
        
        bis = self.analyzer.get_bis()
        fxs = self.analyzer.get_fxs()
        
        latest_bi = bis[-1] if bis else None
        signals = self._generate_simple_signals(bis, df_5min)
        
        return {
            'bis': bis,
            'fxs': fxs,
            'latest_bi': latest_bi,
            'signals': signals
        }
    
    def _analyze_with_chanlun(self, df_5min: pd.DataFrame, code: str) -> Dict:
        """使用chanlun-pro框架"""
        # 转换数据格式
        klines = self._convert_to_chanlun_format(df_5min)
        
        # 创建缠论分析对象
        config = {
            'fx_qj': 'k',
            'fx_qy': 'h',
            'bi_type': 'new',
            'bi_bzh': 'no',
            'xd_bzh': 'no',
            'zs_bi_type': 'bi',
            'zs_xd_type': 'xd',
            'zs_qj': 'zg',
            'zs_wzgx': 'zf'
        }
        
        cd = cl.CL(code=code, frequency="5m", config=config)
        cd.process_klines(klines)
        
        # 获取分析结果
        bis = cd.get_bis()
        fxs = cd.get_fxs()
        
        latest_bi = bis[-1] if bis else None
        signals = self._generate_chanlun_signals(latest_bi, cd)
        
        return {
            'bis': bis,
            'fxs': fxs,
            'latest_bi': latest_bi,
            'signals': signals,
            'cd': cd  # 保存缠论对象供后续使用
        }
    
    def _convert_to_chanlun_format(self, df: pd.DataFrame):
        """转换为chanlun-pro格式"""
        klines = []

        # 确保df是DataFrame
        if not isinstance(df, pd.DataFrame):
            print(f"⚠️ 数据类型错误: {type(df)}, 期望DataFrame")
            return klines

        # 重置索引确保可以正常迭代
        df_reset = df.reset_index()

        for i, row in df_reset.iterrows():
            try:
                # 创建K线对象
                kline = {
                    'k_index': i,
                    'date': row.get('datetime', row.get('dt', row.get('index', None))),
                    'o': float(row['open']),
                    'h': float(row['high']),
                    'l': float(row['low']),
                    'c': float(row['close']),
                    'v': float(row.get('volume', 0))
                }
                klines.append(kline)
            except Exception as e:
                print(f"⚠️ 转换K线数据失败: {e}, 行数据: {row}")
                continue

        return klines
    
    def _generate_simple_signals(self, bis: List[Dict], df_5min: pd.DataFrame) -> List[Dict]:
        """生成简化的交易信号"""
        signals = []
        
        if not bis:
            return signals
        
        latest_bi = bis[-1]
        current_price = df_5min['close'].iloc[-1]
        
        # 检查笔结束信号
        if latest_bi['done'] and latest_bi['type'] == 'down':
            # 向下笔结束，可能反弹
            if current_price <= latest_bi['low'] * 1.02:  # 价格接近笔低点
                signals.append({
                    'type': 'bi_reversal',
                    'direction': 'up',
                    'strength': 0.6,
                    'reason': '向下笔结束，价格接近低点',
                    'price': current_price,
                    'bi_low': latest_bi['low']
                })
        
        return signals
    
    def _generate_chanlun_signals(self, latest_bi, cd) -> List[Dict]:
        """生成基于chanlun-pro的交易信号"""
        signals = []
        
        if latest_bi is None:
            return signals
        
        try:
            # 检查笔是否完成
            if not latest_bi.is_done():
                return signals
            
            # 只关注向下笔
            if latest_bi.type != "down":
                return signals
            
            signal_strength = 0.5
            reasons = []
            
            # 检查背驰
            if hasattr(latest_bi, 'bc_exists'):
                if latest_bi.bc_exists(["pz", "qs"]):
                    signal_strength += 0.3
                    reasons.append("存在背驰")
            
            # 检查买卖点
            if hasattr(latest_bi, 'mmd_exists'):
                if latest_bi.mmd_exists(["1buy", "2buy", "3buy"]):
                    signal_strength += 0.2
                    reasons.append("存在买点")
            
            # 检查笔停顿
            if self._check_bi_pause(latest_bi, cd):
                signal_strength += 0.2
                reasons.append("笔停顿")
            
            # 生成信号
            if signal_strength >= 0.7:
                klines = cd.get_klines()
                current_price = klines[-1].c if klines else 0
                
                signals.append({
                    'type': 'bi_reversal',
                    'direction': 'up',
                    'strength': signal_strength,
                    'reason': '; '.join(reasons),
                    'price': current_price,
                    'bi_low': latest_bi.low
                })
        
        except Exception as e:
            print(f"⚠️ 生成缠论信号时出错: {e}")
        
        return signals
    
    def _check_bi_pause(self, bi, cd) -> bool:
        """检查笔停顿"""
        try:
            if not bi.is_done():
                return False
            
            klines = cd.get_klines()
            if not klines:
                return False
            
            # 获取笔结束后的K线
            bi_end_index = bi.end.klines[-1].k_index if hasattr(bi.end, 'klines') else -1
            next_klines = [k for k in klines if k.k_index > bi_end_index]
            
            if not next_klines:
                return False
            
            # 检查反向K线
            for k in next_klines:
                if bi.type == "up" and k.c < k.o and k.c < bi.end.val:
                    return True
                elif bi.type == "down" and k.c > k.o and k.c > bi.end.val:
                    return True
            
            return False
        except:
            return False
    
    def check_bi_end_conditions(self, analysis_result: Dict) -> Dict:
        """检查笔结束条件"""
        latest_bi = analysis_result.get('latest_bi')
        if not latest_bi:
            return {'is_end': False, 'confidence': 0.0, 'reasons': []}
        
        confidence = 0.0
        reasons = []
        
        # 基础条件：笔必须完成
        if self.use_simple_analyzer:
            if latest_bi.get('done', False):
                confidence += 0.3
                reasons.append("笔已完成")
        else:
            if hasattr(latest_bi, 'is_done') and latest_bi.is_done():
                confidence += 0.3
                reasons.append("笔已完成")
        
        # 检查信号强度
        signals = analysis_result.get('signals', [])
        if signals:
            max_strength = max(s.get('strength', 0) for s in signals)
            confidence += max_strength * 0.7
            reasons.extend([s.get('reason', '') for s in signals])
        
        return {
            'is_end': confidence >= 0.6,
            'confidence': confidence,
            'reasons': reasons
        }

def test_chanlun_analyzer():
    """测试缠论分析器"""
    print("🧪 测试缠论分析器")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='5T')
    np.random.seed(42)
    
    # 生成模拟的5分钟K线数据
    base_price = 10.0
    prices = [base_price]
    
    for i in range(99):
        change = np.random.normal(0, 0.02)  # 2%的标准差
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 确保价格为正
    
    test_data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        
        test_data.append({
            'datetime': date,
            'open': open_price,
            'high': max(open_price, high, close_price),
            'low': min(open_price, low, close_price),
            'close': close_price,
            'volume': np.random.randint(1000, 10000)
        })
    
    df_test = pd.DataFrame(test_data)
    df_test = df_test.set_index('datetime')
    
    print(f"📊 测试数据: {len(df_test)} 根5分钟K线")
    print(f"价格范围: {df_test['close'].min():.2f} - {df_test['close'].max():.2f}")
    
    # 测试分析器
    analyzer = ChanLunAnalyzer()
    result = analyzer.analyze_5min_data(df_test, "TEST")
    
    print(f"📈 分析结果:")
    print(f"  分型数量: {len(result['fxs'])}")
    print(f"  笔数量: {len(result['bis'])}")
    print(f"  信号数量: {len(result['signals'])}")
    
    if result['latest_bi']:
        print(f"  最新笔类型: {result['latest_bi'].get('type', 'unknown')}")
    
    if result['signals']:
        for signal in result['signals']:
            print(f"  信号: {signal['type']} - {signal['reason']} (强度: {signal['strength']:.2f})")
    
    # 测试笔结束条件
    end_check = analyzer.check_bi_end_conditions(result)
    print(f"📊 笔结束检查:")
    print(f"  是否结束: {end_check['is_end']}")
    print(f"  置信度: {end_check['confidence']:.2f}")
    print(f"  原因: {'; '.join(end_check['reasons'])}")
    
    print("✅ 缠论分析器测试完成")

if __name__ == "__main__":
    test_chanlun_analyzer()
