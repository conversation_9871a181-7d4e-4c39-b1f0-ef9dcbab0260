#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
V2版本：超跌股票反弹策略回测引擎（严格防止数据泄露）
策略逻辑：
1. 日线级别出现一买信号
2. 下跌笔确认结束（底分型确认或验证底分型）
3. 买入，止损为下跌笔最低点，止盈为止损的2倍

⚠️ 严格防止数据泄露：
- 只使用开仓前可获得的信息作为特征
- 交易结果（exit_price, pnl, hold_days等）不作为预测特征
- 止损止盈价格在开仓时计算，但不作为预测特征
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class OversoldReboundBacktestV2:
    """V2版本超跌反弹策略回测引擎（防数据泄露）"""
    
    def __init__(self):
        self.initial_capital = 1000000  # 100万初始资金
        self.max_positions = 10         # 最大持仓10个股票
        self.position_size = 100000     # 每个仓位10万（均仓）
        
        # 策略参数
        self.risk_reward_ratio = 2.0  # 止盈止损比例 2:1
        
        # 股票池（模拟A股主要股票）
        self.stock_pool = self._generate_stock_pool()
        
        # 机器学习特征数据（严格防止泄露）
        self.ml_features = []
    
    def _generate_stock_pool(self):
        """生成股票池"""
        stock_pool = []
        
        # 主板和深圳股票
        for i in range(1, 2000):
            if i < 1000:
                stock_pool.append(f"{i:06d}")
            else:
                stock_pool.append(f"00{i:04d}")
        
        # 创业板
        for i in range(1, 500):
            stock_pool.append(f"30{i:04d}")
        
        return stock_pool[:1500]  # 限制在1500只股票
    
    def _simulate_daily_buy_signal(self, code: str, date: datetime):
        """模拟日线一买信号检测"""
        # 模拟信号出现概率
        year = date.year
        month = date.month
        
        # 根据市场环境调整信号概率
        if year == 2021:
            base_prob = 0.015  # 2021年牛市，一买信号较少
        elif year == 2022:
            base_prob = 0.035  # 2022年熊市，一买信号较多
        elif year == 2023:
            base_prob = 0.025  # 2023年震荡市
        elif year == 2024:
            base_prob = 0.020  # 2024年平稳市
        else:
            base_prob = 0.025  # 2025年
        
        # 季节性调整（年底年初信号较多）
        if month in [1, 2, 11, 12]:
            base_prob *= 1.2
        
        signal_prob = np.random.random()
        
        if signal_prob < base_prob:
            # 生成一买信号的基础信息
            signal_price = np.random.uniform(8, 80)  # 模拟股价
            
            # 模拟下跌笔的情况
            decline_range = np.random.uniform(0.15, 0.40)  # 下跌15%-40%
            bi_high = signal_price / (1 - decline_range)  # 推算笔高点
            bi_low = signal_price * np.random.uniform(0.85, 0.95)  # 笔低点略低于当前价
            
            # ⚠️ 重要：止损止盈价格在开仓时确定，但不作为预测特征
            stop_loss = bi_low
            risk_amount = signal_price - stop_loss
            take_profit = signal_price + risk_amount * self.risk_reward_ratio
            
            return {
                'code': code,
                'signal_price': signal_price,
                'bi_high': bi_high,
                'bi_low': bi_low,
                'decline_range': decline_range,
                'signal_type': '一买信号',
                # ⚠️ 以下信息用于交易执行，但不作为ML特征
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_amount': risk_amount,
            }
        
        return None
    
    def _calculate_prediction_features(self, signal: dict, date: datetime):
        """计算预测特征（严格防止数据泄露）"""
        # ⚠️ 只包含开仓前可获得的信息
        features = {
            # 基础信息
            'code': signal['code'],
            'open_date': date.strftime('%Y-%m-%d'),
            'open_time': date.strftime('%Y-%m-%d 09:30:00'),  # 假设开盘时买入
            'year': date.year,
            'month': date.month,
            'quarter': (date.month - 1) // 3 + 1,
            'weekday': date.weekday(),
            
            # 信号基础信息（开仓前已知）
            'signal_type': signal['signal_type'],
            'signal_price': signal['signal_price'],
            'bi_high': signal['bi_high'],
            'bi_low': signal['bi_low'],
            'decline_range': signal['decline_range'],
        }
        
        # 生成技术指标特征（开仓前已知）
        features.update(self._generate_technical_features(signal, date))
        
        # 生成缠论特征（开仓前已知）
        features.update(self._generate_chanlun_features(signal, date))
        
        # 生成市场环境特征（开仓前已知）
        features.update(self._generate_market_features(signal, date))
        
        return features
    
    def _generate_technical_features(self, signal: dict, date: datetime):
        """生成技术指标特征（开仓前已知）"""
        signal_price = signal['signal_price']
        
        # 前一日日线技术指标
        features = {
            # K线基础数据
            'prev_close': signal_price,
            'prev_open': signal_price * np.random.uniform(0.98, 1.02),
            'prev_high': signal_price * np.random.uniform(1.0, 1.05),
            'prev_low': signal_price * np.random.uniform(0.95, 1.0),
            'prev_volume': np.random.lognormal(15, 1),
            
            # 移动平均线
            'prev_ma5': signal_price * np.random.uniform(0.98, 1.02),
            'prev_ma10': signal_price * np.random.uniform(0.96, 1.04),
            'prev_ma20': signal_price * np.random.uniform(0.94, 1.06),
            'prev_ma60': signal_price * np.random.uniform(0.90, 1.10),
            
            # 价格相对位置
            'price_vs_ma5': np.random.uniform(-0.1, 0.1),
            'price_vs_ma10': np.random.uniform(-0.15, 0.1),
            'price_vs_ma20': np.random.uniform(-0.2, 0.1),
            'price_vs_ma60': np.random.uniform(-0.3, 0.2),
            
            # 成交量指标
            'volume_ma5': np.random.lognormal(15, 0.8),
            'volume_ratio': np.random.uniform(0.5, 3.0),
            
            # 技术指标
            'rsi_14': np.random.uniform(20, 80),
            'macd': np.random.uniform(-2, 2),
            'macd_signal': np.random.uniform(-2, 2),
            'kdj_k': np.random.uniform(0, 100),
            'kdj_d': np.random.uniform(0, 100),
            
            # 波动率
            'volatility_5d': np.random.uniform(0.01, 0.06),
            'volatility_20d': np.random.uniform(0.015, 0.05),
            
            # 价格区间位置
            'price_position_5d': np.random.uniform(0, 1),
            'price_position_20d': np.random.uniform(0, 1),
            'price_position_60d': np.random.uniform(0, 1),
        }
        
        # 5分钟级别技术指标（开盘前最后一个5分钟）
        features.update({
            'min5_close': signal_price * np.random.uniform(0.995, 1.005),
            'min5_volume': np.random.lognormal(12, 1),
            'min5_ma10': signal_price * np.random.uniform(0.99, 1.01),
            'min5_ma20': signal_price * np.random.uniform(0.98, 1.02),
            'min5_rsi': np.random.uniform(20, 80),
            'min5_volatility': np.random.uniform(0.005, 0.02),
        })
        
        return features
    
    def _generate_chanlun_features(self, signal: dict, date: datetime):
        """生成缠论特征（开仓前已知）"""
        return {
            # 一买信号确认
            'has_1buy_signal': 1,
            'is_1buy_confirmed': np.random.choice([0, 1], p=[0.3, 0.7]),
            
            # 笔的特征
            'bi_decline_pct': signal['decline_range'],
            'price_vs_bi_low': (signal['signal_price'] - signal['bi_low']) / signal['bi_low'],
            'price_vs_bi_high': (signal['signal_price'] - signal['bi_high']) / signal['bi_high'],
            
            # 底分型特征
            'bottom_fractal_confirmed': np.random.choice([0, 1], p=[0.4, 0.6]),
            'bottom_fractal_strength': np.random.uniform(0.1, 1.0),
            'days_since_bi_low': np.random.randint(1, 15),
            
            # 笔的强度和质量
            'bi_strength': np.random.uniform(0.2, 0.8),
            'bi_duration_days': np.random.randint(5, 30),
            
            # 中枢相关（如果有）
            'has_zhongshu': np.random.choice([0, 1], p=[0.6, 0.4]),
            'zhongshu_strength': np.random.uniform(0, 1),
            'price_vs_zhongshu': np.random.uniform(-0.5, 0.5),
            
            # 背驰特征
            'has_beichi': np.random.choice([0, 1], p=[0.7, 0.3]),
            'beichi_strength': np.random.uniform(0, 1),
        }
    
    def _generate_market_features(self, signal: dict, date: datetime):
        """生成市场环境特征（开仓前已知）"""
        year = date.year
        month = date.month
        
        # 根据年份设置市场环境
        if year == 2021:
            market_sentiment = np.random.uniform(0.6, 0.9)  # 牛市
            market_volatility = np.random.uniform(0.3, 0.6)
        elif year == 2022:
            market_sentiment = np.random.uniform(0.1, 0.4)  # 熊市
            market_volatility = np.random.uniform(0.7, 1.0)
        elif year == 2023:
            market_sentiment = np.random.uniform(0.3, 0.7)  # 震荡市
            market_volatility = np.random.uniform(0.5, 0.8)
        elif year == 2024:
            market_sentiment = np.random.uniform(0.4, 0.6)  # 平稳市
            market_volatility = np.random.uniform(0.4, 0.6)
        else:  # 2025
            market_sentiment = np.random.uniform(0.3, 0.6)
            market_volatility = np.random.uniform(0.5, 0.8)
        
        return {
            # 市场环境
            'market_sentiment': market_sentiment,
            'market_volatility': market_volatility,
            'is_bull_market': 1 if year == 2021 else 0,
            'is_bear_market': 1 if year == 2022 else 0,
            'is_volatile_market': 1 if year in [2022, 2023] else 0,
            
            # 时间特征
            'is_year_end': 1 if month in [11, 12] else 0,
            'is_year_start': 1 if month in [1, 2] else 0,
            'is_mid_year': 1 if month in [6, 7, 8] else 0,
            
            # 季节性特征
            'spring': 1 if month in [3, 4, 5] else 0,
            'summer': 1 if month in [6, 7, 8] else 0,
            'autumn': 1 if month in [9, 10, 11] else 0,
            'winter': 1 if month in [12, 1, 2] else 0,
        }
    
    def _simulate_trade_execution(self, signal: dict, features: dict):
        """模拟交易执行和结果（用于生成标签）"""
        # 根据特征计算成功概率
        success_prob = self._calculate_success_probability(features)
        
        # 生成交易结果
        is_success = np.random.random() < success_prob
        
        if is_success:
            # 成功：触发止盈
            exit_price = signal['take_profit']
            exit_reason = '止盈'
            hold_days = np.random.randint(3, 20)  # 3-20天止盈
        else:
            # 失败：触发止损
            exit_price = signal['stop_loss']
            exit_reason = '止损'
            hold_days = np.random.randint(1, 10)   # 1-10天止损
        
        # 计算盈亏
        pnl_pct = (exit_price - signal['signal_price']) / signal['signal_price']
        
        # ⚠️ 交易结果信息（不作为预测特征，仅用于生成标签）
        execution_info = {
            'exit_price': exit_price,
            'pnl_pct': pnl_pct,
            'hold_days': hold_days,
            'exit_reason': exit_reason,
            'label': 1 if is_success else -1,
            'trade_result': 'win' if is_success else 'loss',
            'success_probability': success_prob,
            
            # 交易执行信息（不作为预测特征）
            'actual_stop_loss': signal['stop_loss'],
            'actual_take_profit': signal['take_profit'],
            'risk_amount': signal['risk_amount'],
            'shares': int(self.position_size / signal['signal_price'] / 100) * 100,
            'position_cost': int(self.position_size / signal['signal_price'] / 100) * 100 * signal['signal_price'],
        }
        
        return execution_info
    
    def _calculate_success_probability(self, features: dict):
        """根据特征计算成功概率"""
        base_prob = 0.45  # 基础成功率45%（一买信号质量较高）
        
        # 市场环境调整
        market_bonus = features['market_sentiment'] * 0.25
        volatility_penalty = features['market_volatility'] * 0.1
        
        # 技术指标调整
        tech_bonus = 0
        if features['rsi_14'] < 30:  # 超卖
            tech_bonus += 0.15
        if features['price_vs_ma20'] < -0.1:  # 远离均线
            tech_bonus += 0.1
        if features['volume_ratio'] > 1.5:  # 放量
            tech_bonus += 0.1
        
        # 缠论信号调整
        chanlun_bonus = 0
        if features['is_1buy_confirmed'] == 1:
            chanlun_bonus += 0.2
        if features['bottom_fractal_confirmed'] == 1:
            chanlun_bonus += 0.15
        if features['has_beichi'] == 1:
            chanlun_bonus += 0.1
        
        chanlun_bonus += features['bi_strength'] * 0.15
        chanlun_bonus += features['bottom_fractal_strength'] * 0.1
        
        # 时间特征调整
        time_bonus = 0
        if features['is_year_start'] == 1:  # 年初效应
            time_bonus += 0.1
        
        final_prob = base_prob + market_bonus - volatility_penalty + tech_bonus + chanlun_bonus + time_bonus
        final_prob = max(0.15, min(0.85, final_prob))  # 限制在15%-85%
        
        return final_prob

    def run_backtest(self, start_date='2021-01-01', end_date='2025-07-18'):
        """运行回测"""
        print("🚀 V2版本：超跌股票反弹策略回测（严格防数据泄露）")
        print("=" * 60)
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"💰 初始资金: {self.initial_capital:,} 元")
        print(f"📊 最大持仓: {self.max_positions} 个股票")
        print(f"🎯 策略: 日线一买 + 下跌笔确认结束")
        print(f"📈 止盈止损: 1:{self.risk_reward_ratio} 风险收益比")
        print(f"⚠️  严格防止数据泄露: 只使用开仓前可获得的信息")
        print("=" * 60)

        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        current_date = start_dt
        trading_days = 0
        total_signals = 0

        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4是周一到周五
                trading_days += 1

                # 每50个交易日输出一次进度
                if trading_days % 50 == 0:
                    print(f"📅 {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"📊 已收集信号: {total_signals} 个")

                # 扫描股票池寻找信号
                daily_signals = 0

                # 随机选择部分股票进行扫描（提高效率）
                selected_stocks = np.random.choice(self.stock_pool, size=min(80, len(self.stock_pool)), replace=False)

                for code in selected_stocks:
                    signal = self._simulate_daily_buy_signal(code, current_date)

                    if signal is not None:
                        # 计算预测特征（严格防止数据泄露）
                        prediction_features = self._calculate_prediction_features(signal, current_date)

                        # 模拟交易执行（生成标签和结果）
                        execution_info = self._simulate_trade_execution(signal, prediction_features)

                        # 合并特征和结果
                        complete_record = {**prediction_features, **execution_info}

                        # 保存到机器学习数据
                        self.ml_features.append(complete_record)

                        daily_signals += 1
                        total_signals += 1

                if daily_signals > 0 and trading_days % 10 == 0:
                    print(f"   📈 {current_date.strftime('%Y-%m-%d')}: 发现 {daily_signals} 个一买信号")

            current_date += timedelta(days=1)

        print("\n" + "=" * 60)
        print(f"✅ V2版本回测完成!")
        print(f"📊 总交易日: {trading_days} 天")
        print(f"📊 总信号数: {total_signals} 个")
        print(f"📊 完整交易记录: {len(self.ml_features)} 条")

        # 生成回测报告
        self._generate_backtest_report()

        # 保存机器学习数据
        self._save_ml_features()

        return {
            'trading_days': trading_days,
            'total_signals': total_signals,
            'complete_trades': len(self.ml_features)
        }

    def _generate_backtest_report(self):
        """生成回测报告"""
        if not self.ml_features:
            print("❌ 没有交易数据")
            return

        df = pd.DataFrame(self.ml_features)

        print(f"\n📊 V2版本策略表现分析:")

        # 按年份统计
        for year in sorted(df['year'].unique()):
            year_data = df[df['year'] == year]
            total = len(year_data)
            wins = sum(year_data['label'] == 1)
            losses = sum(year_data['label'] == -1)
            win_rate = wins / total if total > 0 else 0

            # 计算累计收益
            cum_return = (1 + year_data.sort_values('open_date')['pnl_pct']).prod() - 1
            avg_return = year_data['pnl_pct'].mean()
            avg_success_prob = year_data['success_probability'].mean()

            print(f"  {year}年: {total}条信号, 胜率{win_rate:.2%}, "
                  f"平均收益{avg_return:.2%}, 累计收益{cum_return:.2%}, "
                  f"平均成功概率{avg_success_prob:.2%}")

        # 总体统计
        total_trades = len(df)
        total_wins = sum(df['label'] == 1)
        overall_win_rate = total_wins / total_trades
        overall_cum_return = (1 + df.sort_values('open_date')['pnl_pct']).prod() - 1
        overall_avg_prob = df['success_probability'].mean()

        print(f"\n📈 总体表现:")
        print(f"  总交易数: {total_trades} 条")
        print(f"  总胜率: {overall_win_rate:.2%}")
        print(f"  累计收益: {overall_cum_return:.2%}")
        print(f"  平均成功概率: {overall_avg_prob:.2%}")

        # 止盈止损统计
        take_profit_count = sum(df['exit_reason'] == '止盈')
        stop_loss_count = sum(df['exit_reason'] == '止损')

        print(f"  止盈次数: {take_profit_count} 次 ({take_profit_count/total_trades:.2%})")
        print(f"  止损次数: {stop_loss_count} 次 ({stop_loss_count/total_trades:.2%})")

        # 平均持仓天数
        avg_hold_days = df['hold_days'].mean()
        print(f"  平均持仓: {avg_hold_days:.1f} 天")

        # 风险收益比验证
        avg_win_return = df[df['label'] == 1]['pnl_pct'].mean()
        avg_loss_return = df[df['label'] == -1]['pnl_pct'].mean()
        actual_risk_reward = abs(avg_win_return / avg_loss_return) if avg_loss_return != 0 else 0

        print(f"  平均盈利: {avg_win_return:.2%}")
        print(f"  平均亏损: {avg_loss_return:.2%}")
        print(f"  实际风险收益比: 1:{actual_risk_reward:.2f}")

    def _save_ml_features(self):
        """保存机器学习特征数据"""
        if not self.ml_features:
            print("❌ 没有特征数据")
            return

        try:
            # 转换为DataFrame
            ml_df = pd.DataFrame(self.ml_features)

            # 保存到data目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            os.makedirs(data_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = os.path.join(data_dir, f'v2_oversold_rebound_features_{timestamp}.csv')
            parquet_filename = os.path.join(data_dir, f'v2_oversold_rebound_features_{timestamp}.parquet')

            ml_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            ml_df.to_parquet(parquet_filename, index=False)

            print(f"\n✅ V2版本特征数据已保存:")
            print(f"  CSV文件: {csv_filename}")
            print(f"  Parquet文件: {parquet_filename}")
            print(f"  总记录数: {len(ml_df)} 条")

            # 验证数据质量（防止泄露检查）
            self._verify_data_quality(ml_df)

        except Exception as e:
            print(f"❌ 保存特征数据失败: {e}")

    def _verify_data_quality(self, df):
        """验证数据质量，确保没有数据泄露"""
        print(f"\n🔍 数据质量验证（防泄露检查）:")

        # 检查特征列
        prediction_features = [col for col in df.columns if col not in [
            'exit_price', 'pnl_pct', 'hold_days', 'exit_reason', 'label', 'trade_result',
            'success_probability', 'actual_stop_loss', 'actual_take_profit', 'risk_amount',
            'shares', 'position_cost'
        ]]

        result_columns = [col for col in df.columns if col in [
            'exit_price', 'pnl_pct', 'hold_days', 'exit_reason', 'label', 'trade_result'
        ]]

        print(f"  预测特征数: {len(prediction_features)} 个")
        print(f"  结果标签数: {len(result_columns)} 个")
        print(f"  样本/特征比例: {len(df) / len(prediction_features):.2f}")

        # 检查是否有异常相关性（可能的数据泄露）
        high_corr_features = []
        for col in prediction_features[:10]:  # 检查前10个特征
            if df[col].dtype in ['int64', 'float64']:
                try:
                    corr = df[col].corr(df['label'])
                    if abs(corr) > 0.8:  # 相关性过高可能有问题
                        high_corr_features.append((col, corr))
                except:
                    pass

        if high_corr_features:
            print(f"  ⚠️  发现高相关特征:")
            for col, corr in high_corr_features:
                print(f"    {col}: {corr:.4f}")
        else:
            print(f"  ✅ 特征相关性正常，无明显数据泄露")

        # 检查标签分布
        label_dist = df['label'].value_counts()
        print(f"  标签分布: 盈利{label_dist.get(1, 0)}条, 亏损{label_dist.get(-1, 0)}条")

def main():
    """主函数"""
    backtest = OversoldReboundBacktestV2()
    results = backtest.run_backtest(
        start_date="2021-01-01",
        end_date="2025-07-18"
    )

    print(f"\n🎉 V2版本数据收集完成！")
    print(f"📊 为机器学习准备了 {results['complete_trades']} 条训练数据")
    print(f"⚠️  数据已严格防止泄露，可安全用于机器学习训练")

if __name__ == "__main__":
    main()
