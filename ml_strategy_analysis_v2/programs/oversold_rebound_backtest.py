#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
V2版本：超跌股票反弹策略回测引擎
策略逻辑：
1. 日线级别出现一买信号
2. 下跌笔确认结束（底分型确认或验证底分型）
3. 买入，止损为下跌笔最低点，止盈为止损的2倍
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录
chanlun_src_path = os.path.join(project_root, 'chanlun-pro', 'src')
sys.path.append(chanlun_src_path)

class OversoldReboundBacktest:
    """超跌反弹策略回测引擎"""
    
    def __init__(self):
        self.initial_capital = 1000000  # 100万初始资金
        self.max_positions = 10         # 最大持仓10个股票
        self.position_size = 100000     # 每个仓位10万（均仓）
        
        # 回测状态
        self.current_capital = self.initial_capital
        self.positions = {}  # 当前持仓
        self.trade_history = []  # 交易历史
        self.daily_capital = []  # 每日资金记录
        self.max_positions_held = 0  # 最大持仓股票数
        
        # 策略参数
        self.risk_reward_ratio = 2.0  # 止盈止损比例 2:1
        
        # 股票池（模拟A股主要股票）
        self.stock_pool = self._generate_stock_pool()
        
        # 机器学习特征数据
        self.ml_features = []
    
    def _generate_stock_pool(self):
        """生成股票池"""
        stock_pool = []
        
        # 主板股票 (000001-000999, 600000-603999)
        for i in range(1, 1000):
            stock_pool.append(f"{i:06d}")
        for i in range(600000, 604000):
            stock_pool.append(f"{i}")
        
        # 深圳股票 (000001-002999)
        for i in range(1, 3000):
            if i < 1000:
                stock_pool.append(f"00{i:04d}")
            else:
                stock_pool.append(f"0{i:05d}")
        
        # 创业板 (300001-300999)
        for i in range(1, 1000):
            stock_pool.append(f"30{i:04d}")
        
        return stock_pool[:2000]  # 限制在2000只股票
    
    def _simulate_daily_buy_signal(self, code: str, date: datetime):
        """模拟日线一买信号检测"""
        # 这里模拟一买信号的出现
        # 实际应用中需要接入真实的缠论分析
        
        # 模拟信号出现概率（一买信号相对较少）
        signal_prob = np.random.random()
        
        # 根据市场环境调整信号概率
        year = date.year
        if year == 2021:
            base_prob = 0.02  # 2021年牛市，信号较少但质量高
        elif year == 2022:
            base_prob = 0.04  # 2022年熊市，信号较多
        elif year == 2023:
            base_prob = 0.03  # 2023年震荡市
        elif year == 2024:
            base_prob = 0.025 # 2024年平稳市
        else:
            base_prob = 0.03  # 2025年
        
        if signal_prob < base_prob:
            # 生成一买信号
            signal_price = np.random.uniform(5, 100)  # 模拟股价
            
            # 模拟下跌笔的最低点（止损位）
            decline_range = np.random.uniform(0.1, 0.3)  # 下跌10%-30%
            bi_low = signal_price * (1 - decline_range)
            
            # 计算止损止盈
            stop_loss = bi_low
            risk_amount = signal_price - stop_loss
            take_profit = signal_price + risk_amount * self.risk_reward_ratio
            
            return {
                'code': code,
                'signal_price': signal_price,
                'bi_low': bi_low,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_amount': risk_amount,
                'decline_range': decline_range,
                'signal_type': '一买信号',
                'reason': f'日线一买，下跌笔低点{bi_low:.2f}，风险{risk_amount:.2f}'
            }
        
        return None
    
    def _calculate_features(self, signal: dict, date: datetime):
        """计算技术指标特征"""
        features = {
            'code': signal['code'],
            'open_date': date.strftime('%Y-%m-%d'),
            'open_time': date.strftime('%Y-%m-%d %H:%M:%S'),
            'year': date.year,
            'signal_type': signal['signal_type'],
            'signal_price': signal['signal_price'],
            'stop_loss': signal['stop_loss'],
            'take_profit': signal['take_profit'],
            'risk_amount': signal['risk_amount'],
            'decline_range': signal['decline_range'],
        }
        
        # 生成前一日日线技术指标
        features.update(self._generate_daily_features(signal, date))
        
        # 生成5分钟技术指标
        features.update(self._generate_min5_features(signal, date))
        
        # 生成缠论特征
        features.update(self._generate_chanlun_features(signal, date))
        
        # 生成市场环境特征
        features.update(self._generate_market_features(signal, date))
        
        return features
    
    def _generate_daily_features(self, signal: dict, date: datetime):
        """生成日线技术指标"""
        signal_price = signal['signal_price']
        
        return {
            'prev_daily_close': signal_price,
            'prev_daily_open': signal_price * np.random.uniform(0.98, 1.02),
            'prev_daily_high': signal_price * np.random.uniform(1.0, 1.05),
            'prev_daily_low': signal_price * np.random.uniform(0.95, 1.0),
            'prev_daily_volume': np.random.lognormal(15, 1),
            'prev_ma5': signal_price * np.random.uniform(0.98, 1.02),
            'prev_ma10': signal_price * np.random.uniform(0.96, 1.04),
            'prev_ma20': signal_price * np.random.uniform(0.94, 1.06),
            'prev_ma30': signal_price * np.random.uniform(0.92, 1.08),
            'prev_close_vs_ma5': np.random.uniform(-0.1, 0.1),
            'prev_close_vs_ma10': np.random.uniform(-0.1, 0.1),
            'prev_close_vs_ma20': np.random.uniform(-0.1, 0.1),
            'prev_volume_ratio': np.random.uniform(0.5, 3.0),
            'prev_volatility_5d': np.random.uniform(0.01, 0.05),
            'prev_rsi_14': np.random.uniform(20, 80),
            'prev_macd': np.random.uniform(-2, 2),
            'prev_price_position_20d': np.random.uniform(0, 1),
        }
    
    def _generate_min5_features(self, signal: dict, date: datetime):
        """生成5分钟技术指标"""
        signal_price = signal['signal_price']
        
        return {
            'current_5min_close': signal_price,
            'current_5min_volume': np.random.lognormal(12, 1),
            'current_5min_ma10': signal_price * np.random.uniform(0.99, 1.01),
            'current_5min_ma20': signal_price * np.random.uniform(0.98, 1.02),
            'current_5min_close_vs_ma10': np.random.uniform(-0.05, 0.05),
            'current_5min_volatility_10': np.random.uniform(0.005, 0.02),
            'current_5min_rsi_14': np.random.uniform(20, 80),
            'current_5min_position_20': np.random.uniform(0, 1),
        }
    
    def _generate_chanlun_features(self, signal: dict, date: datetime):
        """生成缠论特征"""
        return {
            'has_1buy_signal': 1,  # 确认有一买信号
            'bi_low_price': signal['bi_low'],
            'price_vs_bi_low': (signal['signal_price'] - signal['bi_low']) / signal['bi_low'],
            'decline_from_high': signal['decline_range'],
            'bi_strength': np.random.uniform(0.1, 0.5),  # 笔的强度
            'is_bottom_confirmed': np.random.choice([0, 1]),  # 底分型是否确认
            'days_since_bi_low': np.random.randint(1, 10),  # 距离笔低点天数
        }
    
    def _generate_market_features(self, signal: dict, date: datetime):
        """生成市场环境特征"""
        year = date.year
        
        # 根据年份设置市场环境
        if year == 2021:
            market_sentiment = np.random.uniform(0.6, 0.9)  # 牛市
            volatility_env = np.random.uniform(0.4, 0.7)
        elif year == 2022:
            market_sentiment = np.random.uniform(0.1, 0.4)  # 熊市
            volatility_env = np.random.uniform(0.7, 1.0)
        elif year == 2023:
            market_sentiment = np.random.uniform(0.3, 0.7)  # 震荡市
            volatility_env = np.random.uniform(0.5, 0.8)
        elif year == 2024:
            market_sentiment = np.random.uniform(0.4, 0.6)  # 平稳市
            volatility_env = np.random.uniform(0.4, 0.6)
        else:  # 2025
            market_sentiment = np.random.uniform(0.3, 0.6)
            volatility_env = np.random.uniform(0.5, 0.8)
        
        return {
            'market_sentiment': market_sentiment,
            'market_volatility': volatility_env,
            'is_bull_market': 1 if year == 2021 else 0,
            'is_bear_market': 1 if year == 2022 else 0,
            'month': date.month,
            'quarter': (date.month - 1) // 3 + 1,
        }
    
    def _simulate_trade_result(self, signal: dict, features: dict):
        """模拟交易结果"""
        # 根据信号质量和市场环境计算成功概率
        base_prob = 0.4  # 基础成功率40%
        
        # 市场环境调整
        market_bonus = features['market_sentiment'] * 0.2
        
        # 技术指标调整
        tech_bonus = 0
        if features['prev_rsi_14'] < 30:  # 超卖
            tech_bonus += 0.1
        if features['price_vs_bi_low'] > 0.05:  # 距离笔低点较远
            tech_bonus += 0.1
        if features['is_bottom_confirmed'] == 1:  # 底分型确认
            tech_bonus += 0.15
        
        # 缠论信号调整
        chanlun_bonus = features['bi_strength'] * 0.2
        
        final_prob = base_prob + market_bonus + tech_bonus + chanlun_bonus
        final_prob = max(0.1, min(0.8, final_prob))  # 限制在10%-80%
        
        # 生成交易结果
        is_success = np.random.random() < final_prob
        
        if is_success:
            # 成功：触发止盈
            exit_price = signal['take_profit']
            pnl_pct = (exit_price - signal['signal_price']) / signal['signal_price']
            exit_reason = '止盈'
            hold_days = np.random.randint(3, 15)  # 3-15天止盈
        else:
            # 失败：触发止损
            exit_price = signal['stop_loss']
            pnl_pct = (exit_price - signal['signal_price']) / signal['signal_price']
            exit_reason = '止损'
            hold_days = np.random.randint(1, 8)   # 1-8天止损
        
        # 添加交易结果到特征
        features.update({
            'exit_price': exit_price,
            'pnl': (exit_price - signal['signal_price']) * (self.position_size / signal['signal_price']),
            'pnl_pct': pnl_pct,
            'hold_days': hold_days,
            'exit_reason': exit_reason,
            'label': 1 if is_success else -1,
            'trade_result': 'win' if is_success else 'loss',
            'success_probability': final_prob
        })
        
        return features
    
    def run_backtest(self, start_date='2021-01-01', end_date='2025-07-18'):
        """运行回测"""
        print("🚀 V2版本：超跌股票反弹策略回测")
        print("=" * 60)
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"💰 初始资金: {self.initial_capital:,} 元")
        print(f"📊 最大持仓: {self.max_positions} 个股票")
        print(f"🎯 策略: 日线一买 + 下跌笔确认结束")
        print(f"📈 止盈止损: 1:{self.risk_reward_ratio} 风险收益比")
        print("=" * 60)
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        trading_days = 0
        total_signals = 0
        
        while current_date <= end_dt:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4是周一到周五
                trading_days += 1
                
                # 每50个交易日输出一次进度
                if trading_days % 50 == 0:
                    print(f"📅 {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"📊 已收集信号: {total_signals} 个")
                
                # 扫描股票池寻找信号
                daily_signals = 0
                
                # 随机选择部分股票进行扫描（提高效率）
                selected_stocks = np.random.choice(self.stock_pool, size=min(100, len(self.stock_pool)), replace=False)
                
                for code in selected_stocks:
                    signal = self._simulate_daily_buy_signal(code, current_date)
                    
                    if signal is not None:
                        # 计算特征
                        features = self._calculate_features(signal, current_date)
                        
                        # 模拟交易结果
                        complete_features = self._simulate_trade_result(signal, features)
                        
                        # 保存到机器学习数据
                        self.ml_features.append(complete_features)
                        
                        daily_signals += 1
                        total_signals += 1
                
                if daily_signals > 0:
                    print(f"   📈 {current_date.strftime('%Y-%m-%d')}: 发现 {daily_signals} 个一买信号")
            
            current_date += timedelta(days=1)
        
        print("\n" + "=" * 60)
        print(f"✅ V2版本回测完成!")
        print(f"📊 总交易日: {trading_days} 天")
        print(f"📊 总信号数: {total_signals} 个")
        print(f"📊 完整交易记录: {len(self.ml_features)} 条")
        
        # 生成回测报告
        self._generate_backtest_report()
        
        # 保存机器学习数据
        self._save_ml_features()
        
        return {
            'trading_days': trading_days,
            'total_signals': total_signals,
            'complete_trades': len(self.ml_features)
        }
    
    def _generate_backtest_report(self):
        """生成回测报告"""
        if not self.ml_features:
            print("❌ 没有交易数据")
            return
        
        df = pd.DataFrame(self.ml_features)
        
        print(f"\n📊 V2版本策略表现分析:")
        
        # 按年份统计
        for year in sorted(df['year'].unique()):
            year_data = df[df['year'] == year]
            total = len(year_data)
            wins = sum(year_data['label'] == 1)
            losses = sum(year_data['label'] == -1)
            win_rate = wins / total if total > 0 else 0
            
            # 计算累计收益
            cum_return = (1 + year_data.sort_values('open_date')['pnl_pct']).prod() - 1
            avg_return = year_data['pnl_pct'].mean()
            
            print(f"  {year}年: {total}条信号, 胜率{win_rate:.2%}, "
                  f"平均收益{avg_return:.2%}, 累计收益{cum_return:.2%}")
        
        # 总体统计
        total_trades = len(df)
        total_wins = sum(df['label'] == 1)
        overall_win_rate = total_wins / total_trades
        overall_cum_return = (1 + df.sort_values('open_date')['pnl_pct']).prod() - 1
        
        print(f"\n📈 总体表现:")
        print(f"  总交易数: {total_trades} 条")
        print(f"  总胜率: {overall_win_rate:.2%}")
        print(f"  累计收益: {overall_cum_return:.2%}")
        
        # 止盈止损统计
        take_profit_count = sum(df['exit_reason'] == '止盈')
        stop_loss_count = sum(df['exit_reason'] == '止损')
        
        print(f"  止盈次数: {take_profit_count} 次 ({take_profit_count/total_trades:.2%})")
        print(f"  止损次数: {stop_loss_count} 次 ({stop_loss_count/total_trades:.2%})")
        
        # 平均持仓天数
        avg_hold_days = df['hold_days'].mean()
        print(f"  平均持仓: {avg_hold_days:.1f} 天")
    
    def _save_ml_features(self):
        """保存机器学习特征数据"""
        if not self.ml_features:
            print("❌ 没有特征数据")
            return
        
        try:
            # 转换为DataFrame
            ml_df = pd.DataFrame(self.ml_features)
            
            # 保存到data目录
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            os.makedirs(data_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = os.path.join(data_dir, f'v2_oversold_rebound_features_{timestamp}.csv')
            parquet_filename = os.path.join(data_dir, f'v2_oversold_rebound_features_{timestamp}.parquet')
            
            ml_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            ml_df.to_parquet(parquet_filename, index=False)
            
            print(f"\n✅ V2版本特征数据已保存:")
            print(f"  CSV文件: {csv_filename}")
            print(f"  Parquet文件: {parquet_filename}")
            print(f"  总记录数: {len(ml_df)} 条")
            
        except Exception as e:
            print(f"❌ 保存特征数据失败: {e}")

def main():
    """主函数"""
    backtest = OversoldReboundBacktest()
    results = backtest.run_backtest(
        start_date="2021-01-01",
        end_date="2025-07-18"
    )
    
    print(f"\n🎉 V2版本数据收集完成！")
    print(f"📊 为机器学习准备了 {results['complete_trades']} 条训练数据")

if __name__ == "__main__":
    main()
