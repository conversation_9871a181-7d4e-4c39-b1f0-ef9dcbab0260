#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整数据收集器 - 记录所有缠论、技术指标、价格、成交量数据
确保机器学习训练数据的完整性
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class CompleteDataCollector:
    """完整数据收集器"""
    
    def __init__(self):
        self.stock_pool = [f"{i:06d}" for i in range(1, 51)]  # 50只股票测试
        self.stock_kline_history = {}
        self.complete_signal_records = []
        
    def _generate_single_kline(self, code: str, date: datetime, prev_close: float):
        """生成单根K线"""
        daily_return = np.random.normal(0, 0.025)
        
        open_price = prev_close * (1 + np.random.uniform(-0.015, 0.015))
        close_price = prev_close * (1 + daily_return)
        
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.025))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.025))
        
        volume = np.random.lognormal(15, 0.8)
        amount = volume * (high_price + low_price + open_price + close_price) / 4
        
        return {
            'code': code,
            'date': date,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': int(volume),
            'amount': round(amount, 2),
            'prev_close': prev_close
        }
    
    def _calculate_technical_indicators(self, klines):
        """计算完整的技术指标"""
        if len(klines) < 60:
            return {}
        
        # 提取价格和成交量数据
        closes = np.array([k['close'] for k in klines])
        highs = np.array([k['high'] for k in klines])
        lows = np.array([k['low'] for k in klines])
        volumes = np.array([k['volume'] for k in klines])
        
        # ⚠️ 彻底修复数据泄露：在开盘时做决策，只能使用前一日及之前的数据
        # 当前价格 = 前一日收盘价（开盘时已知的最新价格）
        if len(closes) < 2:
            return {}  # 数据不足，无法计算

        current_price = closes[-2]  # 前一日收盘价（开盘时已知）

        indicators = {}

        # 移动平均线（只使用前一日及之前的数据）
        indicators['ma5'] = np.mean(closes[-6:-1]) if len(closes) >= 6 else current_price
        indicators['ma10'] = np.mean(closes[-11:-1]) if len(closes) >= 11 else current_price
        indicators['ma20'] = np.mean(closes[-21:-1]) if len(closes) >= 21 else current_price
        indicators['ma30'] = np.mean(closes[-31:-1]) if len(closes) >= 31 else current_price
        indicators['ma60'] = np.mean(closes[-61:-1]) if len(closes) >= 61 else current_price
        
        # 价格相对均线位置（乖离率）- 使用前一日收盘价
        indicators['bias_ma5'] = (current_price - indicators['ma5']) / indicators['ma5'] * 100  # 5日乖离率
        indicators['bias_ma10'] = (current_price - indicators['ma10']) / indicators['ma10'] * 100  # 10日乖离率
        indicators['bias_ma20'] = (current_price - indicators['ma20']) / indicators['ma20'] * 100  # 20日乖离率
        indicators['bias_ma30'] = (current_price - indicators['ma30']) / indicators['ma30'] * 100  # 30日乖离率
        indicators['bias_ma60'] = (current_price - indicators['ma60']) / indicators['ma60'] * 100  # 60日乖离率

        # 保持原有的比例指标（兼容性）
        indicators['price_vs_ma5'] = indicators['bias_ma5'] / 100
        indicators['price_vs_ma10'] = indicators['bias_ma10'] / 100
        indicators['price_vs_ma20'] = indicators['bias_ma20'] / 100
        indicators['price_vs_ma30'] = indicators['bias_ma30'] / 100
        indicators['price_vs_ma60'] = indicators['bias_ma60'] / 100
        
        # 成交量指标（只使用前一日及之前的数据）
        prev_volume = volumes[-2] if len(volumes) >= 2 else volumes[-1]
        indicators['volume_ma5'] = np.mean(volumes[-6:-1]) if len(volumes) >= 6 else prev_volume
        indicators['volume_ma10'] = np.mean(volumes[-11:-1]) if len(volumes) >= 11 else prev_volume
        indicators['volume_ma20'] = np.mean(volumes[-21:-1]) if len(volumes) >= 21 else prev_volume
        indicators['volume_ratio'] = prev_volume / indicators['volume_ma5'] if indicators['volume_ma5'] > 0 else 1
        indicators['volume_ratio_10'] = prev_volume / indicators['volume_ma10'] if indicators['volume_ma10'] > 0 else 1
        indicators['volume_ratio_20'] = prev_volume / indicators['volume_ma20'] if indicators['volume_ma20'] > 0 else 1
        
        # RSI指标（只使用前一日及之前的数据）
        if len(closes) >= 15:
            deltas = np.diff(closes[-16:-1])  # 使用前15天数据计算14天RSI
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            avg_gain = np.mean(gains)
            avg_loss = np.mean(losses)
            rs = avg_gain / avg_loss if avg_loss != 0 else 100
            indicators['rsi_14'] = 100 - (100 / (1 + rs))
        else:
            indicators['rsi_14'] = 50
        
        # MACD指标（只使用前一日及之前的数据）
        if len(closes) >= 27:
            ema12 = current_price  # 使用前一日收盘价
            ema26 = np.mean(closes[-27:-1])  # 使用前26天数据
            indicators['macd'] = ema12 - ema26
            indicators['macd_signal'] = indicators['macd'] * 0.9  # 简化
            indicators['macd_hist'] = indicators['macd'] - indicators['macd_signal']
        else:
            indicators['macd'] = 0
            indicators['macd_signal'] = 0
            indicators['macd_hist'] = 0
        
        # KDJ指标（只使用前一日及之前的数据）
        if len(closes) >= 10:
            high_9 = np.max(highs[-10:-1])  # 前9天的最高价
            low_9 = np.min(lows[-10:-1])    # 前9天的最低价
            rsv = (current_price - low_9) / (high_9 - low_9) * 100 if high_9 != low_9 else 50
            indicators['kdj_k'] = rsv * 0.3 + 50 * 0.7  # 简化计算
            indicators['kdj_d'] = indicators['kdj_k'] * 0.3 + 50 * 0.7
            indicators['kdj_j'] = 3 * indicators['kdj_k'] - 2 * indicators['kdj_d']
        else:
            indicators['kdj_k'] = 50
            indicators['kdj_d'] = 50
            indicators['kdj_j'] = 50
        
        # 前几日收益率（修复数据泄露：使用前一日收盘价作为基准）
        if len(closes) >= 3:
            # 1日前收益率：前1日相对前2日的收益率
            indicators['return_1d'] = (closes[-2] - closes[-3]) / closes[-3] * 100
        else:
            indicators['return_1d'] = 0

        if len(closes) >= 4:
            # 2日前收益率：前1日相对前3日的收益率
            indicators['return_2d'] = (closes[-2] - closes[-4]) / closes[-4] * 100
        else:
            indicators['return_2d'] = 0

        if len(closes) >= 5:
            # 3日前收益率：前1日相对前4日的收益率
            indicators['return_3d'] = (closes[-2] - closes[-5]) / closes[-5] * 100
        else:
            indicators['return_3d'] = 0

        if len(closes) >= 7:
            # 5日前收益率：前1日相对前6日的收益率
            indicators['return_5d'] = (closes[-2] - closes[-7]) / closes[-7] * 100
        else:
            indicators['return_5d'] = 0

        if len(closes) >= 12:
            # 10日前收益率：前1日相对前11日的收益率
            indicators['return_10d'] = (closes[-2] - closes[-12]) / closes[-12] * 100
        else:
            indicators['return_10d'] = 0

        if len(closes) >= 22:
            # 20日前收益率：前1日相对前21日的收益率
            indicators['return_20d'] = (closes[-2] - closes[-22]) / closes[-22] * 100
        else:
            indicators['return_20d'] = 0

        # 累计收益率（修复数据泄露：使用历史数据计算）
        if len(closes) >= 5:
            # 前3日累计收益率（不包含当日）
            cum_return_3d = 1.0
            for i in range(3):
                daily_return = closes[-2-i] / closes[-3-i]  # 使用历史数据
                cum_return_3d *= daily_return
            indicators['cum_return_3d'] = (cum_return_3d - 1) * 100
        else:
            indicators['cum_return_3d'] = 0

        if len(closes) >= 7:
            # 前5日累计收益率（不包含当日）
            cum_return_5d = 1.0
            for i in range(5):
                daily_return = closes[-2-i] / closes[-3-i]  # 使用历史数据
                cum_return_5d *= daily_return
            indicators['cum_return_5d'] = (cum_return_5d - 1) * 100
        else:
            indicators['cum_return_5d'] = 0

        # 波动率
        if len(closes) >= 20:
            returns = np.diff(closes[-21:]) / closes[-21:-1]
            indicators['volatility_20'] = np.std(returns) * 100  # 转换为百分比
        else:
            indicators['volatility_20'] = 2.0

        if len(closes) >= 10:
            returns = np.diff(closes[-11:]) / closes[-11:-1]
            indicators['volatility_10'] = np.std(returns) * 100  # 10日波动率
        else:
            indicators['volatility_10'] = 2.0

        if len(closes) >= 5:
            returns = np.diff(closes[-6:]) / closes[-6:-1]
            indicators['volatility_5'] = np.std(returns) * 100  # 5日波动率
        else:
            indicators['volatility_5'] = 2.0
        
        # 价格位置（修复数据泄露：使用前一日价格）
        if len(closes) >= 20:
            high_20 = np.max(highs[-20:])
            low_20 = np.min(lows[-20:])
            indicators['price_position_20'] = (current_price - low_20) / (high_20 - low_20) if high_20 != low_20 else 0.5
        else:
            indicators['price_position_20'] = 0.5

        if len(closes) >= 60:
            high_60 = np.max(highs[-60:])
            low_60 = np.min(lows[-60:])
            indicators['price_position_60'] = (current_price - low_60) / (high_60 - low_60) if high_60 != low_60 else 0.5
        else:
            indicators['price_position_60'] = 0.5

        # 均线多空排列
        if len(closes) >= 60:
            ma_alignment_bull = (indicators['ma5'] > indicators['ma10'] > indicators['ma20'] > indicators['ma30'] > indicators['ma60'])
            ma_alignment_bear = (indicators['ma5'] < indicators['ma10'] < indicators['ma20'] < indicators['ma30'] < indicators['ma60'])
            indicators['ma_alignment_bull'] = 1 if ma_alignment_bull else 0
            indicators['ma_alignment_bear'] = 1 if ma_alignment_bear else 0
            indicators['ma_alignment_mixed'] = 1 if not (ma_alignment_bull or ma_alignment_bear) else 0
        else:
            indicators['ma_alignment_bull'] = 0
            indicators['ma_alignment_bear'] = 0
            indicators['ma_alignment_mixed'] = 1

        # 均线斜率（趋势强度）
        if len(closes) >= 25:
            ma5_slope = (indicators['ma5'] - np.mean(closes[-10:-5])) / np.mean(closes[-10:-5]) * 100
            ma10_slope = (indicators['ma10'] - np.mean(closes[-15:-10])) / np.mean(closes[-15:-10]) * 100
            ma20_slope = (indicators['ma20'] - np.mean(closes[-25:-20])) / np.mean(closes[-25:-20]) * 100

            indicators['ma5_slope'] = ma5_slope
            indicators['ma10_slope'] = ma10_slope
            indicators['ma20_slope'] = ma20_slope
        else:
            indicators['ma5_slope'] = 0
            indicators['ma10_slope'] = 0
            indicators['ma20_slope'] = 0

        # 价格突破均线情况
        if len(closes) >= 3:
            # 检查是否突破均线
            prev_close = closes[-2]
            indicators['break_ma5'] = 1 if (prev_close <= indicators['ma5'] and current_price > indicators['ma5']) else 0
            indicators['break_ma10'] = 1 if (prev_close <= indicators['ma10'] and current_price > indicators['ma10']) else 0
            indicators['break_ma20'] = 1 if (prev_close <= indicators['ma20'] and current_price > indicators['ma20']) else 0

            # 检查是否跌破均线
            indicators['fall_below_ma5'] = 1 if (prev_close >= indicators['ma5'] and current_price < indicators['ma5']) else 0
            indicators['fall_below_ma10'] = 1 if (prev_close >= indicators['ma10'] and current_price < indicators['ma10']) else 0
            indicators['fall_below_ma20'] = 1 if (prev_close >= indicators['ma20'] and current_price < indicators['ma20']) else 0
        else:
            indicators['break_ma5'] = 0
            indicators['break_ma10'] = 0
            indicators['break_ma20'] = 0
            indicators['fall_below_ma5'] = 0
            indicators['fall_below_ma10'] = 0
            indicators['fall_below_ma20'] = 0

        # 极值乖离率（判断超买超卖）
        bias_values = [abs(indicators['bias_ma5']), abs(indicators['bias_ma10']), abs(indicators['bias_ma20'])]
        indicators['max_bias'] = max(bias_values)  # 最大乖离率
        indicators['min_bias'] = min(bias_values)  # 最小乖离率
        indicators['avg_bias'] = np.mean(bias_values)  # 平均乖离率

        # 乖离率极值判断
        indicators['extreme_overbought'] = 1 if indicators['max_bias'] > 15 and current_price > indicators['ma20'] else 0
        indicators['extreme_oversold'] = 1 if indicators['max_bias'] > 15 and current_price < indicators['ma20'] else 0
        indicators['moderate_overbought'] = 1 if 8 < indicators['max_bias'] <= 15 and current_price > indicators['ma20'] else 0
        indicators['moderate_oversold'] = 1 if 8 < indicators['max_bias'] <= 15 and current_price < indicators['ma20'] else 0

        return indicators
    
    def _analyze_chanlun_structure(self, klines):
        """分析缠论结构"""
        if len(klines) < 30:
            return {}
        
        # 提取价格数据
        highs = [k['high'] for k in klines[-30:]]
        lows = [k['low'] for k in klines[-30:]]
        closes = [k['close'] for k in klines[-30:]]
        
        chanlun_data = {}
        
        # 寻找分型（简化版）
        fractals = []
        for i in range(2, len(highs)-2):
            # 顶分型
            if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                fractals.append({
                    'type': 'top',
                    'index': i,
                    'price': highs[i],
                    'strength': (highs[i] - min(highs[i-2:i+3])) / highs[i]
                })
            
            # 底分型
            elif (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                  lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                fractals.append({
                    'type': 'bottom',
                    'index': i,
                    'price': lows[i],
                    'strength': (max(lows[i-2:i+3]) - lows[i]) / lows[i]
                })
        
        chanlun_data['fractal_count'] = len(fractals)
        chanlun_data['top_fractal_count'] = len([f for f in fractals if f['type'] == 'top'])
        chanlun_data['bottom_fractal_count'] = len([f for f in fractals if f['type'] == 'bottom'])
        
        # 最近的分型
        if fractals:
            last_fractal = fractals[-1]
            chanlun_data['last_fractal_type'] = last_fractal['type']
            chanlun_data['last_fractal_price'] = last_fractal['price']
            chanlun_data['last_fractal_strength'] = last_fractal['strength']
            chanlun_data['bars_since_last_fractal'] = len(closes) - 1 - last_fractal['index']
        else:
            chanlun_data['last_fractal_type'] = 'none'
            chanlun_data['last_fractal_price'] = closes[-1]
            chanlun_data['last_fractal_strength'] = 0
            chanlun_data['bars_since_last_fractal'] = 0
        
        # 笔的分析
        bi_points = []
        for fractal in fractals:
            if fractal['strength'] > 0.03:  # 强度阈值
                bi_points.append(fractal)
        
        chanlun_data['bi_point_count'] = len(bi_points)
        
        if len(bi_points) >= 2:
            # 最近的笔
            last_bi = bi_points[-1]
            second_last_bi = bi_points[-2]
            
            chanlun_data['current_bi_type'] = last_bi['type']
            chanlun_data['current_bi_price'] = last_bi['price']
            chanlun_data['current_bi_strength'] = last_bi['strength']
            
            # 笔的幅度
            bi_range = abs(last_bi['price'] - second_last_bi['price']) / second_last_bi['price']
            chanlun_data['current_bi_range'] = bi_range
            
            # 当前价格相对笔的位置（使用前一日收盘价）
            reference_price = closes[-2] if len(closes) >= 2 else closes[-1]
            if last_bi['type'] == 'bottom':
                chanlun_data['price_vs_bi_low'] = (reference_price - last_bi['price']) / last_bi['price']
                chanlun_data['price_vs_bi_high'] = (reference_price - second_last_bi['price']) / second_last_bi['price']
            else:
                chanlun_data['price_vs_bi_high'] = (reference_price - last_bi['price']) / last_bi['price']
                chanlun_data['price_vs_bi_low'] = (reference_price - second_last_bi['price']) / second_last_bi['price']
        else:
            chanlun_data['current_bi_type'] = 'none'
            chanlun_data['current_bi_price'] = closes[-1]
            chanlun_data['current_bi_strength'] = 0
            chanlun_data['current_bi_range'] = 0
            chanlun_data['price_vs_bi_low'] = 0
            chanlun_data['price_vs_bi_high'] = 0
        
        # 背驰分析（简化）
        if len(bi_points) >= 3:
            # 检查是否存在背驰
            recent_bis = bi_points[-3:]
            if all(bi['type'] == recent_bis[0]['type'] for bi in recent_bis):
                # 同向笔，检查背驰
                if recent_bis[0]['type'] == 'bottom':
                    # 底背驰：价格新低但力度减弱
                    price_trend = recent_bis[-1]['price'] < recent_bis[0]['price']
                    strength_trend = recent_bis[-1]['strength'] < recent_bis[0]['strength']
                    chanlun_data['has_bottom_divergence'] = price_trend and strength_trend
                else:
                    # 顶背驰：价格新高但力度减弱
                    price_trend = recent_bis[-1]['price'] > recent_bis[0]['price']
                    strength_trend = recent_bis[-1]['strength'] < recent_bis[0]['strength']
                    chanlun_data['has_top_divergence'] = price_trend and strength_trend
            else:
                chanlun_data['has_bottom_divergence'] = False
                chanlun_data['has_top_divergence'] = False
        else:
            chanlun_data['has_bottom_divergence'] = False
            chanlun_data['has_top_divergence'] = False
        
        return chanlun_data
    
    def _detect_1buy_signals(self, klines, technical_indicators, chanlun_data):
        """检测一买信号"""
        if len(klines) < 30:
            return []

        # 使用前一日收盘价作为信号价格（开盘时已知）
        signal_price = klines[-2]['close'] if len(klines) >= 2 else klines[-1]['close']
        signals = []
        
        # 一买信号条件
        conditions = []
        
        # 1. 存在底分型
        if chanlun_data.get('last_fractal_type') == 'bottom':
            conditions.append('bottom_fractal')
        
        # 2. RSI超卖
        if technical_indicators.get('rsi_14', 50) < 35:
            conditions.append('rsi_oversold')
        
        # 3. 价格在低位
        if technical_indicators.get('price_position_20', 0.5) < 0.3:
            conditions.append('price_low_position')
        
        # 4. 成交量放大
        if technical_indicators.get('volume_ratio', 1) > 1.2:
            conditions.append('volume_increase')
        
        # 5. 底背驰
        if chanlun_data.get('has_bottom_divergence', False):
            conditions.append('bottom_divergence')
        
        # 6. 价格接近笔低点
        if abs(chanlun_data.get('price_vs_bi_low', 0)) < 0.1:
            conditions.append('near_bi_low')
        
        # 根据满足条件数量判断信号强度
        condition_count = len(conditions)
        
        if condition_count >= 2:  # 至少满足2个条件
            signal_strength = min(condition_count / 6.0, 1.0)  # 最多6个条件
            
            # 计算止损止盈
            bi_low = chanlun_data.get('current_bi_price', signal_price * 0.95)
            if chanlun_data.get('current_bi_type') != 'bottom':
                bi_low = signal_price * 0.95  # 默认5%止损

            stop_loss = bi_low
            risk_amount = signal_price - stop_loss
            take_profit = signal_price + risk_amount * 2.0  # 2:1风险收益比

            signal = {
                'type': '1buy',
                'price': signal_price,
                'strength': signal_strength,
                'conditions': conditions,
                'condition_count': condition_count,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_amount': risk_amount,
                'risk_reward_ratio': 2.0,
                'bi_low': bi_low
            }
            
            signals.append(signal)
        
        return signals

    def _create_complete_record(self, code, date, kline, technical_indicators, chanlun_data, signal, event_type):
        """创建完整的数据记录"""
        record = {
            # 基础信息
            'code': code,
            'date': date.strftime('%Y-%m-%d'),
            'datetime': date.strftime('%Y-%m-%d %H:%M:%S'),
            'year': date.year,
            'month': date.month,
            'quarter': (date.month - 1) // 3 + 1,
            'weekday': date.weekday(),

            # 事件信息
            'event_type': event_type,  # appeared, disappeared, confirmed
            'signal_type': signal['type'],
            'signal_strength': signal['strength'],
            'condition_count': signal['condition_count'],
            'conditions': '|'.join(signal['conditions']),

            # 价格数据
            'open': kline['open'],
            'high': kline['high'],
            'low': kline['low'],
            'close': kline['close'],
            'volume': kline['volume'],
            'amount': kline['amount'],
            'prev_close': kline['prev_close'],
            'signal_price': signal['price'],

            # 止损止盈（交易执行信息，不用于预测）
            'stop_loss': signal['stop_loss'],
            'take_profit': signal['take_profit'],
            'risk_amount': signal['risk_amount'],
            'risk_reward_ratio': signal['risk_reward_ratio'],
            'bi_low': signal['bi_low'],
        }

        # 技术指标数据
        for key, value in technical_indicators.items():
            record[f'tech_{key}'] = value

        # 缠论数据
        for key, value in chanlun_data.items():
            record[f'cl_{key}'] = value

        # 市场环境数据
        market_data = self._get_market_environment(date)
        for key, value in market_data.items():
            record[f'market_{key}'] = value

        return record

    def _get_market_environment(self, date):
        """获取市场环境数据"""
        year = date.year
        month = date.month

        # 根据年份模拟市场环境
        if year == 2021:
            sentiment = np.random.uniform(0.6, 0.9)  # 牛市
            volatility = np.random.uniform(0.3, 0.6)
        elif year == 2022:
            sentiment = np.random.uniform(0.1, 0.4)  # 熊市
            volatility = np.random.uniform(0.7, 1.0)
        elif year == 2023:
            sentiment = np.random.uniform(0.3, 0.7)  # 震荡市
            volatility = np.random.uniform(0.5, 0.8)
        else:
            sentiment = np.random.uniform(0.4, 0.6)  # 平稳市
            volatility = np.random.uniform(0.4, 0.6)

        return {
            'sentiment': sentiment,
            'volatility': volatility,
            'is_bull_market': 1 if year == 2021 else 0,
            'is_bear_market': 1 if year == 2022 else 0,
            'is_volatile_market': 1 if year in [2022, 2023] else 0,
            'is_year_end': 1 if month in [11, 12] else 0,
            'is_year_start': 1 if month in [1, 2] else 0,
            'season_spring': 1 if month in [3, 4, 5] else 0,
            'season_summer': 1 if month in [6, 7, 8] else 0,
            'season_autumn': 1 if month in [9, 10, 11] else 0,
            'season_winter': 1 if month in [12, 1, 2] else 0,
        }

    def run_complete_data_collection(self, start_date='2021-01-01', end_date='2021-06-30'):
        """运行完整数据收集"""
        print("🚀 完整数据收集器 - 记录所有缠论、技术指标、价格、成交量数据")
        print("=" * 70)
        print(f"📅 收集期间: {start_date} 到 {end_date}")
        print(f"📊 股票池: {len(self.stock_pool)} 只股票")
        print(f"🎯 目标: 收集完整的机器学习训练数据")
        print("=" * 70)

        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        current_date = start_dt
        trading_days = 0
        total_records = 0

        # 存储前一日的信号状态
        prev_day_signals = {code: [] for code in self.stock_pool}

        while current_date <= end_dt:
            if current_date.weekday() < 5:  # 交易日
                trading_days += 1

                if trading_days % 20 == 0:
                    print(f"📅 {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"📊 已收集记录: {total_records} 条")

                # 处理每只股票
                for code in self.stock_pool:
                    # 初始化历史数据
                    if code not in self.stock_kline_history:
                        self.stock_kline_history[code] = []
                        base_price = np.random.uniform(15, 45)

                        # 生成60天历史数据
                        for i in range(60):
                            hist_date = current_date - timedelta(days=60-i)
                            if hist_date.weekday() < 5:
                                hist_kline = self._generate_single_kline(code, hist_date, base_price)
                                self.stock_kline_history[code].append(hist_kline)
                                base_price = hist_kline['close']

                    # 生成当日K线
                    prev_close = self.stock_kline_history[code][-1]['close']
                    today_kline = self._generate_single_kline(code, current_date, prev_close)
                    self.stock_kline_history[code].append(today_kline)

                    # 保持最近100根K线
                    if len(self.stock_kline_history[code]) > 100:
                        self.stock_kline_history[code] = self.stock_kline_history[code][-100:]

                    # 计算技术指标
                    technical_indicators = self._calculate_technical_indicators(self.stock_kline_history[code])

                    # 分析缠论结构
                    chanlun_data = self._analyze_chanlun_structure(self.stock_kline_history[code])

                    # 检测一买信号
                    current_signals = self._detect_1buy_signals(
                        self.stock_kline_history[code], technical_indicators, chanlun_data
                    )

                    # 检查信号持续性
                    prev_signals = prev_day_signals[code]

                    # 处理信号事件
                    for signal in current_signals:
                        # 检查是否是新信号
                        is_new = True
                        for prev_signal in prev_signals:
                            if abs(signal['price'] - prev_signal['price']) < signal['price'] * 0.03:
                                is_new = False
                                break

                        if is_new:
                            # 新出现的信号
                            record = self._create_complete_record(
                                code, current_date, today_kline, technical_indicators,
                                chanlun_data, signal, 'appeared'
                            )
                            self.complete_signal_records.append(record)
                            total_records += 1
                        else:
                            # 持续的信号，检查是否确认
                            if signal['strength'] > 0.6:
                                record = self._create_complete_record(
                                    code, current_date, today_kline, technical_indicators,
                                    chanlun_data, signal, 'confirmed'
                                )
                                self.complete_signal_records.append(record)
                                total_records += 1

                    # 检查消失的信号
                    for prev_signal in prev_signals:
                        still_exists = False
                        for curr_signal in current_signals:
                            if abs(curr_signal['price'] - prev_signal['price']) < prev_signal['price'] * 0.03:
                                still_exists = True
                                break

                        if not still_exists:
                            # 信号消失
                            record = self._create_complete_record(
                                code, current_date, today_kline, technical_indicators,
                                chanlun_data, prev_signal, 'disappeared'
                            )
                            self.complete_signal_records.append(record)
                            total_records += 1

                    # 更新前一日信号状态
                    prev_day_signals[code] = current_signals

            current_date += timedelta(days=1)

        print("\n" + "=" * 70)
        print(f"✅ 完整数据收集完成!")
        print(f"📊 总交易日: {trading_days} 天")
        print(f"📊 总记录数: {total_records} 条")

        # 保存完整数据
        self._save_complete_data()

        return {
            'trading_days': trading_days,
            'total_records': total_records
        }

    def _save_complete_data(self):
        """保存完整数据"""
        if not self.complete_signal_records:
            print("❌ 没有数据记录")
            return

        try:
            df = pd.DataFrame(self.complete_signal_records)

            # 保存到data目录
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = os.path.join(data_dir, f'complete_1buy_signals_{timestamp}.csv')
            parquet_filename = os.path.join(data_dir, f'complete_1buy_signals_{timestamp}.parquet')

            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            df.to_parquet(parquet_filename, index=False)

            print(f"\n✅ 完整数据已保存:")
            print(f"  CSV文件: {csv_filename}")
            print(f"  Parquet文件: {parquet_filename}")
            print(f"  总记录数: {len(df)} 条")
            print(f"  总字段数: {len(df.columns)} 个")

            # 数据质量报告
            self._generate_data_quality_report(df)

        except Exception as e:
            print(f"❌ 保存失败: {e}")

    def _generate_data_quality_report(self, df):
        """生成数据质量报告"""
        print(f"\n📊 数据质量报告:")
        print(f"=" * 50)

        # 事件类型分布
        event_counts = df['event_type'].value_counts()
        print(f"事件类型分布:")
        for event_type, count in event_counts.items():
            print(f"  {event_type}: {count} 条 ({count/len(df)*100:.1f}%)")

        # 字段分类统计
        basic_fields = [col for col in df.columns if not col.startswith(('tech_', 'cl_', 'market_'))]
        tech_fields = [col for col in df.columns if col.startswith('tech_')]
        cl_fields = [col for col in df.columns if col.startswith('cl_')]
        market_fields = [col for col in df.columns if col.startswith('market_')]

        print(f"\n字段分类:")
        print(f"  基础字段: {len(basic_fields)} 个")
        print(f"  技术指标: {len(tech_fields)} 个")
        print(f"  缠论数据: {len(cl_fields)} 个")
        print(f"  市场环境: {len(market_fields)} 个")
        print(f"  总字段数: {len(df.columns)} 个")

        # 信号强度分布
        if 'signal_strength' in df.columns:
            strength_stats = df['signal_strength'].describe()
            print(f"\n信号强度统计:")
            print(f"  平均强度: {strength_stats['mean']:.3f}")
            print(f"  强度范围: {strength_stats['min']:.3f} - {strength_stats['max']:.3f}")

            strong_signals = len(df[df['signal_strength'] > 0.6])
            print(f"  强信号(>0.6): {strong_signals} 条 ({strong_signals/len(df)*100:.1f}%)")

def main():
    """主函数"""
    collector = CompleteDataCollector()
    results = collector.run_complete_data_collection(
        start_date="2021-01-01",
        end_date="2021-03-31"  # 先收集3个月的完整数据
    )

    print(f"\n🎉 完整数据收集完成！")
    print(f"📊 现在拥有包含所有缠论、技术指标、价格、成交量的完整数据")
    print(f"🤖 可以用于训练高质量的机器学习模型")

if __name__ == "__main__":
    main()
