#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查 D:\git\czsc-new\stock_data 目录下的股票数据
为V2版本机器学习策略分析做准备
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob
from pathlib import Path

def check_czsc_stock_data():
    """检查CZSC股票数据目录"""
    print("🔍 检查CZSC股票数据目录")
    print("=" * 60)
    
    # 数据目录路径
    data_path = Path(r"D:\git\czsc-new\stock_data")
    
    if not data_path.exists():
        print(f"❌ 数据目录不存在: {data_path}")
        print("💡 请确认路径是否正确")
        return
    
    print(f"✅ 数据目录存在: {data_path}")
    
    # 检查目录结构
    print(f"\n📁 目录结构:")
    subdirs = [d for d in data_path.iterdir() if d.is_dir()]
    files = [f for f in data_path.iterdir() if f.is_file()]
    
    print(f"   子目录数量: {len(subdirs)}")
    print(f"   文件数量: {len(files)}")
    
    if subdirs:
        print(f"   前10个子目录:")
        for i, subdir in enumerate(subdirs[:10]):
            print(f"      {i+1}. {subdir.name}")
        if len(subdirs) > 10:
            print(f"      ... 还有 {len(subdirs)-10} 个子目录")
    
    if files:
        print(f"   前10个文件:")
        for i, file in enumerate(files[:10]):
            print(f"      {i+1}. {file.name}")
        if len(files) > 10:
            print(f"      ... 还有 {len(files)-10} 个文件")
    
    # 查找数据文件
    print(f"\n🔍 查找数据文件...")
    
    # 常见的数据文件格式
    data_patterns = [
        "*.csv", "*.parquet", "*.pkl", "*.h5", "*.hdf5",
        "**/*.csv", "**/*.parquet", "**/*.pkl", "**/*.h5", "**/*.hdf5"
    ]
    
    all_data_files = []
    for pattern in data_patterns:
        files = list(data_path.glob(pattern))
        all_data_files.extend(files)
    
    # 去重
    all_data_files = list(set(all_data_files))
    
    print(f"📊 找到 {len(all_data_files)} 个数据文件")
    
    if len(all_data_files) == 0:
        print("❌ 没有找到数据文件")
        return
    
    # 按文件类型分类
    file_types = {}
    for file in all_data_files:
        ext = file.suffix.lower()
        if ext not in file_types:
            file_types[ext] = []
        file_types[ext].append(file)
    
    print(f"\n📋 文件类型分布:")
    for ext, files in file_types.items():
        print(f"   {ext}: {len(files)} 个文件")
    
    # 分析数据文件
    print(f"\n📊 数据文件分析:")
    
    sample_files = all_data_files[:20]  # 分析前20个文件
    
    daily_data_info = []
    minute_data_info = []
    
    for file_path in sample_files:
        try:
            print(f"\n🔍 分析文件: {file_path.name}")
            
            # 根据文件扩展名读取数据
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, nrows=1000)  # 只读取前1000行进行分析
            elif file_path.suffix.lower() == '.parquet':
                df = pd.read_parquet(file_path)
                if len(df) > 1000:
                    df = df.head(1000)
            elif file_path.suffix.lower() == '.pkl':
                df = pd.read_pickle(file_path)
                if len(df) > 1000:
                    df = df.head(1000)
            else:
                print(f"   ⚠️  不支持的文件格式: {file_path.suffix}")
                continue
            
            print(f"   📊 数据形状: {df.shape}")
            print(f"   📋 列名: {list(df.columns)}")
            
            # 检查是否包含时间列
            time_columns = []
            for col in df.columns:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in ['date', 'time', 'dt', 'timestamp']):
                    time_columns.append(col)
            
            if time_columns:
                print(f"   🕐 时间列: {time_columns}")
                
                # 尝试解析时间
                for time_col in time_columns:
                    try:
                        if df[time_col].dtype == 'object':
                            # 尝试转换为datetime
                            time_series = pd.to_datetime(df[time_col])
                        else:
                            time_series = df[time_col]
                        
                        if len(time_series.dropna()) > 0:
                            earliest = time_series.min()
                            latest = time_series.max()
                            print(f"      {time_col}: {earliest} ~ {latest}")
                            
                            # 判断是日线还是分钟线数据
                            time_diff = time_series.diff().dropna()
                            if len(time_diff) > 0:
                                avg_diff = time_diff.mean()
                                if avg_diff >= pd.Timedelta(days=1):
                                    data_type = "日线"
                                    daily_data_info.append({
                                        'file': file_path.name,
                                        'earliest': earliest,
                                        'latest': latest,
                                        'records': len(df)
                                    })
                                else:
                                    data_type = "分钟线"
                                    minute_data_info.append({
                                        'file': file_path.name,
                                        'earliest': earliest,
                                        'latest': latest,
                                        'records': len(df)
                                    })
                                
                                print(f"      数据类型: {data_type}")
                    except Exception as e:
                        print(f"      ⚠️  时间解析失败: {e}")
            else:
                print(f"   ⚠️  没有找到时间列")
            
        except Exception as e:
            print(f"   ❌ 文件读取失败: {e}")
    
    # 汇总分析结果
    print(f"\n" + "="*60)
    print(f"📊 数据汇总分析")
    print(f"="*60)
    
    if daily_data_info:
        print(f"\n📈 日线数据文件 ({len(daily_data_info)} 个):")
        earliest_daily = min([info['earliest'] for info in daily_data_info])
        latest_daily = max([info['latest'] for info in daily_data_info])
        total_daily_records = sum([info['records'] for info in daily_data_info])
        
        print(f"   📅 时间范围: {earliest_daily} ~ {latest_daily}")
        print(f"   📊 总记录数: {total_daily_records:,} 条")
        print(f"   📈 数据年限: {(latest_daily - earliest_daily).days / 365.25:.1f} 年")
        
        print(f"   📋 详细信息:")
        for info in daily_data_info[:5]:  # 显示前5个
            print(f"      {info['file']}: {info['records']:,} 条 ({info['earliest']} ~ {info['latest']})")
        if len(daily_data_info) > 5:
            print(f"      ... 还有 {len(daily_data_info)-5} 个文件")
    else:
        print(f"\n❌ 没有找到日线数据文件")
    
    if minute_data_info:
        print(f"\n📈 分钟线数据文件 ({len(minute_data_info)} 个):")
        earliest_minute = min([info['earliest'] for info in minute_data_info])
        latest_minute = max([info['latest'] for info in minute_data_info])
        total_minute_records = sum([info['records'] for info in minute_data_info])
        
        print(f"   📅 时间范围: {earliest_minute} ~ {latest_minute}")
        print(f"   📊 总记录数: {total_minute_records:,} 条")
        print(f"   📈 数据年限: {(latest_minute - earliest_minute).days / 365.25:.1f} 年")
        
        print(f"   📋 详细信息:")
        for info in minute_data_info[:5]:  # 显示前5个
            print(f"      {info['file']}: {info['records']:,} 条 ({info['earliest']} ~ {info['latest']})")
        if len(minute_data_info) > 5:
            print(f"      ... 还有 {len(minute_data_info)-5} 个文件")
    else:
        print(f"\n❌ 没有找到分钟线数据文件")
    
    # V2版本建议
    print(f"\n" + "="*60)
    print(f"🎯 V2版本数据收集建议")
    print(f"="*60)
    
    if daily_data_info and minute_data_info:
        # 计算可用的时间范围
        start_date = max(earliest_daily, earliest_minute)
        end_date = min(latest_daily, latest_minute)
        available_years = (end_date - start_date).days / 365.25
        
        print(f"✅ 日线和分钟线数据都可用")
        print(f"📅 推荐回测时间范围: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}")
        print(f"📈 可用数据年限: {available_years:.1f} 年")
        
        if available_years >= 3:
            print(f"✅ 数据充足，可以进行大规模机器学习训练")
            target_samples = int(available_years * 1000)  # 每年目标1000个样本
            print(f"🎯 预计可收集样本数: {target_samples:,} 条")
        else:
            print(f"⚠️  数据年限较短，建议扩展数据范围或降低样本要求")
    
    elif daily_data_info:
        print(f"⚠️  只有日线数据，缺少分钟线数据")
        print(f"💡 建议: 寻找分钟线数据源或使用日线数据进行分析")
    
    elif minute_data_info:
        print(f"⚠️  只有分钟线数据，缺少日线数据")
        print(f"💡 建议: 从分钟线数据合成日线数据")
    
    else:
        print(f"❌ 没有找到可用的股票数据")
        print(f"💡 建议: 检查数据目录或重新下载数据")

if __name__ == "__main__":
    check_czsc_stock_data()
