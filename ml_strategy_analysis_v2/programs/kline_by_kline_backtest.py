#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
逐K线回测引擎 - 真实模拟一买信号的动态特性
每个K线收盘后重新计算缠论结构，捕捉信号的出现和消失
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 获取当前文件的目录，然后构建相对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # 回到项目根目录
chanlun_src_path = os.path.join(project_root, 'chanlun-pro', 'src')
sys.path.append(chanlun_src_path)

class KlineByKlineBacktest:
    """逐K线回测引擎"""
    
    def __init__(self):
        self.initial_capital = 1000000
        self.max_positions = 10
        self.position_size = 100000
        self.risk_reward_ratio = 2.0
        
        # 股票池
        self.stock_pool = self._generate_stock_pool()
        
        # 存储每只股票的K线历史和缠论状态
        self.stock_kline_history = {}  # {code: [klines]}
        self.stock_chanlun_state = {}  # {code: chanlun_analysis}
        
        # 信号记录
        self.signal_history = []  # 记录所有出现过的信号（包括后来消失的）
        self.confirmed_signals = []  # 最终确认的信号
        self.ml_features = []
        
        # 当前日期的所有股票信号
        self.daily_signals = {}
    
    def _generate_stock_pool(self):
        """生成股票池"""
        return [f"{i:06d}" for i in range(1, 101)]  # 100只股票用于测试
    
    def _simulate_daily_kline(self, code: str, date: datetime):
        """模拟生成单只股票的日K线数据"""
        # 如果是第一次，初始化历史数据
        if code not in self.stock_kline_history:
            # 生成前60天的历史K线（用于计算技术指标）
            self.stock_kline_history[code] = []
            base_price = np.random.uniform(10, 50)
            
            for i in range(60):
                hist_date = date - timedelta(days=60-i)
                if hist_date.weekday() < 5:  # 只生成交易日
                    kline = self._generate_single_kline(code, hist_date, base_price)
                    self.stock_kline_history[code].append(kline)
                    base_price = kline['close']
        
        # 生成当日K线
        prev_close = self.stock_kline_history[code][-1]['close'] if self.stock_kline_history[code] else 20.0
        today_kline = self._generate_single_kline(code, date, prev_close)
        
        return today_kline
    
    def _generate_single_kline(self, code: str, date: datetime, prev_close: float):
        """生成单根K线"""
        # 模拟价格波动
        daily_return = np.random.normal(0, 0.03)  # 3%日波动率
        
        open_price = prev_close * (1 + np.random.uniform(-0.02, 0.02))
        close_price = prev_close * (1 + daily_return)
        
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.03))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.03))
        
        volume = np.random.lognormal(15, 1)
        
        return {
            'code': code,
            'date': date,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': int(volume),
            'prev_close': prev_close
        }
    
    def _update_chanlun_analysis(self, code: str, new_kline: dict):
        """更新缠论分析（模拟真实的缠论计算）"""
        # 添加新K线到历史
        self.stock_kline_history[code].append(new_kline)
        
        # 保持最近200根K线（节省内存）
        if len(self.stock_kline_history[code]) > 200:
            self.stock_kline_history[code] = self.stock_kline_history[code][-200:]
        
        # 模拟缠论分析
        klines = self.stock_kline_history[code]
        
        if len(klines) < 30:  # 至少需要30根K线
            return None
        
        # 模拟笔的识别
        bi_analysis = self._simulate_bi_analysis(klines)
        
        # 模拟一买信号识别
        buy_signals = self._simulate_1buy_detection(klines, bi_analysis)
        
        chanlun_state = {
            'code': code,
            'date': new_kline['date'],
            'klines_count': len(klines),
            'bi_analysis': bi_analysis,
            'buy_signals': buy_signals,
            'current_price': new_kline['close']
        }
        
        self.stock_chanlun_state[code] = chanlun_state
        return chanlun_state
    
    def _simulate_bi_analysis(self, klines):
        """模拟笔的分析"""
        if len(klines) < 10:
            return None
        
        recent_klines = klines[-30:]  # 分析最近30根K线
        
        # 模拟寻找笔的高低点
        highs = [k['high'] for k in recent_klines]
        lows = [k['low'] for k in recent_klines]
        
        # 简化的笔识别：寻找局部极值
        bi_points = []
        
        for i in range(2, len(recent_klines)-2):
            # 检查是否是局部高点
            if (recent_klines[i]['high'] > recent_klines[i-1]['high'] and 
                recent_klines[i]['high'] > recent_klines[i-2]['high'] and
                recent_klines[i]['high'] > recent_klines[i+1]['high'] and
                recent_klines[i]['high'] > recent_klines[i+2]['high']):
                bi_points.append({
                    'type': 'high',
                    'price': recent_klines[i]['high'],
                    'date': recent_klines[i]['date'],
                    'index': i
                })
            
            # 检查是否是局部低点
            elif (recent_klines[i]['low'] < recent_klines[i-1]['low'] and 
                  recent_klines[i]['low'] < recent_klines[i-2]['low'] and
                  recent_klines[i]['low'] < recent_klines[i+1]['low'] and
                  recent_klines[i]['low'] < recent_klines[i+2]['low']):
                bi_points.append({
                    'type': 'low',
                    'price': recent_klines[i]['low'],
                    'date': recent_klines[i]['date'],
                    'index': i
                })
        
        # 安全地计算最后的高低点
        low_points = [bp['price'] for bp in bi_points if bp['type'] == 'low']
        high_points = [bp['price'] for bp in bi_points if bp['type'] == 'high']

        return {
            'bi_points': bi_points,
            'last_bi_low': min(low_points) if low_points else None,
            'last_bi_high': max(high_points) if high_points else None
        }
    
    def _simulate_1buy_detection(self, klines, bi_analysis):
        """模拟一买信号检测"""
        if not bi_analysis or not bi_analysis['bi_points']:
            return []
        
        current_price = klines[-1]['close']
        bi_points = bi_analysis['bi_points']
        
        # 寻找最近的下跌笔
        recent_lows = [bp for bp in bi_points if bp['type'] == 'low']
        if not recent_lows:
            return []
        
        last_low = recent_lows[-1]
        
        # 一买信号条件（简化版）：
        # 1. 存在明显的下跌笔
        # 2. 当前价格在笔低点附近
        # 3. 出现底分型确认
        
        signals = []
        
        # 检查是否满足一买条件
        if (current_price > last_low['price'] * 0.95 and  # 在笔低点附近
            current_price < last_low['price'] * 1.1 and   # 不能离得太远
            len(recent_lows) >= 1):  # 至少有一个低点
            
            # 模拟信号强度（会影响后续是否消失）
            signal_strength = np.random.uniform(0.3, 1.0)
            
            # 信号可能消失的概率（强度越低越容易消失）
            disappear_prob = 1 - signal_strength
            
            signal = {
                'type': '1buy',
                'price': current_price,
                'bi_low': last_low['price'],
                'bi_low_date': last_low['date'],
                'strength': signal_strength,
                'disappear_prob': disappear_prob,
                'confirmed': signal_strength > 0.6,  # 强度>0.6才算确认
                'stop_loss': last_low['price'],
                'take_profit': current_price + (current_price - last_low['price']) * self.risk_reward_ratio
            }
            
            signals.append(signal)
        
        return signals
    
    def _check_signal_persistence(self, code: str, prev_signals: list, current_signals: list):
        """检查信号的持续性（信号是否消失）"""
        persistent_signals = []
        disappeared_signals = []
        new_signals = []
        
        # 检查之前的信号是否还存在
        for prev_signal in prev_signals:
            still_exists = False
            for curr_signal in current_signals:
                # 简单的信号匹配（实际应该更复杂）
                if (abs(curr_signal['price'] - prev_signal['price']) < prev_signal['price'] * 0.05 and
                    curr_signal['type'] == prev_signal['type']):
                    still_exists = True
                    persistent_signals.append(curr_signal)
                    break
            
            if not still_exists:
                disappeared_signals.append(prev_signal)
        
        # 检查新出现的信号
        for curr_signal in current_signals:
            is_new = True
            for prev_signal in prev_signals:
                if (abs(curr_signal['price'] - prev_signal['price']) < prev_signal['price'] * 0.05 and
                    curr_signal['type'] == prev_signal['type']):
                    is_new = False
                    break
            
            if is_new:
                new_signals.append(curr_signal)
        
        return persistent_signals, disappeared_signals, new_signals
    
    def _record_signal_event(self, code: str, date: datetime, event_type: str, signal: dict):
        """记录信号事件"""
        event = {
            'code': code,
            'date': date,
            'event_type': event_type,  # 'appeared', 'disappeared', 'confirmed'
            'signal_type': signal['type'],
            'signal_price': signal['price'],
            'signal_strength': signal['strength'],
            'bi_low': signal['bi_low'],
            'stop_loss': signal['stop_loss'],
            'take_profit': signal['take_profit']
        }
        
        self.signal_history.append(event)
    
    def run_kline_by_kline_backtest(self, start_date='2021-01-01', end_date='2021-12-31'):
        """运行逐K线回测"""
        print("🚀 逐K线回测引擎 - 真实模拟一买信号动态特性")
        print("=" * 60)
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"📊 股票池: {len(self.stock_pool)} 只股票")
        print(f"🎯 策略: 逐K线检测一买信号的出现和消失")
        print("=" * 60)
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        trading_days = 0
        total_signals_appeared = 0
        total_signals_disappeared = 0
        total_signals_confirmed = 0
        
        # 存储前一日的信号状态
        prev_day_signals = {code: [] for code in self.stock_pool}
        
        while current_date <= end_dt:
            if current_date.weekday() < 5:  # 交易日
                trading_days += 1
                
                if trading_days % 50 == 0:
                    print(f"📅 {current_date.strftime('%Y-%m-%d')} (第{trading_days}个交易日)")
                    print(f"📊 信号统计: 出现{total_signals_appeared}, 消失{total_signals_disappeared}, 确认{total_signals_confirmed}")
                
                daily_new_signals = 0
                daily_disappeared_signals = 0
                daily_confirmed_signals = 0
                
                # 处理每只股票
                for code in self.stock_pool:
                    # 生成当日K线
                    today_kline = self._simulate_daily_kline(code, current_date)
                    
                    # 更新缠论分析
                    chanlun_state = self._update_chanlun_analysis(code, today_kline)
                    
                    if chanlun_state and chanlun_state['buy_signals']:
                        current_signals = chanlun_state['buy_signals']
                        
                        # 检查信号持续性
                        persistent, disappeared, new = self._check_signal_persistence(
                            code, prev_day_signals[code], current_signals
                        )
                        
                        # 记录信号事件
                        for signal in new:
                            self._record_signal_event(code, current_date, 'appeared', signal)
                            daily_new_signals += 1
                            total_signals_appeared += 1
                        
                        for signal in disappeared:
                            self._record_signal_event(code, current_date, 'disappeared', signal)
                            daily_disappeared_signals += 1
                            total_signals_disappeared += 1
                        
                        for signal in persistent:
                            if signal['confirmed'] and not any(s['confirmed'] for s in prev_day_signals[code]):
                                self._record_signal_event(code, current_date, 'confirmed', signal)
                                daily_confirmed_signals += 1
                                total_signals_confirmed += 1
                        
                        # 更新前一日信号状态
                        prev_day_signals[code] = current_signals
                    else:
                        # 如果今天没有信号，之前的信号都消失了
                        for signal in prev_day_signals[code]:
                            self._record_signal_event(code, current_date, 'disappeared', signal)
                            daily_disappeared_signals += 1
                            total_signals_disappeared += 1
                        
                        prev_day_signals[code] = []
                
                if daily_new_signals > 0 or daily_disappeared_signals > 0:
                    print(f"   📈 {current_date.strftime('%Y-%m-%d')}: "
                          f"新增{daily_new_signals}, 消失{daily_disappeared_signals}, 确认{daily_confirmed_signals}")
            
            current_date += timedelta(days=1)
        
        print("\n" + "=" * 60)
        print(f"✅ 逐K线回测完成!")
        print(f"📊 总交易日: {trading_days} 天")
        print(f"📊 信号出现: {total_signals_appeared} 次")
        print(f"📊 信号消失: {total_signals_disappeared} 次")
        print(f"📊 信号确认: {total_signals_confirmed} 次")
        print(f"📊 信号持续率: {(total_signals_appeared - total_signals_disappeared) / total_signals_appeared * 100:.1f}%")
        
        # 分析信号动态特性
        self._analyze_signal_dynamics()
        
        # 保存数据
        self._save_signal_data()
        
        return {
            'trading_days': trading_days,
            'signals_appeared': total_signals_appeared,
            'signals_disappeared': total_signals_disappeared,
            'signals_confirmed': total_signals_confirmed
        }
    
    def _analyze_signal_dynamics(self):
        """分析信号的动态特性"""
        if not self.signal_history:
            return
        
        df = pd.DataFrame(self.signal_history)
        
        print(f"\n📊 信号动态特性分析:")
        
        # 按事件类型统计
        event_counts = df['event_type'].value_counts()
        print(f"  信号出现: {event_counts.get('appeared', 0)} 次")
        print(f"  信号消失: {event_counts.get('disappeared', 0)} 次")
        print(f"  信号确认: {event_counts.get('confirmed', 0)} 次")
        
        # 分析信号强度与持续性的关系
        appeared_signals = df[df['event_type'] == 'appeared']
        if len(appeared_signals) > 0:
            avg_strength = appeared_signals['signal_strength'].mean()
            print(f"  平均信号强度: {avg_strength:.3f}")
            
            # 强信号 vs 弱信号的持续率
            strong_signals = appeared_signals[appeared_signals['signal_strength'] > 0.6]
            weak_signals = appeared_signals[appeared_signals['signal_strength'] <= 0.6]
            
            print(f"  强信号(>0.6): {len(strong_signals)} 个")
            print(f"  弱信号(≤0.6): {len(weak_signals)} 个")
    
    def _save_signal_data(self):
        """保存信号数据"""
        if not self.signal_history:
            return
        
        try:
            df = pd.DataFrame(self.signal_history)
            
            # 保存到data目录
            data_dir = os.path.join(os.path.dirname(current_dir), 'data')
            os.makedirs(data_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(data_dir, f'kline_by_kline_signals_{timestamp}.csv')
            
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"\n✅ 信号数据已保存: {filename}")
            print(f"📊 总记录数: {len(df)} 条")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")

def main():
    """主函数"""
    backtest = KlineByKlineBacktest()
    results = backtest.run_kline_by_kline_backtest(
        start_date="2021-01-01",
        end_date="2021-03-31"  # 先测试3个月
    )
    
    print(f"\n🎉 逐K线回测完成！")
    print(f"📊 这种方法能真实捕捉一买信号的动态特性")

if __name__ == "__main__":
    main()
