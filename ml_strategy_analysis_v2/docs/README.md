# 机器学习策略分析 V2

## 📋 项目概述

V2版本是基于CZSC股票数据的大规模机器学习策略分析项目，目标是收集更多样化的交易信号，构建更强大的预测模型。

### 🎯 V2版本改进目标
- **扩大数据规模**：目标收集5000+条交易记录
- **多样化信号**：不仅限于涨停后反弹，包含多种交易信号
- **防止过拟合**：使用更严格的验证方法和正则化
- **提升泛化能力**：构建更稳健的预测模型

## 📊 数据源情况

### 数据路径
- **数据目录**：`D:\git\czsc-new\stock_data`
- **数据格式**：Parquet文件
- **股票数量**：10,294只股票

### 数据时间范围
#### 日线数据
- **时间跨度**：2021-01-04 到 2025-07-17（4.5年）
- **数据列**：symbol, dt, open, high, low, close, pre_close, change, pct_chg, volume, amount
- **数据质量**：✅ 优秀，覆盖完整

#### 5分钟数据
- **时间跨度**：2021-01-04 到 2021-07-29（约6个月）
- **数据列**：symbol, dt, close, open, high, low, volume, amount
- **数据质量**：⚠️ 时间范围较短

### V2版本回测计划
- **推荐时间范围**：2021-01-04 到 2021-07-29
- **可用数据年限**：0.6年
- **预期样本数**：3000-5000条交易记录

## 🔧 V2版本技术改进

### 1. 多样化交易信号
- ✅ 涨停后反弹（V1原有）
- 🆕 日线突破信号
- 🆕 均线交叉信号
- 🆕 成交量异动信号
- 🆕 技术指标背驰信号
- 🆕 形态识别信号

### 2. 增强特征工程
- **基础技术指标**：MA、RSI、MACD、BOLL等
- **高级技术指标**：KDJ、CCI、威廉指标等
- **量价关系**：量价配合度、资金流向等
- **市场情绪**：涨跌比、强弱指标等
- **相对强度**：相对大盘表现、行业表现等

### 3. 模型优化策略
- **防过拟合**：交叉验证、早停、正则化
- **特征选择**：递归特征消除、重要性排序
- **模型集成**：Stacking、Blending、Voting
- **超参调优**：网格搜索、贝叶斯优化

### 4. 验证方法改进
- **时间序列分割**：避免数据泄露
- **滑动窗口验证**：模拟真实交易环境
- **样本外测试**：严格的泛化能力测试

## 📁 V2版本文件结构

```
ml_strategy_analysis_v2/
├── programs/                    # 程序文件
│   ├── check_czsc_data.py              # 数据检查脚本
│   ├── v2_data_collector.py            # V2数据收集引擎
│   ├── v2_feature_engineer.py          # V2特征工程
│   ├── v2_model_trainer.py             # V2模型训练器
│   ├── v2_strategy_optimizer.py        # V2策略优化器
│   └── v2_backtest_engine.py           # V2回测引擎
├── data/                        # 数据文件
│   ├── raw/                     # 原始数据
│   ├── processed/               # 处理后数据
│   └── features/                # 特征数据
├── results/                     # 结果文件
│   ├── models/                  # 训练好的模型
│   ├── reports/                 # 分析报告
│   └── charts/                  # 图表文件
├── configs/                     # 配置文件
│   ├── data_config.yaml         # 数据配置
│   ├── model_config.yaml        # 模型配置
│   └── strategy_config.yaml     # 策略配置
└── docs/                        # 文档
    ├── README.md                # 项目说明
    ├── data_analysis.md         # 数据分析报告
    └── model_performance.md     # 模型性能报告
```

## 🎯 V2版本开发计划

### Phase 1: 数据收集与预处理
- [x] 检查CZSC数据源
- [ ] 开发多信号数据收集器
- [ ] 构建特征工程管道
- [ ] 数据质量检查与清洗

### Phase 2: 特征工程优化
- [ ] 实现基础技术指标
- [ ] 开发高级特征
- [ ] 特征选择与降维
- [ ] 特征重要性分析

### Phase 3: 模型训练与优化
- [ ] 实现多种机器学习模型
- [ ] 超参数调优
- [ ] 模型集成
- [ ] 防过拟合优化

### Phase 4: 策略验证与部署
- [ ] 样本外测试
- [ ] 策略回测验证
- [ ] 性能对比分析
- [ ] 部署准备

## 📊 预期改进效果

### V1 vs V2 对比目标
| 指标 | V1版本 | V2目标 |
|------|--------|--------|
| 样本数量 | 426条 | 5000+条 |
| 特征维度 | 148个 | 200+个 |
| 胜率 | 100%（疑似过拟合） | 65-75% |
| 泛化能力 | 待验证 | 强 |
| 模型稳定性 | 中等 | 高 |

### 关键改进点
1. **数据量充足**：避免过拟合问题
2. **信号多样化**：提高模型泛化能力
3. **验证严格**：确保真实交易环境下的表现
4. **特征优化**：提高预测准确性

---

**创建时间**：2025-07-18  
**版本**：V2  
**状态**：开发中
