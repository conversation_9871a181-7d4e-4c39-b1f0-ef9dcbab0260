#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库中日线和5分钟数据的时间范围
为V2版本数据收集做准备
"""

import sys
sys.path.append(r'D:\git\chan-pro\chanlun-pro\src')

from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
import pandas as pd
from datetime import datetime
import chanlun.config as config

def check_data_time_range():
    """检查数据时间范围"""
    print("🔍 检查数据库中日线和5分钟数据的时间范围")
    print("=" * 60)
    
    try:
        # 尝试连接NAS数据库，如果失败则尝试本地数据库
        try:
            nas_url = (
                f"mysql+pymysql://{config.NAS_DB_USER}:{quote_plus(config.NAS_DB_PWD)}"
                f"@{config.NAS_DB_HOST}:{config.NAS_DB_PORT}/{config.NAS_DB_DATABASE}"
                f"?charset=utf8mb4"
            )
            engine = create_engine(nas_url, echo=False, connect_args={'connect_timeout': 5})
            # 测试连接
            with engine.connect() as test_conn:
                test_conn.execute(text("SELECT 1"))
            print("✅ 使用NAS数据库")
        except:
            print("⚠️  NAS数据库连接失败，尝试本地数据库...")
            local_url = (
                f"mysql+pymysql://{config.LOCAL_DB_USER}:{quote_plus(config.LOCAL_DB_PWD)}"
                f"@{config.LOCAL_DB_HOST}:{config.LOCAL_DB_PORT}/{config.LOCAL_DB_DATABASE}"
                f"?charset=utf8mb4"
            )
            engine = create_engine(local_url, echo=False, connect_args={'connect_timeout': 5})
            # 测试连接
            with engine.connect() as test_conn:
                test_conn.execute(text("SELECT 1"))
            print("✅ 使用本地数据库")
        
        with engine.connect() as conn:
            print("✅ 数据库连接成功")
            
            # 查找所有K线表
            result = conn.execute(text("""
                SELECT table_name, table_rows
                FROM information_schema.tables
                WHERE table_schema = :db_name
                AND table_name LIKE '%klines%'
                ORDER BY table_rows DESC
            """), {"db_name": config.NAS_DB_DATABASE})
            
            tables = result.fetchall()
            
            if not tables:
                print("❌ 没有找到K线数据表")
                return
            
            print(f"📊 找到 {len(tables)} 个K线表:")
            for table_name, rows in tables:
                print(f"   {table_name}: {rows:,} 行")
            
            # 选择数据量最大的表进行分析
            main_table = tables[0][0]
            print(f"\n🎯 使用主表: {main_table}")
            
            # 检查日线数据时间范围
            print(f"\n📈 日线数据 (频率='d') 时间范围:")
            result = conn.execute(text(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT code) as stock_count,
                    MIN(dt) as earliest_date,
                    MAX(dt) as latest_date
                FROM {main_table}
                WHERE f = 'd'
            """))
            
            daily_stats = result.fetchone()
            if daily_stats and daily_stats[0] > 0:
                total_records, stock_count, earliest, latest = daily_stats
                print(f"   📊 总记录数: {total_records:,} 条")
                print(f"   📊 股票数量: {stock_count:,} 只")
                print(f"   📅 最早日期: {earliest}")
                print(f"   📅 最新日期: {latest}")
                
                # 计算数据年限
                if earliest and latest:
                    date_range = latest - earliest
                    years = date_range.days / 365.25
                    print(f"   📈 数据年限: {years:.1f} 年")
            else:
                print("   ❌ 没有日线数据")
            
            # 检查5分钟数据时间范围
            print(f"\n📈 5分钟数据 (频率='5m') 时间范围:")
            result = conn.execute(text(f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT code) as stock_count,
                    MIN(dt) as earliest_date,
                    MAX(dt) as latest_date
                FROM {main_table}
                WHERE f = '5m'
            """))
            
            min5_stats = result.fetchone()
            if min5_stats and min5_stats[0] > 0:
                total_records, stock_count, earliest, latest = min5_stats
                print(f"   📊 总记录数: {total_records:,} 条")
                print(f"   📊 股票数量: {stock_count:,} 只")
                print(f"   📅 最早日期: {earliest}")
                print(f"   📅 最新日期: {latest}")
                
                # 计算数据年限
                if earliest and latest:
                    date_range = latest - earliest
                    years = date_range.days / 365.25
                    print(f"   📈 数据年限: {years:.1f} 年")
            else:
                print("   ❌ 没有5分钟数据")
            
            # 检查其他频率数据
            print(f"\n📊 所有频率数据统计:")
            result = conn.execute(text(f"""
                SELECT 
                    f as frequency,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT code) as stock_count,
                    MIN(dt) as earliest_date,
                    MAX(dt) as latest_date
                FROM {main_table}
                GROUP BY f
                ORDER BY 
                    CASE f
                        WHEN 'd' THEN 1
                        WHEN '60m' THEN 2
                        WHEN '30m' THEN 3
                        WHEN '15m' THEN 4
                        WHEN '5m' THEN 5
                        WHEN '1m' THEN 6
                        ELSE 7
                    END
            """))
            
            freq_stats = result.fetchall()
            
            print(f"   {'频率':<8} {'记录数':<12} {'股票数':<8} {'最早日期':<12} {'最新日期':<12} {'年限':<8}")
            print(f"   {'-'*8} {'-'*12} {'-'*8} {'-'*12} {'-'*12} {'-'*8}")
            
            for freq, records, stocks, earliest, latest in freq_stats:
                years = 0
                if earliest and latest:
                    date_range = latest - earliest
                    years = date_range.days / 365.25
                
                print(f"   {freq:<8} {records:<12,} {stocks:<8} {earliest.strftime('%Y-%m-%d') if earliest else 'N/A':<12} {latest.strftime('%Y-%m-%d') if latest else 'N/A':<12} {years:<8.1f}")
            
            # 检查数据完整性 - 随机选择几只股票
            print(f"\n🔍 数据完整性检查 (随机选择5只股票):")
            result = conn.execute(text(f"""
                SELECT DISTINCT code
                FROM {main_table}
                WHERE f = 'd'
                ORDER BY RAND()
                LIMIT 5
            """))
            
            sample_codes = [row[0] for row in result.fetchall()]
            
            for code in sample_codes:
                # 检查该股票的日线和5分钟数据
                result = conn.execute(text(f"""
                    SELECT 
                        f,
                        COUNT(*) as count,
                        MIN(dt) as start_date,
                        MAX(dt) as end_date
                    FROM {main_table}
                    WHERE code = :code AND f IN ('d', '5m')
                    GROUP BY f
                """), {"code": code})
                
                stock_data = result.fetchall()
                
                print(f"\n   📈 {code}:")
                for freq, count, start_date, end_date in stock_data:
                    print(f"      {freq}: {count:,} 条 ({start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')})")
            
            # 总结建议
            print(f"\n" + "="*60)
            print(f"📋 V2版本数据收集建议:")
            
            if daily_stats and daily_stats[0] > 0:
                earliest_daily = daily_stats[2]
                latest_daily = daily_stats[3]
                print(f"✅ 日线数据充足: {earliest_daily} 到 {latest_daily}")
            else:
                print(f"⚠️  日线数据不足，需要先下载日线数据")
            
            if min5_stats and min5_stats[0] > 0:
                earliest_5min = min5_stats[2]
                latest_5min = min5_stats[3]
                print(f"✅ 5分钟数据充足: {earliest_5min} 到 {latest_5min}")
            else:
                print(f"⚠️  5分钟数据不足，需要先下载5分钟数据")
            
            # 推荐回测时间范围
            if daily_stats and min5_stats and daily_stats[0] > 0 and min5_stats[0] > 0:
                # 取两者的交集时间范围
                start_date = max(daily_stats[2], min5_stats[2])
                end_date = min(daily_stats[3], min5_stats[3])
                
                print(f"\n🎯 推荐V2版本回测时间范围:")
                print(f"   开始时间: {start_date}")
                print(f"   结束时间: {end_date}")
                
                # 计算可用年限
                if start_date and end_date:
                    available_years = (end_date - start_date).days / 365.25
                    print(f"   可用年限: {available_years:.1f} 年")
                    
                    if available_years >= 3:
                        print(f"   ✅ 数据充足，可以进行大规模回测")
                    else:
                        print(f"   ⚠️  数据年限较短，建议扩展数据范围")
        
        engine.dispose()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_data_time_range()
